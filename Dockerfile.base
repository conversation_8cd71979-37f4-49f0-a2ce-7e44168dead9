# syntax=docker/dockerfile:1
ARG PYTHON_VERSION=3.11
FROM python:${PYTHON_VERSION}-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install -y \
  libasound-dev libportaudio2 libportaudiocpp0 portaudio19-dev \
  gcc npm git build-essential \
  && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PyTorch here — cached in this image
RUN pip install --no-cache-dir torch==2.6.0
RUN pip install --no-cache-dir playwright

# Optional: include common Python deps
RUN pip install --no-cache-dir wheel setuptools

# Set up a default workdir (can be overwritten in final image)
WORKDIR /app

# building image
# docker build -f Dockerfile.base -t toolbox-base:3.11 .
