<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Workbench - SimpleCore</title>
    <link rel="stylesheet" type="text/css" href="/web/core0/styles.css?v=1.1">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <style>
        .workbench-container {
            display: flex;
            height: 100vh;
            background: var(--theme-bg);
            color: var(--theme-text);
        }
        
        .canvas-area {
            flex: 1;
            position: relative;
            background: var(--theme-bg-light);
            border-right: 1px solid var(--theme-border);
        }
        
        .toolbar {
            width: 300px;
            background: var(--theme-bg-light);
            padding: 1rem;
            overflow-y: auto;
            border-left: 1px solid var(--theme-border);
        }
        
        .header {
            padding: 1rem;
            background: var(--theme-bg-light);
            border-bottom: 1px solid var(--theme-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .tool-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--theme-border);
        }
        
        .tool-section h3 {
            margin: 0 0 0.5rem 0;
            color: var(--theme-accent);
            font-size: 1rem;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: var(--theme-accent);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            background: var(--theme-accent-hover);
        }
        
        .btn-secondary {
            background: var(--theme-bg);
            color: var(--theme-text);
            border: 1px solid var(--theme-border);
        }
        
        .btn-secondary:hover {
            background: var(--theme-bg-hover);
        }
        
        #canvas {
            border: 1px solid var(--theme-border);
        }
        
        .ideas-list {
            max-height: 200px;
            overflow-y: auto;
            background: var(--theme-bg);
            border: 1px solid var(--theme-border);
            border-radius: 4px;
            padding: 0.5rem;
        }
        
        .idea-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: var(--theme-bg-light);
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .idea-item:hover {
            background: var(--theme-bg-hover);
        }
        
        .loading {
            text-align: center;
            padding: 1rem;
            color: var(--theme-text-muted);
        }
        
        .error {
            color: var(--theme-error);
            font-size: 0.8rem;
            padding: 0.5rem;
            background: var(--theme-error-bg);
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        .success {
            color: var(--theme-success);
            font-size: 0.8rem;
            padding: 0.5rem;
            background: var(--theme-success-bg);
            border-radius: 4px;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="workbench-container">
        <!-- Header -->
        <div class="header" style="position: absolute; top: 0; left: 0; right: 0; z-index: 100;">
            <h1>Digital Workbench</h1>
            <div>
                <button class="btn btn-secondary" onclick="saveWorkbench()">Save Workbench</button>
                <button class="btn btn-secondary" onclick="window.TBf?.router('/api/SimpleCore/ui')">Back to Home</button>
            </div>
        </div>
        
        <!-- Canvas Area -->
        <div class="canvas-area" style="margin-top: 60px;">
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <!-- Toolbar -->
        <div class="toolbar" style="margin-top: 60px;">
            <!-- Basic Tools -->
            <div class="tool-section">
                <h3>Basic Shapes</h3>
                <button class="btn" onclick="addRectangle()">Add Rectangle</button>
                <button class="btn" onclick="addCircle()">Add Circle</button>
                <button class="btn" onclick="addText()">Add Text</button>
            </div>
            
            <!-- Idea Tools -->
            <div class="tool-section">
                <h3>Idea Components</h3>
                <button class="btn" onclick="addIdeaComponent()">Add Idea Component</button>
                <button class="btn" onclick="addConnection()">Add Connection</button>
            </div>
            
            <!-- My Ideas -->
            <div class="tool-section">
                <h3>My Ideas</h3>
                <button class="btn btn-secondary" onclick="loadMyIdeas()">Load My Ideas</button>
                <div id="ideas-container">
                    <div class="loading">Click "Load My Ideas" to see your ideas</div>
                </div>
            </div>
            
            <!-- Collaboration -->
            <div class="tool-section">
                <h3>Collaboration</h3>
                <button class="btn btn-secondary" onclick="findCollaborators()">Find Collaborators</button>
                <div id="collaborators-list">
                    <div class="loading">No collaborators online</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Initialize Fabric.js canvas
        let canvas;
        let userIdeas = [];
        let isLoading = false;
        
        // Initialize canvas
        function initCanvas() {
            canvas = new fabric.Canvas('canvas', {
                backgroundColor: '#f8f9fa',
                selection: true
            });
            
            // Resize canvas to fit container
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);
        }
        
        function resizeCanvas() {
            const container = document.querySelector('.canvas-area');
            const rect = container.getBoundingClientRect();
            canvas.setDimensions({
                width: rect.width - 20,
                height: rect.height - 20
            });
        }
        
        // Basic shape functions
        window.addRectangle = function() {
            const rect = new fabric.Rect({
                left: 100,
                top: 100,
                width: 100,
                height: 60,
                fill: '#3498db',
                stroke: '#2980b9',
                strokeWidth: 2
            });
            canvas.add(rect);
        };
        
        window.addCircle = function() {
            const circle = new fabric.Circle({
                left: 150,
                top: 150,
                radius: 50,
                fill: '#e74c3c',
                stroke: '#c0392b',
                strokeWidth: 2
            });
            canvas.add(circle);
        };
        
        window.addText = function() {
            const text = new fabric.Text('Your Text Here', {
                left: 200,
                top: 200,
                fontSize: 16,
                fill: '#2c3e50'
            });
            canvas.add(text);
        };
        
        window.addIdeaComponent = function() {
            const component = new fabric.Rect({
                left: 250,
                top: 100,
                width: 120,
                height: 80,
                fill: '#9b59b6',
                stroke: '#8e44ad',
                strokeWidth: 2,
                rx: 10,
                ry: 10
            });
            
            const label = new fabric.Text('Idea Component', {
                left: 260,
                top: 130,
                fontSize: 12,
                fill: 'white'
            });
            
            const group = new fabric.Group([component, label], {
                left: 250,
                top: 100
            });
            
            canvas.add(group);
        };
        
        window.addConnection = function() {
            const line = new fabric.Line([50, 50, 200, 200], {
                stroke: '#34495e',
                strokeWidth: 3,
                selectable: true
            });
            canvas.add(line);
        };
        
        // Load user ideas
        window.loadMyIdeas = async function() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('ideas-container');
            container.innerHTML = '<div class="loading">Loading ideas...</div>';
            
            try {
                // Use fetch instead of TB.api to avoid potential issues
                const response = await fetch('/api/SimpleCore/get_user_ideas', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.error === 'none') {
                    userIdeas = data.result?.data || [];
                    displayIdeas();
                } else {
                    throw new Error(data.info?.help_text || 'Failed to load ideas');
                }
            } catch (error) {
                console.error('Error loading ideas:', error);
                container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            } finally {
                isLoading = false;
            }
        };
        
        function displayIdeas() {
            const container = document.getElementById('ideas-container');
            
            if (userIdeas.length === 0) {
                container.innerHTML = '<div class="loading">No ideas found. Create some ideas first!</div>';
                return;
            }
            
            const ideasHtml = userIdeas.map(idea => `
                <div class="idea-item" onclick="addIdeaToCanvas('${idea.id}')">
                    <strong>${idea.title}</strong>
                    <div style="font-size: 0.7rem; color: var(--theme-text-muted);">
                        ${idea.description.substring(0, 50)}...
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = ideasHtml;
        }
        
        window.addIdeaToCanvas = function(ideaId) {
            const idea = userIdeas.find(i => i.id === ideaId);
            if (!idea) return;
            
            const ideaBox = new fabric.Rect({
                left: Math.random() * 300 + 50,
                top: Math.random() * 200 + 50,
                width: 150,
                height: 100,
                fill: '#f39c12',
                stroke: '#e67e22',
                strokeWidth: 2,
                rx: 8,
                ry: 8
            });
            
            const title = new fabric.Text(idea.title, {
                left: ideaBox.left + 10,
                top: ideaBox.top + 10,
                fontSize: 14,
                fill: 'white',
                fontWeight: 'bold'
            });
            
            const description = new fabric.Text(
                idea.description.substring(0, 40) + '...',
                {
                    left: ideaBox.left + 10,
                    top: ideaBox.top + 30,
                    fontSize: 10,
                    fill: 'white'
                }
            );
            
            const group = new fabric.Group([ideaBox, title, description], {
                left: ideaBox.left,
                top: ideaBox.top
            });
            
            canvas.add(group);
        };
        
        // Save workbench
        window.saveWorkbench = function() {
            const workbenchData = {
                canvas: canvas.toJSON(),
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('simplecore_workbench', JSON.stringify(workbenchData));
            
            // Show success message briefly
            const container = document.getElementById('ideas-container');
            const originalContent = container.innerHTML;
            container.innerHTML = '<div class="success">Workbench saved!</div>';
            setTimeout(() => {
                container.innerHTML = originalContent;
            }, 2000);
        };
        
        // Load workbench
        function loadWorkbench() {
            try {
                const saved = localStorage.getItem('simplecore_workbench');
                if (saved) {
                    const data = JSON.parse(saved);
                    canvas.loadFromJSON(data.canvas, canvas.renderAll.bind(canvas));
                }
            } catch (error) {
                console.error('Error loading workbench:', error);
            }
        }
        
        // Find collaborators
        window.findCollaborators = async function() {
            const container = document.getElementById('collaborators-list');
            container.innerHTML = '<div class="loading">Finding collaborators...</div>';
            
            try {
                const response = await fetch('/api/SimpleCore/find_connections', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.error === 'none') {
                    const connections = data.result?.data || [];
                    if (connections.length === 0) {
                        container.innerHTML = '<div class="loading">No collaborators found</div>';
                    } else {
                        const connectionsHtml = connections.map(conn => `
                            <div class="idea-item">
                                <strong>User ${conn.user_id}</strong>
                                <div style="font-size: 0.7rem;">
                                    Compatibility: ${(conn.compatibility_score * 100).toFixed(0)}%
                                </div>
                            </div>
                        `).join('');
                        container.innerHTML = connectionsHtml;
                    }
                } else {
                    throw new Error(data.info?.help_text || 'Failed to find collaborators');
                }
            } catch (error) {
                console.error('Error finding collaborators:', error);
                container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        };
        
        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initCanvas();
            loadWorkbench();
        });
    </script>
</body>
</html>
