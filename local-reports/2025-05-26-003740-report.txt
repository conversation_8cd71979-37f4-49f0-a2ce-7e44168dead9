Quality Check Report - 2025-05-26-003740
Commit: 7428541df3f90405d969f4dcecf57f9c441a75d3
========================================

[Ruff] Exit Code: 1
[Ruff] Output:
test.py:10:1: I001 [*] Import block is un-sorted or un-formatted
   |
 8 |   """
 9 |
10 | / import argparse
11 | | import json
12 | | import pathlib
13 | | import subprocess
14 | | import sys
15 | | import tempfile
16 | | from typing import List, Dict
17 | |
18 | | import pandas as pd
   | |___________________^ I001
19 |   def safe_get_git_commits(repo: pathlib.Path, max_commits: int = 50) -> pd.DataFrame:
20 |       try:
   |
   = help: Organize imports

test.py:10:8: F401 [*] `argparse` imported but unused
   |
 8 | """
 9 |
10 | import argparse
   |        ^^^^^^^^ F401
11 | import json
12 | import pathlib
   |
   = help: Remove unused import: `argparse`

test.py:14:8: F401 [*] `sys` imported but unused
   |
12 | import pathlib
13 | import subprocess
14 | import sys
   |        ^^^ F401
15 | import tempfile
16 | from typing import List, Dict
   |
   = help: Remove unused import: `sys`

test.py:15:8: F401 [*] `tempfile` imported but unused
   |
13 | import subprocess
14 | import sys
15 | import tempfile
   |        ^^^^^^^^ F401
16 | from typing import List, Dict
   |
   = help: Remove unused import: `tempfile`

test.py:16:1: UP035 `typing.List` is deprecated, use `list` instead
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
17 |
18 | import pandas as pd
   |

test.py:16:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
17 |
18 | import pandas as pd
   |

test.py:16:20: F401 [*] `typing.List` imported but unused
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   |                    ^^^^ F401
17 |
18 | import pandas as pd
   |
   = help: Remove unused import

test.py:16:26: F401 [*] `typing.Dict` imported but unused
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   |                          ^^^^ F401
17 |
18 | import pandas as pd
   |
   = help: Remove unused import

toolboxv2\__gui__.py:10:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
 8 |     import customtkinter as ctk
 9 | except ImportError:
10 |     os.system("pip install customtkinter")
   |     ^^^^^^^^^ S605
11 |     import customtkinter as ctk
   |

toolboxv2\__init__.py:4:18: F401 `yaml.safe_load` imported but unused
  |
2 | import os
3 |
4 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
5 |
6 | try:
  |
  = help: Remove unused import: `yaml.safe_load`

toolboxv2\__init__.py:116:12: F401 `toolboxv2.mods` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
114 | try:
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
    |            ^^^^^^^^^^^^^^ F401
117 |     from toolboxv2.mods import *
118 | except ImportError as e:
    |
    = help: Remove unused import: `toolboxv2.mods`

toolboxv2\__init__.py:117:5: F403 `from toolboxv2.mods import *` used; unable to detect undefined names
    |
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
117 |     from toolboxv2.mods import *
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
118 | except ImportError as e:
119 |     MODS_ERROR = e
    |

toolboxv2\__init__.py:129:22: F401 `platform.node` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                      ^^^^ F401
130 |
131 | __init_cwd__ = init_cwd = Path.cwd()
    |
    = help: Remove unused import

toolboxv2\__init__.py:129:28: F401 `platform.system` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                            ^^^^^^ F401
130 |
131 | __init_cwd__ = init_cwd = Path.cwd()
    |
    = help: Remove unused import

toolboxv2\__init__.py:150:5: F405 `mods` may be undefined, or defined from star imports
    |
148 |     "get_logger",
149 |     "flows_dict",
150 |     "mods",
    |     ^^^^^^ F405
151 |     "get_app",
152 |     "TBEF",
    |

toolboxv2\__main__.py:31:12: F401 `hmr` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
30 | try:
31 |     import hmr
   |            ^^^ F401
32 |
33 |     HOT_RELOADER = True
   |
   = help: Remove unused import: `hmr`

toolboxv2\__main__.py:105:5: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
103 |     def profile_execute_all_functions(*args):
104 |         return print(args)
105 |     raise ValueError("Failed to import function for profiling")
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
106 |
107 | try:
    |

toolboxv2\__main__.py:872:17: S605 Starting a process with a shell, possible injection detected
    |
870 |             print(f"Exit app {app_pid}")
871 |             if system() == "Windows":
872 |                 os.system(f"taskkill /pid {app_pid} /F")
    |                 ^^^^^^^^^ S605
873 |             else:
874 |                 os.system(f"kill -9 {app_pid}")
    |

toolboxv2\__main__.py:874:17: S605 Starting a process with a shell, possible injection detected
    |
872 |                 os.system(f"taskkill /pid {app_pid} /F")
873 |             else:
874 |                 os.system(f"kill -9 {app_pid}")
    |                 ^^^^^^^^^ S605
875 |
876 |     if args.command and not args.background_application:
    |

toolboxv2\__main__.py:898:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
897 | def install_ipython():
898 |     os.system('pip install ipython prompt_toolkit')
    |     ^^^^^^^^^ S605
    |

toolboxv2\__main__.py:1029:5: B018 Found useless expression. Either assign it to a variable or remove it.
     |
1028 | """)
1029 |     ()
     |     ^^ B018
1030 |     return c
     |

toolboxv2\apps\demo.py:15:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
14 | # Beispielinhalt der Streamlit-App
15 | st.title(f"{os.system('dir')}")
   |             ^^^^^^^^^ S605
16 | st.title("Meine Streamlit-App mit benutzerdefiniertem Styling")
17 | st.write("Dieser Text sollte gemäß dem benutzerdefinierten CSS gestylt sein.")
   |

toolboxv2\flows\core0.py:8:9: F401 `toolboxv2.mods.EventManager.module.EventID` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 6 | try:
 7 |     from toolboxv2.mods.EventManager.module import (
 8 |         EventID,
   |         ^^^^^^^ F401
 9 |         EventManagerClass,
10 |         Scope,
   |
   = help: Remove unused import: `toolboxv2.mods.EventManager.module.EventID`

toolboxv2\flows\core0.py:82:5: S605 Starting a process with a shell, possible injection detected
   |
81 |     # os.system(f"toolboxv2 --test --debug")
82 |     os.system(f"tb -bgr -p 42869 -n core0 -l -m {NAME}")
   |     ^^^^^^^^^ S605
   |

toolboxv2\flows\docker.py:21:9: S605 Starting a process with a shell, possible injection detected
   |
19 |     app.print(f"Running command : {comm}")
20 |     try:
21 |         os.system(comm)
   |         ^^^^^^^^^ S605
22 |     except KeyboardInterrupt:
23 |         app.print("Exit")
   |

toolboxv2\flows\minicli.py:40:5: S605 Starting a process with a shell, possible injection detected
   |
38 |     _ = ""
39 |     _ = "powershell -Command " if pw else "bash -c "
40 |     os.system(_ + buff)
   |     ^^^^^^^^^ S605
   |

toolboxv2\flows\minicli.py:48:18: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
47 |     try:
48 |         result = eval(buff, app.globals['root'], app.locals['user'])
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
49 |         if result is not None:
50 |             print(f"+ #{app.locals['user']['counts']}>", result)
   |

toolboxv2\flows\minicli.py:55:13: S102 Use of `exec` detected
   |
53 |     except SyntaxError:
54 |         try:
55 |             exec(buff, app.globals['root'], app.locals['user'])
   |             ^^^^ S102
56 |             print(f"* #{app.locals['user']['counts']}> Statement executed")
57 |         except Exception as e:
   |

toolboxv2\flows\minicli.py:264:36: B023 Function definition does not bind loop variable `cpu_usage`
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                    ^^^^^^^^^ B023
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

toolboxv2\flows\minicli.py:264:57: B023 Function definition does not bind loop variable `memory_usage`
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                                         ^^^^^^^^^^^^ B023
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

toolboxv2\flows\minicli.py:264:79: B023 Function definition does not bind loop variable `disk_usage`
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                                                               ^^^^^^^^^^ B023
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

toolboxv2\flows\vad_talk.py:138:50: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
136 |                     if self.input_queue.qsize() < self.config.MAX_QUEUE_SIZE:
137 |                         self.input_queue.put(data)
138 |                         if self.config.DEBUG and random.random() < 0.01:  # Log occasionally in debug mode
    |                                                  ^^^^^^^^^^^^^^^ S311
139 |                             self.logger.debug(
140 |                                 f"Audio chunk queued: {len(data)} bytes, queue size: {self.input_queue.qsize()}")
    |

toolboxv2\flows\vad_talk.py:146:68: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
144 |                             self.logger.debug(f"Queue full ({self.input_queue.qsize()}), skipping audio frame")
145 |                         time.sleep(0.01)
146 |                 elif not self.mute_input and self.config.DEBUG and random.random() < 0.01:
    |                                                                    ^^^^^^^^^^^^^^^ S311
147 |                     self.logger.debug("Audio input (TTS active)")
    |

toolboxv2\flows\vad_talk.py:208:54: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
206 |                         if not self.mute_input and self.input_queue.qsize() < self.config.MAX_QUEUE_SIZE:
207 |                             self.input_queue.put(data)
208 |                             if self.config.DEBUG and random.random() < 0.01:
    |                                                      ^^^^^^^^^^^^^^^ S311
209 |                                 self.logger.debug(f"Websocket audio chunk received: {len(data)} bytes")
210 |             except Exception as e:
    |

toolboxv2\flows\vad_talk.py:330:42: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
328 |                 # Frame size is not 20ms or 40ms, use energy-based detection
329 |                 is_speech = avg_energy > self.energy_threshold
330 |                 if self.config.DEBUG and random.random() < 0.01:
    |                                          ^^^^^^^^^^^^^^^ S311
331 |                     self.logger.debug(
332 |                         f"Using energy-based detection for frame size {frame_size}: energy={avg_energy}, is_speech={is_speech}")
    |

toolboxv2\flows\vad_talk.py:439:50: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
438 |                     elif silence_duration > 0.1:  # Short pause detection
439 |                         if self.config.DEBUG and random.random() < 0.05:
    |                                                  ^^^^^^^^^^^^^^^ S311
440 |                             self.logger.debug(f"Thinking pause detected: {silence_duration:.2f}s")
441 |                         return "thinking", None
    |

toolboxv2\flows\vad_talk.py:558:55: B023 Function definition does not bind loop variable `audio_data`
    |
556 |                         try:
557 |                             # Transcribe the audio
558 |                             result = self._transcribe(audio_data)
    |                                                       ^^^^^^^^^^ B023
559 |                             with self.processing_lock:
    |

toolboxv2\flows\vad_talk.py:562:36: B023 Function definition does not bind loop variable `segment_id`
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
    |                          ^^^^^^^^^^ B023
563 | …                         self.transcription_segments[segment_id] = result
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

toolboxv2\flows\vad_talk.py:562:62: B023 Function definition does not bind loop variable `segment_id`
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
    |                                                    ^^^^^^^^^^ B023
563 | …                         self.transcription_segments[segment_id] = result
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

toolboxv2\flows\vad_talk.py:563:65: B023 Function definition does not bind loop variable `segment_id`
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
563 | …                         self.transcription_segments[segment_id] = result
    |                                                       ^^^^^^^^^^ B023
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

toolboxv2\flows\vad_talk.py:564:87: B023 Function definition does not bind loop variable `segment_id`
    |
562 |                                 if segment_id != "final" and segment_id != "legacy":
563 |                                     self.transcription_segments[segment_id] = result
564 |                                     self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |                                                                                       ^^^^^^^^^^ B023
565 |
566 |                                 # For final segments, try to concatenate previous segments if available
    |

toolboxv2\flows\vad_talk.py:567:36: B023 Function definition does not bind loop variable `is_final`
    |
566 | …                     # For final segments, try to concatenate previous segments if available
567 | …                     if is_final and self.transcription_segments:
    |                          ^^^^^^^^ B023
568 | …                         combined_result = self._concatenate_transcriptions(result)
569 | …                         self.logger.info(
    |

toolboxv2\flows\vad_talk.py:577:36: B023 Function definition does not bind loop variable `is_final`
    |
576 | …                     # Store for reference
577 | …                     if is_final:
    |                          ^^^^^^^^ B023
578 | …                         self.last_transcription = result
    |

toolboxv2\flows\vad_talk.py:581:36: B023 Function definition does not bind loop variable `callback`
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
    |                                    ^^^^^^^^ B023
582 |                                     self.results_queue.put((result, is_final, callback))
583 |                         finally:
    |

toolboxv2\flows\vad_talk.py:582:69: B023 Function definition does not bind loop variable `is_final`
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
582 |                                     self.results_queue.put((result, is_final, callback))
    |                                                                     ^^^^^^^^ B023
583 |                         finally:
584 |                             self.task_queue.task_done()
    |

toolboxv2\flows\vad_talk.py:582:79: B023 Function definition does not bind loop variable `callback`
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
582 |                                     self.results_queue.put((result, is_final, callback))
    |                                                                               ^^^^^^^^ B023
583 |                         finally:
584 |                             self.task_queue.task_done()
    |

toolboxv2\flows\vad_talk.py:792:24: SIM115 Use a context manager for opening files
    |
790 |                 return ""
791 |             # Create a temporary WAV file
792 |             temp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^ SIM115
793 |             wav_path = temp_wav.name
794 |             temp_wav.close()  # Close the file handle immediately
    |

toolboxv2\flows\vad_talk.py:1374:28: B023 Function definition does not bind loop variable `callback_start`
     |
1373 |                         # Trigger any provided callback before starting
1374 |                         if callback_start:
     |                            ^^^^^^^^^^^^^^ B023
1375 |                             callback_start()
     |

toolboxv2\flows\vad_talk.py:1375:29: B023 Function definition does not bind loop variable `callback_start`
     |
1373 |                         # Trigger any provided callback before starting
1374 |                         if callback_start:
1375 |                             callback_start()
     |                             ^^^^^^^^^^^^^^ B023
1376 |
1377 |                         self.logger.debug(f"Starting TTS for text: {text[:50]}{'...' if len(text) > 50 else ''}")
     |

toolboxv2\flows\vad_talk.py:1390:32: B023 Function definition does not bind loop variable `callback_end`
     |
1388 | …                     self.is_speaking = False
1389 | …                     #self.current_text = None
1390 | …                     if callback_end:
     |                          ^^^^^^^^^^^^ B023
1391 | …                         callback_end(text)
1392 | …                     #self.tts_queue.task_done()
     |

toolboxv2\flows\vad_talk.py:1391:33: B023 Function definition does not bind loop variable `callback_end`
     |
1389 | …                     #self.current_text = None
1390 | …                     if callback_end:
1391 | …                         callback_end(text)
     |                           ^^^^^^^^^^^^ B023
1392 | …                     #self.tts_queue.task_done()
1393 | …                     pass
     |

toolboxv2\mods\CloudM\AdminDashboard.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/CloudM/AdminDashboard.py
 2 |
 3 | / import uuid
 4 | | from dataclasses import asdict
 5 | | import json
 6 | |
 7 | | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user, db_helper_delete_user, get_user_by_name, \
 9 | |     db_helper_test_exist
10 | | from toolboxv2.mods.CloudM import mini
11 | | from toolboxv2.mods.CloudM.ModManager import list_modules as list_all_modules
12 | | from .types import User
13 | | from .UserAccountManager import get_current_user_from_request
14 | | # For Waiting List invites, we'll call CloudM.email_services.send_signup_invitation_email
15 | | from .email_services import send_signup_invitation_email
   | |________________________________________________________^ I001
16 |
17 |   Name = 'CloudM.AdminDashboard'
   |
   = help: Organize imports

toolboxv2\mods\CloudM\AdminDashboard.py:3:8: F401 [*] `uuid` imported but unused
  |
1 | # toolboxv2/mods/CloudM/AdminDashboard.py
2 |
3 | import uuid
  |        ^^^^ F401
4 | from dataclasses import asdict
5 | import json
  |
  = help: Remove unused import: `uuid`

toolboxv2\mods\CloudM\AdminDashboard.py:8:91: F401 [*] `toolboxv2.mods.CloudM.AuthManager.get_user_by_name` imported but unused
   |
 7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user, db_helper_delete_user, get_user_by_name, \
   |                                                                                           ^^^^^^^^^^^^^^^^ F401
 9 |     db_helper_test_exist
10 | from toolboxv2.mods.CloudM import mini
   |
   = help: Remove unused import

toolboxv2\mods\CloudM\AdminDashboard.py:9:5: F401 [*] `toolboxv2.mods.CloudM.AuthManager.db_helper_test_exist` imported but unused
   |
 7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user, db_helper_delete_user, get_user_by_name, \
 9 |     db_helper_test_exist
   |     ^^^^^^^^^^^^^^^^^^^^ F401
10 | from toolboxv2.mods.CloudM import mini
11 | from toolboxv2.mods.CloudM.ModManager import list_modules as list_all_modules
   |
   = help: Remove unused import

toolboxv2\mods\CloudM\AdminDashboard.py:941:33: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
939 |                     app.print("Warning: User data (admin list) not valid JSON, falling back to eval: " + str(
940 |                         user_str[:100]) + "...", "WARNING")
941 |                     user_dict = eval(user_str)
    |                                 ^^^^^^^^^^^^^^ S307
942 |                 users_data.append({"uid": user_dict.get("uid", "N/A"), "name": user_dict.get("name", "N/A"),
943 |                                    "email": user_dict.get("email"), "level": user_dict.get("level", -1),
    |

toolboxv2\mods\CloudM\AdminDashboard.py:953:11: F811 Redefinition of unused `get_system_status` from line 885
    |
952 | @export(mod_name=Name, api=True, version=version, request_as_kwarg=True)
953 | async def get_system_status(app: App, request: RequestData):
    |           ^^^^^^^^^^^^^^^^^ F811
954 |     admin_user = await _is_admin(app, request)
955 |     if not admin_user: return Result.default_user_error(info="Permission denied", exec_code=403)
    |
    = help: Remove definition: `get_system_status`

toolboxv2\mods\CloudM\AdminDashboard.py:989:11: F811 Redefinition of unused `list_users_admin` from line 922
    |
988 | @export(mod_name=Name, api=True, version=version, request_as_kwarg=True)
989 | async def list_users_admin(app: App, request: RequestData):
    |           ^^^^^^^^^^^^^^^^ F811
990 |     admin_user = await _is_admin(app, request)
991 |     if not admin_user: return Result.default_user_error(info="Permission denied", exec_code=403)
    |
    = help: Remove definition: `list_users_admin`

toolboxv2\mods\CloudM\AdminDashboard.py:1007:33: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1005 |                     app.print("Warning: User data (admin list) not valid JSON, falling back to eval: " + str(
1006 |                         user_str[:100]) + "...", "WARNING")
1007 |                     user_dict = eval(user_str)
     |                                 ^^^^^^^^^^^^^^ S307
1008 |                 users_data.append({"uid": user_dict.get("uid", "N/A"), "name": user_dict.get("name", "N/A"),
1009 |                                    "email": user_dict.get("email"), "level": user_dict.get("level", -1),
     |

toolboxv2\mods\CloudM\AdminDashboard.py:1078:37: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1076 |                     user_dict_raw = json.loads(user_str)
1077 |                 except json.JSONDecodeError:
1078 |                     user_dict_raw = eval(user_str)
     |                                     ^^^^^^^^^^^^^^ S307
1079 |                 username_to_delete = user_dict_raw.get("name")
1080 |             except Exception as e:
     |

toolboxv2\mods\CloudM\AuthManager.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import base64
 3 | | import datetime
 4 | | import json
 5 | | import os
 6 | | import time
 7 | | import uuid
 8 | | from dataclasses import asdict
 9 | | from urllib.parse import quote
10 | |
11 | | import jwt
12 | | import webauthn
13 | | from pydantic import BaseModel, field_validator
14 | | from webauthn.helpers.exceptions import (
15 | |     InvalidAuthenticationResponse,
16 | |     InvalidRegistrationResponse,
17 | | )
18 | | from webauthn.helpers.structs import AuthenticationCredential, RegistrationCredential
19 | |
20 | | from toolboxv2 import TBEF, App, Result, ToolBox_over, get_app, get_logger
21 | | from toolboxv2.mods.DB.types import DatabaseModes
22 | | from toolboxv2.utils.security.cryp import Code
23 | | from toolboxv2.utils.system.types import ApiResult, ToolBoxInterfaces
24 | | from .email_services import send_magic_link_email
25 | |
26 | | from .types import User, UserCreator
   | |____________________________________^ I001
27 |
28 |   version = "0.0.2"
   |
   = help: Organize imports

toolboxv2\mods\CloudM\AuthManager.py:20:42: F401 [*] `toolboxv2.ToolBox_over` imported but unused
   |
18 | from webauthn.helpers.structs import AuthenticationCredential, RegistrationCredential
19 |
20 | from toolboxv2 import TBEF, App, Result, ToolBox_over, get_app, get_logger
   |                                          ^^^^^^^^^^^^ F401
21 | from toolboxv2.mods.DB.types import DatabaseModes
22 | from toolboxv2.utils.security.cryp import Code
   |
   = help: Remove unused import: `toolboxv2.ToolBox_over`

toolboxv2\mods\CloudM\AuthManager.py:194:38: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
193 |     if isinstance(user_data, bytes):
194 |         return Result.ok(data=User(**eval(user_data.decode())))
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^ S307
195 |     if isinstance(user_data, str):
196 |         return Result.ok(data=User(**eval(user_data)))
    |

toolboxv2\mods\CloudM\AuthManager.py:196:38: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
194 |         return Result.ok(data=User(**eval(user_data.decode())))
195 |     if isinstance(user_data, str):
196 |         return Result.ok(data=User(**eval(user_data)))
    |                                      ^^^^^^^^^^^^^^^ S307
197 |     if isinstance(user_data, dict):
198 |         return Result.ok(data=User(**user_data))
    |

toolboxv2\mods\CloudM\AuthManager.py:209:38: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
207 |             user_data[0] = user_data[0].decode()
208 |
209 |         return Result.ok(data=User(**eval(user_data[0])))
    |                                      ^^^^^^^^^^^^^^^^^^ S307
210 |     else:
211 |         return Result.default_internal_error(info="get_user_by_name failed no User data found", exec_code=2351)
    |

toolboxv2\mods\CloudM\ModManager.py:80:9: S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
   |
78 |         print_func(f"{url} -> {directory}/{filename}")
79 |         os.makedirs(directory, exist_ok=True)
80 |         urllib.request.urlretrieve(url, f"{directory}/{filename}")
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S310
81 |     return f"{directory}/{filename}"
   |

toolboxv2\mods\CloudM\ModManager.py:89:9: S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
   |
87 |         requirements_filename = f"{module_name}-requirements.txt"
88 |         print_func(f"Download requirements {requirements_filename}")
89 |         urllib.request.urlretrieve(requirements_url, requirements_filename)
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S310
90 |
91 |         print_func("Install requirements")
   |

toolboxv2\mods\CloudM\ModManager.py:258:13: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
256 |               temp_dir = Path(temp_dir)
257 |
258 | /             with Spinner(f"Extracting {zip_path.name}"):
259 | |                 # Entpacke ZIP-Datei
260 | |                 with zipfile.ZipFile(zip_path, 'r') as zip_ref:
    | |_______________________________________________________________^ SIM117
261 |                       zip_ref.extractall(temp_dir)
    |
    = help: Combine `with` statements

toolboxv2\mods\CloudM\ModManager.py:462:11: F811 Redefinition of unused `update_all_mods` from line 423
    |
461 | @export(mod_name=Name, name="build_all", test=False)
462 | async def update_all_mods(app, base="mods", upload=True):
    |           ^^^^^^^^^^^^^^^ F811
463 |     if app is None:
464 |         app = get_app(f"{Name}.update_all")
    |
    = help: Remove definition: `update_all_mods`

toolboxv2\mods\CloudM\UI\UserAccountManager.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/CloudM/UI/user_account_manager.py
 2 |
 3 | / import uuid
 4 | | from dataclasses import asdict
 5 | |
 6 | |
 7 | | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user  # Assuming AuthManager functions are accessible
 9 | | from ..types import User  # From toolboxv2/mods/CloudM/types.py
   | |________________________^ I001
10 |
11 |   Name = 'CloudM.UI.UserAccountManager'
   |
   = help: Organize imports

toolboxv2\mods\CloudM\UI\UserAccountManager.py:3:8: F401 [*] `uuid` imported but unused
  |
1 | # toolboxv2/mods/CloudM/UI/user_account_manager.py
2 |
3 | import uuid
  |        ^^^^ F401
4 | from dataclasses import asdict
  |
  = help: Remove unused import: `uuid`

toolboxv2\mods\CloudM\UI\UserAccountManager.py:7:34: F401 [*] `toolboxv2.Result` imported but unused
  |
7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
  |                                  ^^^^^^ F401
8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user  # Assuming AuthManager functions are accessible
9 | from ..types import User  # From toolboxv2/mods/CloudM/types.py
  |
  = help: Remove unused import: `toolboxv2.Result`

toolboxv2\mods\CloudM\UI\UserAccountManager.py:88:16: F541 [*] f-string without any placeholders
   |
86 |         target_id_suffix = target_id_suffix.split("-")[-1]
87 |     if not user:
88 |         return f"<div class='text-red-500'>Error: User not authenticated or found.</div>"
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
89 |
90 |     if setting_key == "experimental_features":
   |
   = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\UI\__init__.py:1:21: F401 `.widget.get_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .widget import get_widget
  |                     ^^^^^^^^^^ F401
  |
  = help: Use an explicit re-export: `get_widget as get_widget`

toolboxv2\mods\CloudM\UI\widget.py:3:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 |   # toolboxv2/mods/CloudM/UI/widget.py
2 |
3 | / import uuid
4 | | from toolboxv2 import App, Result, get_app, RequestData, TBEF
  | |_____________________________________________________________^ I001
5 |
6 |   Name = 'CloudM.UI.widget'
  |
  = help: Organize imports

toolboxv2\mods\CloudM\UI\widget.py:3:8: F401 [*] `uuid` imported but unused
  |
1 | # toolboxv2/mods/CloudM/UI/widget.py
2 |
3 | import uuid
  |        ^^^^ F401
4 | from toolboxv2 import App, Result, get_app, RequestData, TBEF
  |
  = help: Remove unused import: `uuid`

toolboxv2\mods\CloudM\UI\widget.py:4:28: F401 [*] `toolboxv2.Result` imported but unused
  |
3 | import uuid
4 | from toolboxv2 import App, Result, get_app, RequestData, TBEF
  |                            ^^^^^^ F401
5 |
6 | Name = 'CloudM.UI.widget'
  |
  = help: Remove unused import

toolboxv2\mods\CloudM\UI\widget.py:4:58: F401 [*] `toolboxv2.TBEF` imported but unused
  |
3 | import uuid
4 | from toolboxv2 import App, Result, get_app, RequestData, TBEF
  |                                                          ^^^^ F401
5 |
6 | Name = 'CloudM.UI.widget'
  |
  = help: Remove unused import

toolboxv2\mods\CloudM\UI\widget.py:76:9: F841 Local variable `login_url` is assigned to but never used
   |
74 |         # For simplicity here, returning a basic prompt.
75 |         # A robust solution would involve TB.router on client side.
76 |         login_url = "/login.html"  # Placeholder, should come from config or TB.config
   |         ^^^^^^^^^ F841
77 |         # This assumes tbjs is already loaded and will handle routing/state if not logged in.
78 |         # If this endpoint is hit directly without auth, it might show this.
   |
   = help: Remove assignment to unused variable `login_url`

toolboxv2\mods\CloudM\UserAccountManager.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/CloudM/UI/user_account_manager.py
 2 |
 3 | / import uuid
 4 | | from dataclasses import asdict
 5 | |
 6 | | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 7 | | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user  # Assuming AuthManager functions are accessible
 8 | | from .types import User  # From toolboxv2/mods/CloudM/types.py
   | |_______________________^ I001
 9 |
10 |   Name = 'CloudM.UserAccountManager'
   |
   = help: Organize imports

toolboxv2\mods\CloudM\UserAccountManager.py:3:8: F401 [*] `uuid` imported but unused
  |
1 | # toolboxv2/mods/CloudM/UI/user_account_manager.py
2 |
3 | import uuid
  |        ^^^^ F401
4 | from dataclasses import asdict
  |
  = help: Remove unused import: `uuid`

toolboxv2\mods\CloudM\UserAccountManager.py:97:16: F541 [*] f-string without any placeholders
   |
95 | async def update_setting(app: App, request: RequestData, setting_key: str, setting_value: str):
96 |     if request is None:
97 |         return f"<div class='text-red-500'>Error: No request data provided.</div>"
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
98 |     user = await get_current_user_from_request(app, request)
99 |     # hx_trigger might not be reliable or always present if not an HTMX direct call.
   |
   = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\UserAccountManager.py:108:16: F541 [*] f-string without any placeholders
    |
107 |     if not user:
108 |         return f"<div class='text-red-500'>Error: User not authenticated or found.</div>"  # Basic error for HTMX
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
109 |
110 |     if setting_key == "experimental_features":
    |
    = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\UserDashboard.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/CloudM/UserDashboard.py
 2 |
 3 | / import uuid
 4 | | from dataclasses import asdict
 5 | | import json
 6 | |
 7 | | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user, get_magic_link_email as request_magic_link_backend
 9 | | from .types import User
10 | | from .UserAccountManager import get_current_user_from_request
11 | | from .UserInstances import get_user_instance as get_user_instance_internal, \
12 | |     close_user_instance as close_user_instance_internal
   | |_______________________________________________________^ I001
13 |
14 |   # We'll need a new function in UserInstances or a dedicated module manager for user instances
   |
   = help: Organize imports

toolboxv2\mods\CloudM\UserDashboard.py:3:8: F401 [*] `uuid` imported but unused
  |
1 | # toolboxv2/mods/CloudM/UserDashboard.py
2 |
3 | import uuid
  |        ^^^^ F401
4 | from dataclasses import asdict
5 | import json
  |
  = help: Remove unused import: `uuid`

toolboxv2\mods\CloudM\UserDashboard.py:5:8: F401 [*] `json` imported but unused
  |
3 | import uuid
4 | from dataclasses import asdict
5 | import json
  |        ^^^^ F401
6 |
7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
  |
  = help: Remove unused import: `json`

toolboxv2\mods\CloudM\UserDashboard.py:7:23: F401 [*] `toolboxv2.TBEF` imported but unused
  |
5 | import json
6 |
7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
  |                       ^^^^ F401
8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user, get_magic_link_email as request_magic_link_backend
9 | from .types import User
  |
  = help: Remove unused import: `toolboxv2.TBEF`

toolboxv2\mods\CloudM\UserDashboard.py:9:20: F401 [*] `.types.User` imported but unused
   |
 7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user, get_magic_link_email as request_magic_link_backend
 9 | from .types import User
   |                    ^^^^ F401
10 | from .UserAccountManager import get_current_user_from_request
11 | from .UserInstances import get_user_instance as get_user_instance_internal, \
   |
   = help: Remove unused import: `.types.User`

toolboxv2\mods\CloudM\UserInstances.py:145:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
143 |       if instance['SiID'] in UserInstances().live_user_instances:
144 |           instance_live = UserInstances().live_user_instances.get(instance['SiID'], {})
145 | /         if 'live' in instance_live:
146 | |             if instance_live['live'] and instance_live['save']['mods']:
    | |_______________________________________________________________________^ SIM102
147 |                   logger.info(Style.BLUEBG2("Instance returned from live"))
148 |                   return instance_live
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\CloudM\__init__.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / from .extras import login
 2 | | from .module import Tools
 3 | | from .types import User
 4 | | from .UI.widget import get_widget
 5 | | from .UserInstances import UserInstances
 6 | | from .mini import *
 7 | |
 8 | | from .AdminDashboard import Name as AdminDashboard
 9 | | from .UserAccountManager import Name as UserAccountManagerName
10 | | from .UserDashboard import Name as UserDashboardName
   | |____________________________________________________^ I001
11 |
12 |   tools = Tools
   |
   = help: Organize imports

toolboxv2\mods\CloudM\__init__.py:1:21: F401 `.extras.login` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .extras import login
  |                     ^^^^^ F401
2 | from .module import Tools
3 | from .types import User
  |
  = help: Add unused import `login` to __all__

toolboxv2\mods\CloudM\__init__.py:3:20: F401 `.types.User` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .extras import login
2 | from .module import Tools
3 | from .types import User
  |                    ^^^^ F401
4 | from .UI.widget import get_widget
5 | from .UserInstances import UserInstances
  |
  = help: Add unused import `User` to __all__

toolboxv2\mods\CloudM\__init__.py:4:24: F401 `.UI.widget.get_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
2 | from .module import Tools
3 | from .types import User
4 | from .UI.widget import get_widget
  |                        ^^^^^^^^^^ F401
5 | from .UserInstances import UserInstances
6 | from .mini import *
  |
  = help: Add unused import `get_widget` to __all__

toolboxv2\mods\CloudM\__init__.py:5:28: F401 `.UserInstances.UserInstances` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
3 | from .types import User
4 | from .UI.widget import get_widget
5 | from .UserInstances import UserInstances
  |                            ^^^^^^^^^^^^^ F401
6 | from .mini import *
  |
  = help: Add unused import `UserInstances` to __all__

toolboxv2\mods\CloudM\__init__.py:6:1: F403 `from .mini import *` used; unable to detect undefined names
  |
4 | from .UI.widget import get_widget
5 | from .UserInstances import UserInstances
6 | from .mini import *
  | ^^^^^^^^^^^^^^^^^^^ F403
7 |
8 | from .AdminDashboard import Name as AdminDashboard
  |

toolboxv2\mods\CloudM\__init__.py:8:37: F401 `.AdminDashboard.Name` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
 6 | from .mini import *
 7 |
 8 | from .AdminDashboard import Name as AdminDashboard
   |                                     ^^^^^^^^^^^^^^ F401
 9 | from .UserAccountManager import Name as UserAccountManagerName
10 | from .UserDashboard import Name as UserDashboardName
   |
   = help: Add unused import `AdminDashboard` to __all__

toolboxv2\mods\CloudM\__init__.py:9:41: F401 `.UserAccountManager.Name` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
 8 | from .AdminDashboard import Name as AdminDashboard
 9 | from .UserAccountManager import Name as UserAccountManagerName
   |                                         ^^^^^^^^^^^^^^^^^^^^^^ F401
10 | from .UserDashboard import Name as UserDashboardName
   |
   = help: Add unused import `UserAccountManagerName` to __all__

toolboxv2\mods\CloudM\__init__.py:10:36: F401 `.UserDashboard.Name` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
 8 | from .AdminDashboard import Name as AdminDashboard
 9 | from .UserAccountManager import Name as UserAccountManagerName
10 | from .UserDashboard import Name as UserDashboardName
   |                                    ^^^^^^^^^^^^^^^^^ F401
11 |
12 | tools = Tools
   |
   = help: Add unused import `UserDashboardName` to __all__

toolboxv2\mods\CloudM\__init__.py:15:12: F405 `mini` may be undefined, or defined from star imports
   |
13 | Name = 'CloudM'
14 | version = Tools.version
15 | __all__ = ["mini"]
   |            ^^^^^^ F405
   |

toolboxv2\mods\CloudM\email_services.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import smtplib
 2 | | from email.mime.multipart import MIMEMultipart
 3 | | from email.mime.text import MIMEText
 4 | | from jinja2 import Environment, BaseLoader
 5 | | import os
 6 | | import uuid
 7 | | import datetime
 8 | | from urllib.parse import quote  # For URL encoding parameters
   | |______________________________^ I001
 9 |
10 |   # Assuming Code is available from your toolboxv2 installation
   |
   = help: Organize imports

toolboxv2\mods\CloudM\email_services.py:25:1: I001 [*] Import block is un-sorted or un-formatted
   |
23 |       print("Warning: toolboxv2.utils.security.cryp.Code not found, using placeholder.")
24 |
25 | / from toolboxv2 import App, Result, get_app, get_logger, MainTool  # MainTool not used directly here
26 | | from toolboxv2.utils.system.types import ApiResult, \
27 | |     ToolBoxInterfaces, ToolBoxError  # ToolBoxError, ToolBoxInterfaces not used directly
   | |___________________________________^ I001
28 |
29 |   # --- Configuration ---
   |
   = help: Organize imports

toolboxv2\mods\CloudM\email_services.py:117:13: S701 By default, jinja2 sets `autoescape` to `False`. Consider using `autoescape=True` or the `select_autoescape` function to mitigate XSS vulnerabilities.
    |
116 | # Jinja2 Environment for inline templates
117 | jinja_env = Environment(loader=BaseLoader())
    |             ^^^^^^^^^^^ S701
118 | base_template_jinja = jinja_env.from_string(BASE_HTML_TEMPLATE)
    |

toolboxv2\mods\CloudM\email_services.py:351:11: F541 [*] f-string without any placeholders
    |
349 |     # or if GMAIL_EMAIL/PASSWORD are not set.
350 |
351 |     print(f"To test, ensure GMAIL_EMAIL, GMAIL_PASSWORD, and SENDER_EMAIL are set.")
    |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
352 |     print(f"Using SMTP Server: {SMTP_SERVER}:{SMTP_PORT}")
353 |     print(f"App Name: {APP_NAME}, Base URL: {APP_BASE_URL}")
    |
    = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\extras.py:143:9: S605 Starting a process with a shell, possible injection detected
    |
141 |         webbrowser.open(url, new=0, autoraise=True)
142 |     except Exception as e:
143 |         os.system(f"start {url}")
    |         ^^^^^^^^^ S605
144 |         self.logger.error(Style.YELLOW(str(e)))
145 |         self.print(Style.YELLOW(str(e)))
    |

toolboxv2\mods\CloudM\extras.py:152:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
150 | @no_test
151 | def init_git(_):
152 |     os.system("git init")
    |     ^^^^^^^^^ S605
153 |
154 |     os.system(" ".join(
    |

toolboxv2\mods\CloudM\extras.py:154:5: S605 Starting a process with a shell, possible injection detected
    |
152 |     os.system("git init")
153 |
154 |     os.system(" ".join(
    |     ^^^^^^^^^ S605
155 |         ['git', 'remote', 'add', 'origin', 'https://github.com/MarkinHaus/ToolBoxV2.git']))
    |

toolboxv2\mods\CloudM\extras.py:159:5: S605 Starting a process with a shell, possible injection detected
    |
157 |     # Stash any changes
158 |     print("Stashing changes...")
159 |     os.system(" ".join(['git', 'stash']))
    |     ^^^^^^^^^ S605
160 |
161 |     # Pull the latest changes
    |

toolboxv2\mods\CloudM\extras.py:163:5: S605 Starting a process with a shell, possible injection detected
    |
161 |     # Pull the latest changes
162 |     print("Pulling the latest changes...")
163 |     os.system(" ".join(['git', 'pull', 'origin', 'master']))
    |     ^^^^^^^^^ S605
164 |
165 |     # Apply stashed changes
    |

toolboxv2\mods\CloudM\extras.py:167:5: S605 Starting a process with a shell, possible injection detected
    |
165 |     # Apply stashed changes
166 |     print("Applying stashed changes...")
167 |     os.system(" ".join(['git', 'stash', 'pop']))
    |     ^^^^^^^^^ S605
    |

toolboxv2\mods\CloudM\extras.py:207:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
205 | def update_core_pip(self):
206 |     self.print("Init Update.. via pip")
207 |     os.system("pip install --upgrade ToolBoxV2")
    |     ^^^^^^^^^ S605
    |

toolboxv2\mods\CloudM\extras.py:213:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
211 |     self.print("Init Update..")
212 |     if backup:
213 |         os.system("git fetch --all")
    |         ^^^^^^^^^ S605
214 |         d = f"git branch backup-master-{self.st_router.id}-{self.version}-{name}"
215 |         os.system(d)
    |

toolboxv2\mods\CloudM\extras.py:215:9: S605 Starting a process with a shell, possible injection detected
    |
213 |         os.system("git fetch --all")
214 |         d = f"git branch backup-master-{self.st_router.id}-{self.version}-{name}"
215 |         os.system(d)
    |         ^^^^^^^^^ S605
216 |         os.system("git reset --hard origin/master")
217 |     out = os.system("git pull")
    |

toolboxv2\mods\CloudM\extras.py:216:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
214 |         d = f"git branch backup-master-{self.st_router.id}-{self.version}-{name}"
215 |         os.system(d)
216 |         os.system("git reset --hard origin/master")
    |         ^^^^^^^^^ S605
217 |     out = os.system("git pull")
218 |     self.st_router.remove_all_modules()
    |

toolboxv2\mods\CloudM\extras.py:217:11: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
215 |         os.system(d)
216 |         os.system("git reset --hard origin/master")
217 |     out = os.system("git pull")
    |           ^^^^^^^^^ S605
218 |     self.st_router.remove_all_modules()
219 |     try:
    |

toolboxv2\mods\CloudM\extras.py:239:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
237 |         )
238 |         if 'n' not in input("do git commands ? [Y/n]").lower():
239 |             os.system("git stash")
    |             ^^^^^^^^^ S605
240 |             os.system("git pull")
241 |             os.system("git stash pop")
    |

toolboxv2\mods\CloudM\extras.py:240:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
238 |         if 'n' not in input("do git commands ? [Y/n]").lower():
239 |             os.system("git stash")
240 |             os.system("git pull")
    |             ^^^^^^^^^ S605
241 |             os.system("git stash pop")
    |

toolboxv2\mods\CloudM\extras.py:241:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
239 |             os.system("git stash")
240 |             os.system("git pull")
241 |             os.system("git stash pop")
    |             ^^^^^^^^^ S605
242 |
243 |     if out == -1:
    |

toolboxv2\mods\CloudM\extras.py:244:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
243 |     if out == -1:
244 |         os.system("git fetch --all")
    |         ^^^^^^^^^ S605
245 |         os.system("git reset --hard origin/master")
    |

toolboxv2\mods\CloudM\extras.py:245:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
243 |     if out == -1:
244 |         os.system("git fetch --all")
245 |         os.system("git reset --hard origin/master")
    |         ^^^^^^^^^ S605
246 |
247 |     if "-u main" in com and len(com.split("-u main")) > 5:
    |

toolboxv2\mods\CloudM\extras.py:250:9: S605 Starting a process with a shell, possible injection detected
    |
248 |         c = com.replace('-u main ', '')
249 |         print("Restarting... with : " + c)
250 |         os.system(c)
    |         ^^^^^^^^^ S605
251 |     exit(0)
    |

toolboxv2\mods\CloudM\extras.py:308:5: SIM102 Use a single `if` statement instead of nested `if` statements
    |
306 |           return "Pleas connect first to a redis instance"
307 |
308 | /     if not do_root:
309 | |         if 'y' not in input(Style.RED("Ar u sure : the deb will be cleared type y :")):
    | |_______________________________________________________________________________________^ SIM102
310 |               return
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\CloudM\extras.py:360:22: F541 [*] f-string without any placeholders
    |
358 |                 name="UserDashboard",
359 |                 title=Name,
360 |                 path=f"/api/CloudM.UI.widget/get_widget",
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
361 |                 description="main",auth=True
362 |                 )
    |
    = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\mini.py:46:22: S602 `subprocess` call with `shell=True` identified, security issue
   |
45 |             # Add encoding handling for Windows
46 |             result = subprocess.run(
   |                      ^^^^^^^^^^^^^^ S602
47 |                 command,
48 |                 capture_output=True,
   |

toolboxv2\mods\CloudM\mini.py:75:26: S602 `subprocess` call with `shell=True` identified, security issue
   |
73 |             # Try alternate encoding if cp850 fails
74 |             try:
75 |                 result = subprocess.run(
   |                          ^^^^^^^^^^^^^^ S602
76 |                     command,
77 |                     capture_output=True,
   |

toolboxv2\mods\CloudM\mini.py:100:22: S602 `subprocess` call with `shell=True` identified, security issue
    |
 98 |             command = f'ps -p {pids_str} -o pid='
 99 |
100 |             result = subprocess.run(
    |                      ^^^^^^^^^^^^^^ S602
101 |                 command,
102 |                 capture_output=True,
    |

toolboxv2\mods\CloudM\module.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import binascii
 2 | | import hashlib
 3 | | import logging
 4 | | import os
 5 | | import time
 6 | |
 7 | | import requests
 8 | |
 9 | | from toolboxv2 import FileHandler, MainTool, Style, get_app
10 | |
11 | | from ...utils.system.api import find_highest_zip_version
12 | | from .UserInstances import UserInstances
13 | | from .UI.widget import get_widget
14 | | from .UserDashboard import Name as UserDashboardName
   | |____________________________________________________^ I001
15 |
16 |   Name = 'CloudM'
   |
   = help: Organize imports

toolboxv2\mods\CloudM\module.py:13:24: F401 [*] `.UI.widget.get_widget` imported but unused
   |
11 | from ...utils.system.api import find_highest_zip_version
12 | from .UserInstances import UserInstances
13 | from .UI.widget import get_widget
   |                        ^^^^^^^^^^ F401
14 | from .UserDashboard import Name as UserDashboardName
   |
   = help: Remove unused import: `.UI.widget.get_widget`

toolboxv2\mods\CloudM\module.py:14:36: F401 [*] `.UserDashboard.Name` imported but unused
   |
12 | from .UserInstances import UserInstances
13 | from .UI.widget import get_widget
14 | from .UserDashboard import Name as UserDashboardName
   |                                    ^^^^^^^^^^^^^^^^^ F401
15 |
16 | Name = 'CloudM'
   |
   = help: Remove unused import: `.UserDashboard.Name`

toolboxv2\mods\DB\__init__.py:1:23: F401 `toolboxv2.get_logger` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from toolboxv2 import get_logger
  |                       ^^^^^^^^^^ F401
2 |
3 | from .tb_adapter import Tools
  |
  = help: Use an explicit re-export: `get_logger as get_logger`

toolboxv2\mods\DB\__init__.py:4:34: F401 `.tb_adapter.Tools` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
3 | from .tb_adapter import Tools
4 | from .tb_adapter import Tools as DB
  |                                  ^^ F401
5 |
6 | Name = "DB"
  |
  = help: Use an explicit re-export: `Tools as Tools`

toolboxv2\mods\DB\local_instance.py:27:29: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
25 |         if os.path.exists(os.path.join(location, 'MiniDictDB.json')):
26 |             try:
27 |                 self.data = eval(Code.decrypt_symmetric(load_from_json(os.path.join(location, 'MiniDictDB.json')).get('data'), key))
   |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
28 |             except Exception as er:
29 |                 print(f"Data is corrupted error : {er}")
   |

toolboxv2\mods\DB\tb_adapter.py:90:25: UP007 Use `X | Y` for type annotations
   |
88 |         self.encoding = 'utf-8'
89 |
90 |         self.data_base: Optional[MiniRedis , MiniDictDB , DB , None] = None
   |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
91 |         self.mode = DatabaseModes.crate(
92 |             os.getenv("DB_MODE_KEY", "LC") if 'test' not in get_app("DB_MODE_KEY").id else os.getenv("DB_MODE_KEY_TEST",
   |
   = help: Convert to `X | Y`

toolboxv2\mods\EventManager\__init__.py:1:27: F401 `.module.Tools` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .module import Name, Tools, version
  |                           ^^^^^ F401
2 |
3 | Name = Name
  |
  = help: Use an explicit re-export: `Tools as Tools`

toolboxv2\mods\EventManager\module.py:375:23: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
373 |             if id_ != "new_con" and 'id' in data:
374 |                 id_data = data.get('id')
375 |                 id_ = eval(id_)
    |                       ^^^^^^^^^ S307
376 |                 c_host, c_pot = id_
377 |                 print(f"Registering: new client {id_data} : {c_host, c_pot}")
    |

toolboxv2\mods\EventManager\module.py:389:19: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
388 |         if isinstance(id_, str):
389 |             id_ = eval(id_)
    |                   ^^^^^^^^^ S307
390 |
391 |         c_name = self.routes.get(id_)
    |

toolboxv2\mods\EventManager\module.py:480:44: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
478 |             await self.add_server_route(self.source_id, ('0.0.0.0', os.getenv("TOOLBOXV2_REMOTE_PORT", 6587)))
479 |
480 |     async def connect_to_remote(self, host=os.getenv("TOOLBOXV2_REMOTE_IP"),
    |                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
481 |                                 port=os.getenv("TOOLBOXV2_REMOTE_PORT", 6587)):
482 |         await self.add_client_route("S0", (host, port))
    |

toolboxv2\mods\EventManager\module.py:481:38: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
480 |     async def connect_to_remote(self, host=os.getenv("TOOLBOXV2_REMOTE_IP"),
481 |                                 port=os.getenv("TOOLBOXV2_REMOTE_PORT", 6587)):
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
482 |         await self.add_client_route("S0", (host, port))
    |

toolboxv2\mods\EventManager\module.py:595:20: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
594 |         if event.source_types.name is SourceTypes.S.name:
595 |             return eval(event.source, __locals={'app': get_app(str(event_id)), 'event': event, 'eventManagerC': self})
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
596 |
597 |     async def routing_function_router(self, event_id: EventID):
    |

toolboxv2\mods\FastApi\__init__.py:1:31: F401 `.manager.Tools` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .manager import VERSION, Tools
  |                               ^^^^^ F401
2 |
3 | Name = "FastApi"
  |
  = help: Use an explicit re-export: `Tools as Tools`

toolboxv2\mods\FastApi\fast_api_install.py:378:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
377 |         except Exception as e:
378 |             raise HTTPException(status_code=500, detail=f"There was an error uploading the file: {str(e)}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
379 |
380 |         finally:
    |

toolboxv2\mods\FastApi\fast_api_main.py:704:11: F811 Redefinition of unused `index` from line 689
    |
703 | @app.get("/tauri")
704 | async def index():
    |           ^^^^^ F811
705 |     return serve_app_func("/web/assets/widgetControllerLogin.html")
    |
    = help: Remove definition: `index`

toolboxv2\mods\FastApi\fast_api_main.py:709:11: F811 Redefinition of unused `index` from line 704
    |
708 | @app.get("/favicon.ico")
709 | async def index():
    |           ^^^^^ F811
710 |     return serve_app_func('/web/favicon.ico')
711 |     # return "Willkommen bei Simple V0 powered by ToolBoxV2-0.0.3"
    |
    = help: Remove definition: `index`

toolboxv2\mods\FastApi\fast_api_main.py:805:11: F811 Redefinition of unused `login_page` from line 800
    |
804 | @app.get("/web/logout")
805 | async def login_page(access_allowed: bool = Depends(lambda: check_access_level(0))):
    |           ^^^^^^^^^^ F811
806 |     return serve_app_func('web/assets/logout.html')
    |
    = help: Remove definition: `login_page`

toolboxv2\mods\FastApi\fast_api_main.py:862:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
860 |               except httpx.RequestError as e:
861 |                   # More specific error handling for network-related issues
862 | /                 raise HTTPException(
863 | |                     status_code=500,
864 | |                     detail=f"Request failed: {str(e)}"
865 | |                 )
    | |_________________^ B904
866 |               except httpx.HTTPStatusError as e:
867 |                   # Handle HTTP error status codes
    |

toolboxv2\mods\FastApi\fast_api_main.py:868:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
866 |               except httpx.HTTPStatusError as e:
867 |                   # Handle HTTP error status codes
868 | /                 raise HTTPException(
869 | |                     status_code=e.response.status_code,
870 | |                     detail=f"HTTP error: {str(e)}"
871 | |                 )
    | |_________________^ B904
872 |
873 |       except Exception as e:
    |

toolboxv2\mods\FastApi\fast_api_main.py:875:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
873 |       except Exception as e:
874 |           # Catch-all error handling with more detailed logging
875 | /         raise HTTPException(
876 | |             status_code=500,
877 | |             detail=f"Unexpected error in webhook forwarding: {str(e)}"
878 | |         )
    | |_________^ B904
879 |
880 |   @app.api_route("/whatsappHook/{port}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
    |

toolboxv2\mods\FastApi\fast_api_main.py:885:11: F811 Redefinition of unused `startup_event` from line 788
    |
884 | @app.on_event("startup")
885 | async def startup_event():
    |           ^^^^^^^^^^^^^ F811
886 |     print('Server started :', __name__, datetime.now())
    |
    = help: Remove definition: `startup_event`

toolboxv2\mods\FastApi\fast_api_main.py:1055:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     |
1054 |             except fastapi.exceptions.FastAPIError as e:
1055 |                 raise SyntaxError(f"fuction '{function_name}' prove the signature error {e}")
     |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
1056 |         if add:
1057 |             app.include_router(router)
     |

toolboxv2\mods\FastApi\fast_lit.py:109:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
107 |                 headers['Authorization'] = f'Bearer {session_token}'
108 |             except jwt.InvalidTokenError:
109 |                 raise ValueError("Invalid session token")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
110 |
111 |         async with httpx.AsyncClient() as client:
    |

toolboxv2\mods\FastApi\fast_nice.py:44:30: SIM115 Use a context manager for opening files
   |
42 |         self.endpoints: list[UIEndpoint] = []
43 |
44 |         self.helper_contex = open("./dist/helper.html", encoding="utf-8").read()
   |                              ^^^^ SIM115
45 |
46 |         self.app.add_middleware(BaseHTTPMiddleware, dispatch=self.middleware_dispatch)
   |

toolboxv2\mods\FastApi\fast_nice.py:140:21: S102 Use of `exec` detected
    |
138 |                     setup_code = code_editor.value
139 |                     setup_namespace = {}
140 |                     exec(setup_code, {'ui': ui}, setup_namespace)
    |                     ^^^^ S102
141 |                     setup_func = setup_namespace['setup_gui']
    |

toolboxv2\mods\FastApi\fast_nice.py:348:13: SIM102 Use a single `if` statement instead of nested `if` statements
    |
346 |                   await self.handle_ws_message(session_id, gui_id, data)
347 |           except WebSocketDisconnect:
348 | /             if session_id in self.ws_connections:
349 | |                 if gui_id in self.ws_connections[session_id]:
    | |_____________________________________________________________^ SIM102
350 |                       del self.ws_connections[session_id][gui_id]
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\FastApi\manager.py:237:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
235 |         if not os.path.exists(node_modules_path):
236 |             self.logger.info("Node modules folder not found. Installing dependencies in '%s'", node_modules_path)
237 |             os.system("npm install --prefix ./web ./web")
    |             ^^^^^^^^^ S605
238 |
239 |         # Build the uvicorn command.
    |

toolboxv2\mods\FastApi\manager.py:340:21: S605 Starting a process with a shell, possible injection detected
    |
338 |             try:
339 |                 if system() == "Windows":
340 |                     os.system(f"taskkill /pid {api_pid} /F")
    |                     ^^^^^^^^^ S605
341 |                 else:
342 |                     os.kill(api_pid, signal.SIGKILL)
    |

toolboxv2\mods\FileWidget.py:73:13: S112 `try`-`except`-`continue` detected, consider logging the exception
   |
71 |                   elif disposition.get('name') == 'totalChunks':
72 |                       total_chunks = int(content.strip(b'\r\n'))
73 | /             except:
74 | |                 continue
   | |________________________^ S112
75 |
76 |           return ChunkInfo(
   |

toolboxv2\mods\FileWidget.py:660:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
658 |         folder_list = []
659 |         for blob_id in blob_ids:
660 |             blob_data = pickle.loads(storage.read_blob(blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
661 |             folder_list.extend(flatten_dict(blob_data, blob_id, '/').keys())
    |

toolboxv2\mods\MinimalHtml.py:117:17: B007 Loop control variable `i` not used within loop body
    |
115 |             template = string.Template(template_content)
116 |             html_element = '<h1> invalid Template </h1>'
117 |             for i in range(len(template_content)):
    |                 ^ B007
118 |                 try:
119 |                     html_element = template.substitute(**element['kwargs'])
    |
    = help: Rename unused `i` to `_i`

toolboxv2\mods\POA\__init__.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | from .module import MOD_NAME, VERSION
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
2 | version = VERSION
3 | Name = MOD_NAME
  |
  = help: Organize imports

toolboxv2\mods\POA\module.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / import json
2 | | import time
3 | | import uuid  # Für eindeutige IDs
4 | | from typing import Dict, Optional, List, Any
5 | |
6 | | from toolboxv2 import get_app, App, RequestData, Result  # Annahme: RequestData und Result sind korrekt importiert
  | |_______________________________________________________^ I001
7 |
8 |   # Moduldefinition
  |
  = help: Organize imports

toolboxv2\mods\POA\module.py:4:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
2 | import time
3 | import uuid  # Für eindeutige IDs
4 | from typing import Dict, Optional, List, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
5 |
6 | from toolboxv2 import get_app, App, RequestData, Result  # Annahme: RequestData und Result sind korrekt importiert
  |

toolboxv2\mods\POA\module.py:4:1: UP035 `typing.List` is deprecated, use `list` instead
  |
2 | import time
3 | import uuid  # Für eindeutige IDs
4 | from typing import Dict, Optional, List, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
5 |
6 | from toolboxv2 import get_app, App, RequestData, Result  # Annahme: RequestData und Result sind korrekt importiert
  |

toolboxv2\mods\POA\module.py:17:35: UP007 [*] Use `X | Y` for type annotations
   |
15 | # In einer echten Anwendung würde man den Benutzernamen dynamisch aus der Session/RequestData holen
16 | # Für SPP1 verwenden wir einen festen Benutzernamen oder einen, der per Query-Parameter kommt.
17 | def get_current_username(request: Optional[RequestData] = None) -> str:
   |                                   ^^^^^^^^^^^^^^^^^^^^^ UP007
18 |     if request:
19 |         return request.session.user_name
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:23:75: UP006 [*] Use `list` instead of `List` for type annotation
   |
23 | async def get_user_data(app: App, username: str, data_key_prefix: str) -> List[Dict[str, Any]]:
   |                                                                           ^^^^ UP006
24 |     db = app.get_mod("DB")  # Standard DB-Instanz
25 |     # db.edit_cli("RR") # Sicherstellen, dass der Read-Replica-Modus aktiv ist, falls nötig
   |
   = help: Replace with `list`

toolboxv2\mods\POA\module.py:23:80: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
23 | async def get_user_data(app: App, username: str, data_key_prefix: str) -> List[Dict[str, Any]]:
   |                                                                                ^^^^ UP006
24 |     db = app.get_mod("DB")  # Standard DB-Instanz
25 |     # db.edit_cli("RR") # Sicherstellen, dass der Read-Replica-Modus aktiv ist, falls nötig
   |
   = help: Replace with `dict`

toolboxv2\mods\POA\module.py:46:79: UP006 [*] Use `list` instead of `List` for type annotation
   |
46 | async def save_user_data(app: App, username: str, data_key_prefix: str, data: List[Dict[str, Any]]):
   |                                                                               ^^^^ UP006
47 |     db = app.get_mod("DB")
48 |     # db.edit_cli("RR") # oder einen spezifischen Schreibmodus, falls konfiguriert
   |
   = help: Replace with `list`

toolboxv2\mods\POA\module.py:46:84: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
46 | async def save_user_data(app: App, username: str, data_key_prefix: str, data: List[Dict[str, Any]]):
   |                                                                                    ^^^^ UP006
47 |     db = app.get_mod("DB")
48 |     # db.edit_cli("RR") # oder einen spezifischen Schreibmodus, falls konfiguriert
   |
   = help: Replace with `dict`

toolboxv2\mods\POA\module.py:60:40: UP007 [*] Use `X | Y` for type annotations
   |
58 | # --- API Endpunkte für Aufgaben ---
59 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
60 | async def get_tasks(app: App, request: Optional[RequestData] = None):
   |                                        ^^^^^^^^^^^^^^^^^^^^^ UP007
61 |     username = get_current_username(request)
62 |     tasks = await get_user_data(app, username, "poa_tasks")
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:67:45: UP007 [*] Use `X | Y` for type annotations
   |
66 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
67 | async def add_task(app: App, request, data: Optional[Dict[str, str]] = None, **kwargs):
   |                                             ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
68 |     username = get_current_username(request)
69 |     if not isinstance(data, dict):
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:67:54: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
66 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
67 | async def add_task(app: App, request, data: Optional[Dict[str, str]] = None, **kwargs):
   |                                                      ^^^^ UP006
68 |     username = get_current_username(request)
69 |     if not isinstance(data, dict):
   |
   = help: Replace with `dict`

toolboxv2\mods\POA\module.py:90:32: UP007 [*] Use `X | Y` for type annotations
   |
88 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['PUT'])
89 | async def toggle_task(app: App,
90 |                       request: Optional[RequestData] = None, **kwargs):  # Pfad könnte /api/POA/toggle_task?task_id=... sein
   |                                ^^^^^^^^^^^^^^^^^^^^^ UP007
91 |     username = get_current_username(request)
92 |     task_id = request.query_params.get("task_id") if request and request.query_params else None
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:114:32: UP007 [*] Use `X | Y` for type annotations
    |
112 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['DELETE'])
113 | async def delete_task(app: App,
114 |                       request: Optional[RequestData] = None, **kwargs):  # Pfad könnte /api/POA/delete_task?task_id=... sein
    |                                ^^^^^^^^^^^^^^^^^^^^^ UP007
115 |     username = get_current_username(request)
116 |     task_id = request.query_params.get("task_id") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:133:40: UP007 [*] Use `X | Y` for type annotations
    |
131 | # --- API Endpunkte für Notizen (ähnlich wie Aufgaben) ---
132 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
133 | async def get_notes(app: App, request: Optional[RequestData] = None, **kwargs):
    |                                        ^^^^^^^^^^^^^^^^^^^^^ UP007
134 |     username = get_current_username(request)
135 |     notes = await get_user_data(app, username, "poa_notes")
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:140:39: UP007 [*] Use `X | Y` for type annotations
    |
139 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
140 | async def add_note(app: App, request: Optional[RequestData] = None,data=None, **kwargs):
    |                                       ^^^^^^^^^^^^^^^^^^^^^ UP007
141 |     username = get_current_username(request)
142 |     if data and not request.body:
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:167:42: UP007 [*] Use `X | Y` for type annotations
    |
166 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['PUT'])
167 | async def update_note(app: App, request: Optional[RequestData] = None,data=None, **kwargs):
    |                                          ^^^^^^^^^^^^^^^^^^^^^ UP007
168 |     username = get_current_username(request)
169 |     note_id = request.query_params.get("note_id") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:195:42: UP007 [*] Use `X | Y` for type annotations
    |
194 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['DELETE'])
195 | async def delete_note(app: App, request: Optional[RequestData] = None, **kwargs):
    |                                          ^^^^^^^^^^^^^^^^^^^^^ UP007
196 |     username = get_current_username(request)
197 |     note_id = request.query_params.get("note_id") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:214:43: UP007 [*] Use `X | Y` for type annotations
    |
212 | # --- Hauptseite ---
213 | @export(mod_name=MOD_NAME, api=True, version=VERSION, name="main", api_methods=['GET'])  # Zugriff über /api/POA/
214 | async def get_poa_page(app: App, request: Optional[RequestData] = None):
    |                                           ^^^^^^^^^^^^^^^^^^^^^ UP007
215 |     # Diese Funktion liefert das Haupt-HTML für die POA-Anwendung
216 |     # Das HTML wird im nächsten Abschnitt definiert.
    |
    = help: Convert to `X | Y`

toolboxv2\mods\ProcessManager.py:20:23: S602 `subprocess` call with `shell=True` identified, security issue
   |
18 |         self.processes = []
19 |         for command in commands:
20 |             process = subprocess.Popen(command, shell=True)
   |                       ^^^^^^^^^^^^^^^^ S602
21 |             self.processes.append(process)
   |

toolboxv2\mods\SchedulerManager.py:174:17: S102 Use of `exec` detected
    |
172 |             with open(func) as file:
173 |                 func_code = file.read()
174 |                 exec(func_code)
    |                 ^^^^ S102
175 |                 func = locals()[object_name]
176 |         elif isinstance(func, str) and func.endswith('.dill') and safety_mode == 'open':
    |

toolboxv2\mods\SchedulerManager.py:185:17: S102 Use of `exec` detected
    |
183 |             local_vars = {'app': get_app(from_=Name + f".pasing.{object_name}")}
184 |             try:
185 |                 exec(func.strip(), {}, local_vars)
    |                 ^^^^ S102
186 |             except Exception as e:
187 |                 return Result.default_internal_error(f"Function parsing failed withe {e}")
    |

toolboxv2\mods\SocketManager.py:105:20: S113 Probable use of `requests` call without timeout
    |
103 | def get_public_ip():
104 |     try:
105 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^ S113
106 |         ip_address = response.json()['ip']
107 |         return ip_address
    |

toolboxv2\mods\SocketManager.py:251:20: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
249 |         self.logger.debug(f"Starting:{name} client on port {port} with host {host}")
250 |         sock = socket.socket(socket_type, socket.SOCK_STREAM)
251 |         time.sleep(random.choice(range(1, 100)) // 100)
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
252 |         if unix_file:
253 |             connection_error = sock.connect_ex(host)
    |

toolboxv2\mods\SocketManager.py:278:20: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
276 |         self.logger.debug(f"Starting:{name} client on port {port} with host {host}")
277 |
278 |         time.sleep(random.choice(range(1, 100)) // 100)
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
279 |         try:
280 |             if unix_file:
    |

toolboxv2\mods\SocketManager.py:438:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
436 |               f" max:{self.sockets[name]['max_connections']} connect on :{endpoint}")
437 |           self.sockets[name]["client_sockets_dict"][endpoint[0] + str(endpoint[1])] = client_socket
438 | /         if self.sockets[name]['max_connections'] != -1:
439 | |             if self.sockets[name]["connections"] >= self.sockets[name]['max_connections']:
    | |__________________________________________________________________________________________^ SIM102
440 |                   self.sockets[name]["running_dict"]["server_receiver"].set()
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\SocketManager.py:587:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
585 |                 except Exception as e:
586 |                     return Result.custom_error(data=str(e), data_info="Connection down and closed")
587 |         except ConnectionResetError and ConnectionAbortedError as e:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
588 |             self.print(f"Closing Receiver {name}:{identifier} {str(e)}")
589 |             self.sockets[name]["running_dict"]["receive"][identifier].set()
    |

toolboxv2\mods\SocketManager.py:612:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
610 |             self.logger.info(f"{name} -- received JSON -- {msg['identifier']}")
611 |             return msg
612 |         except json.JSONDecodeError and UnicodeDecodeError as e:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
613 |             self.logger.error(f"JSON decode error: {e}")
    |

toolboxv2\mods\TestWidget\__init__.py:1:25: F401 `.wiget_test.get_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .wiget_test import get_widget
  |                         ^^^^^^^^^^ F401
2 |
3 | Name = 'TestWidget'
  |
  = help: Use an explicit re-export: `get_widget as get_widget`

toolboxv2\mods\TestWidget\wiget_test.py:16:72: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
16 | def load_widget(app, display_name="Cud be ur name", cv=0, WidgetID=str(uuid.uuid4())[:4]):
   |                                                                        ^^^^^^^^^^^^ B008
17 |     # vars : $providerurl $WidgetID $root $$username
18 |     app.run_any(TBEF.MINIMALHTML.ADD_GROUP, command=Name)
   |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:23:28: F811 Redefinition of unused `TextSplitter` from line 16
   |
21 | from .research_processor import ResearchProcessor
22 | from .sources.arxiv_source import Paper, search_papers
23 | from .text_splitter import TextSplitter
   |                            ^^^^^^^^^^^^ F811
   |
   = help: Remove definition: `TextSplitter`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:26:7: F811 Redefinition of unused `RobustPDFDownloader` from line 18
   |
26 | class RobustPDFDownloader:
   |       ^^^^^^^^^^^^^^^^^^^ F811
27 |     def __init__(self, max_retries=5, backoff_factor=0.3,
28 |                  download_dir='downloads',
   |
   = help: Remove definition: `RobustPDFDownloader`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:61:24: F821 Undefined name `HTTPAdapter`
   |
59 |             backoff_factor=backoff_factor
60 |         )
61 |         self.adapter = HTTPAdapter(max_retries=self.retry_strategy)
   |                        ^^^^^^^^^^^ F821
62 |
63 |         # Create session with retry mechanism
   |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:64:24: F821 Undefined name `requests`
   |
63 |         # Create session with retry mechanism
64 |         self.session = requests.Session()
   |                        ^^^^^^^^ F821
65 |         self.session.mount("https://", self.adapter)
66 |         self.session.mount("http://", self.adapter)
   |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:99:16: F821 Undefined name `requests`
    |
 97 |             return file_path
 98 |
 99 |         except requests.exceptions.RequestException as e:
    |                ^^^^^^^^ F821
100 |             self.logger.error(f"Download failed for {url}: {e}")
101 |             raise
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:116:26: F821 Undefined name `PyPDF2`
    |
114 |             page_texts = []
115 |             with open(pdf_path, 'rb') as file:
116 |                 reader = PyPDF2.PdfReader(file)
    |                          ^^^^^^ F821
117 |                 for page_num, page in enumerate(reader.pages, 1):
118 |                     text = page.extract_text()
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:143:26: F821 Undefined name `PyPDF2`
    |
141 |         try:
142 |             with open(pdf_path, 'rb') as file:
143 |                 reader = PyPDF2.PdfReader(file)
    |                          ^^^^^^ F821
144 |
145 |                 for page_num, page in enumerate(reader.pages, 1):
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:149:35: F821 Undefined name `Image`
    |
147 |                         for img_index, image in enumerate(page.images):
148 |                             img_data = image.data
149 |                             img = Image.open(io.BytesIO(img_data))
    |                                   ^^^^^ F821
150 |
151 |                             img_filename = f'page_{page_num}_img_{img_index}.png'
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:149:46: F821 Undefined name `io`
    |
147 |                         for img_index, image in enumerate(page.images):
148 |                             img_data = image.data
149 |                             img = Image.open(io.BytesIO(img_data))
    |                                              ^^ F821
150 |
151 |                             img_filename = f'page_{page_num}_img_{img_index}.png'
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:178:7: F811 Redefinition of unused `Paper` from line 22
    |
176 |     relevance_score: float = 0.0
177 |
178 | class Paper(BaseModel):
    |       ^^^^^ F811
179 |     title: str
180 |     summary: str
    |
    = help: Remove definition: `Paper`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:190:5: F811 Redefinition of unused `search_papers` from line 22
    |
188 |     key_sections: list[str] = Field(default_factory=list)
189 |
190 | def search_papers(query: str, max_results=10) -> list[Paper]:
    |     ^^^^^^^^^^^^^ F811
191 |     search = arxiv.Search(
192 |         query=query,
    |
    = help: Remove definition: `search_papers`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:191:14: F821 Undefined name `arxiv`
    |
190 | def search_papers(query: str, max_results=10) -> list[Paper]:
191 |     search = arxiv.Search(
    |              ^^^^^ F821
192 |         query=query,
193 |         max_results=max_results,
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:194:17: F821 Undefined name `arxiv`
    |
192 |         query=query,
193 |         max_results=max_results,
194 |         sort_by=arxiv.SortCriterion.Relevance
    |                 ^^^^^ F821
195 |     )
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:198:19: F821 Undefined name `arxiv`
    |
197 |     results = []
198 |     for result in arxiv.Client().results(search):
    |                   ^^^^^ F821
199 |         paper = Paper(
200 |             title=result.title,
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:453:10: F821 Undefined name `Spinner`
    |
451 | async def main(query: str = "Beste strategien in bretspielen sitler von katar"):
452 |     """Main execution function"""
453 |     with Spinner("Init Isaa"):
    |          ^^^^^^^ F821
454 |         tools = get_app("ArXivPDFProcessor", name=None).get_mod("isaa")
455 |         tools.init_isaa(build=True)
    |

toolboxv2\mods\TruthSeeker\module.py:17:11: SIM115 Use a context manager for opening files
   |
16 | dot = os.path.dirname(os.path.abspath(__file__))
17 | content = open(os.path.join(dot,"template.html"), encoding="utf-8").read()
   |           ^^^^ SIM115
18 | abut_content = open(os.path.join(dot,"abut.html"), encoding="utf-8").read()
   |

toolboxv2\mods\TruthSeeker\module.py:18:16: SIM115 Use a context manager for opening files
   |
16 | dot = os.path.dirname(os.path.abspath(__file__))
17 | content = open(os.path.join(dot,"template.html"), encoding="utf-8").read()
18 | abut_content = open(os.path.join(dot,"abut.html"), encoding="utf-8").read()
   |                ^^^^ SIM115
19 |
20 | code_templates = {
   |

toolboxv2\mods\TruthSeeker\module.py:227:5: SIM102 Use a single `if` statement instead of nested `if` statements
    |
225 |       if abut:
226 |           return HTMLResponse(content=abut_content)
227 | /     if hasattr(request, 'row'):
228 | |         if sid := request.row.query_params.get('session_id'):
    | |_____________________________________________________________^ SIM102
229 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
230 |       if hasattr(request, 'query_params'):
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\TruthSeeker\module.py:230:5: SIM102 Use a single `if` statement instead of nested `if` statements
    |
228 |           if sid := request.row.query_params.get('session_id'):
229 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
230 | /     if hasattr(request, 'query_params'):
231 | |         if sid := request.query_params.get('session_id'):
    | |_________________________________________________________^ SIM102
232 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
233 |       return RedirectResponse(url="/gui/open-Seeker")
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\TruthSeeker\one.py:131:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
129 |             import torchaudio
130 |         except ImportError:
131 |             raise ValueError("Couldn't load audio install torchaudio'")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
132 |         # Handle different audio input types
133 |         if isinstance(audio_data, str):
    |

toolboxv2\mods\TruthSeeker\one.py:306:21: S112 `try`-`except`-`continue` detected, consider logging the exception
    |
304 |                           if chunk_embedding:
305 |                               chunk_embeddings.append(chunk_embedding)
306 | /                     except Exception:
307 | |                         continue
    | |________________________________^ S112
308 |                       try:
309 |                           input_data = InputData(chunk[len(chunk)//2:], modality)
    |

toolboxv2\mods\TruthSeeker\one.py:315:29: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
313 |                     except Exception:
314 |                         if len(chunk_embeddings) == 0 and chunks.index(chunk) == len(chunks)-1:
315 |                             raise e
    |                             ^^^^^^^ B904
    |

toolboxv2\mods\TruthSeeker\tests.py:124:9: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
123 |           # Mock PDF download and processing
124 | /         with patch.object(RobustPDFDownloader, 'download_pdf') as mock_download:
125 | |             with patch.object(RobustPDFDownloader, 'extract_text_from_pdf') as mock_extract:
    | |____________________________________________________________________________________________^ SIM117
126 |                   mock_download.return_value = "test.pdf"
127 |                   mock_extract.return_value = [{'page_number': 1, 'text': 'test content'}]
    |
    = help: Combine `with` statements

toolboxv2\mods\TruthSeeker\tests.py:171:9: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
169 |       def test_process_empty_results(self):
170 |           # Test processing with no results
171 | /         with patch.object(ArXivPDFProcessor, 'generate_queries') as mock_generate:
172 | |             with patch.object(ArXivPDFProcessor, 'search_and_process_papers') as mock_search:
    | |_____________________________________________________________________________________________^ SIM117
173 |                   mock_generate.return_value = ["test query"]
174 |                   mock_search.return_value = []
    |
    = help: Combine `with` statements

toolboxv2\mods\TruthSeeker\tests.py:194:9: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
192 |           mock_search.return_value.results.return_value = [mock_result]
193 |
194 | /         with patch.object(RobustPDFDownloader, 'download_pdf') as mock_download:
195 | |             with patch.object(RobustPDFDownloader, 'extract_text_from_pdf') as mock_extract:
    | |____________________________________________________________________________________________^ SIM117
196 |                   with patch.object(self.mock_tools, 'format_class') as mock_format_class:
197 |                       mock_download.return_value = "test.pdf"
    |
    = help: Combine `with` statements

toolboxv2\mods\WebSocketManager.py:317:49: S105 Possible hardcoded password assigned to: "token"
    |
315 |                         return '{"res": "No User Instance Found"}'
316 |
317 |                     if data['data']['token'] == "**SelfAuth**":
    |                                                 ^^^^^^^^^^^^^^ S105
318 |                         data['data']['token'] = user_instance['token']
    |

toolboxv2\mods\WebSocketManager.py:392:49: S105 Possible hardcoded password assigned to: "token"
    |
390 |                         return '{"res": "No User Instance Found pleas log in"}'
391 |
392 |                     if data['data']['token'] == "**SelfAuth**":
    |                                                 ^^^^^^^^^^^^^^ S105
393 |                         data['data']['token'] = user_instance['token']
    |

toolboxv2\mods\WebSocketManager.py:432:21: SIM102 Use a single `if` statement instead of nested `if` statements
    |
430 |                           res = "Mod Error " + str(e)
431 |
432 | /                     if type(res) == str:
433 | |                         if (res.startswith('{') or res.startswith('[')) or res.startswith('"[') or res.startswith('"{') \
434 | |                             or res.startswith('\"[') or res.startswith('\"{') or res.startswith(
435 | |                             'b"[') or res.startswith('b"{'): \
    | |____________________________________________________________^ SIM102
436 |                               res = eval(res)
437 |                       if not isinstance(res, dict):
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\WebSocketManager.py:436:35: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
434 |                             or res.startswith('\"[') or res.startswith('\"{') or res.startswith(
435 |                             'b"[') or res.startswith('b"{'): \
436 |                             res = eval(res)
    |                                   ^^^^^^^^^ S307
437 |                     if not isinstance(res, dict):
438 |                         res = {"res": res, data['name']: True}
    |

toolboxv2\mods\WhatsAppTb\client.py:145:22: F821 Undefined name `Agent`
    |
143 |     whc: WhClient
144 |     isaa: 'Tools'
145 |     agent: Optional['Agent'] = None
    |                      ^^^^^ F821
146 |     credentials: Credentials | None = None
147 |     state: AssistantState = AssistantState.OFFLINE
    |

toolboxv2\mods\WhatsAppTb\client.py:1407:21: SIM115 Use a context manager for opening files
     |
1405 |                 filename = f"file_{file_type}_{datetime.now().isoformat()}_{content.get('sha256', '')}"
1406 |                 blob_id = self.blob_docs_system.save_document(
1407 |                     open(media_data, 'rb').read(),
     |                     ^^^^ SIM115
1408 |                     filename=filename,
1409 |                     file_type=file_type
     |

toolboxv2\mods\WhatsAppTb\server.py:54:9: S110 `try`-`except`-`pass` detected, consider logging the exception
   |
52 |                   signal.signal(signal.SIGINT, self.signal_handler)
53 |                   signal.signal(signal.SIGTERM, self.signal_handler)
54 | /         except Exception:
55 | |             pass
   | |________________^ S110
56 |
57 |       def offline(self, instance_id):
   |

toolboxv2\mods\WhatsAppTb\server.py:211:22: F821 Undefined name `ui`
    |
209 | …     """Interactive instance control card"""
210 | …     config = self.instances[instance_id]
211 | …     with ui.card().classes('w-full p-4 mb-4 bg-gray-50 dark:bg-gray-800').style("background-color: var(--background-color) !importa…
    |            ^^ F821
212 | …         # Header Section
213 | …         with ui.row().classes('w-full justify-between items-center'):
    |

toolboxv2\mods\WhatsAppTb\server.py:213:26: F821 Undefined name `ui`
    |
211 | …     with ui.card().classes('w-full p-4 mb-4 bg-gray-50 dark:bg-gray-800').style("background-color: var(--background-color) !importa…
212 | …         # Header Section
213 | …         with ui.row().classes('w-full justify-between items-center'):
    |                ^^ F821
214 | …             ui.label(f'📱 {instance_id}').classes('text-xl font-bold')
    |

toolboxv2\mods\WhatsAppTb\server.py:214:25: F821 Undefined name `ui`
    |
212 |                     # Header Section
213 |                     with ui.row().classes('w-full justify-between items-center'):
214 |                         ui.label(f'📱 {instance_id}').classes('text-xl font-bold')
    |                         ^^ F821
215 |
216 |                         # Status Indicator
    |

toolboxv2\mods\WhatsAppTb\server.py:217:25: F821 Undefined name `ui`
    |
216 |                         # Status Indicator
217 |                         ui.label().bind_text_from(
    |                         ^^ F821
218 |                             self.threads, instance_id,
219 |                             lambda x: 'Running' if x and x.is_alive() else 'Stopped'
    |

toolboxv2\mods\WhatsAppTb\server.py:223:26: F821 Undefined name `ui`
    |
222 |                     # Configuration Display
223 |                     with ui.grid(columns=2).classes('w-full mt-4 gap-2'):
    |                          ^^ F821
224 |
225 |                         ui.label('port:').classes('font-bold')
    |

toolboxv2\mods\WhatsAppTb\server.py:225:25: F821 Undefined name `ui`
    |
223 |                     with ui.grid(columns=2).classes('w-full mt-4 gap-2'):
224 |
225 |                         ui.label('port:').classes('font-bold')
    |                         ^^ F821
226 |                         ui.label(config['port'])
    |

toolboxv2\mods\WhatsAppTb\server.py:226:25: F821 Undefined name `ui`
    |
225 |                         ui.label('port:').classes('font-bold')
226 |                         ui.label(config['port'])
    |                         ^^ F821
227 |
228 |                         ui.label('Last Activity:').classes('font-bold')
    |

toolboxv2\mods\WhatsAppTb\server.py:228:25: F821 Undefined name `ui`
    |
226 |                         ui.label(config['port'])
227 |
228 |                         ui.label('Last Activity:').classes('font-bold')
    |                         ^^ F821
229 |                         ui.label().bind_text_from(
230 |                             self.last_messages, instance_id,
    |

toolboxv2\mods\WhatsAppTb\server.py:229:25: F821 Undefined name `ui`
    |
228 |                         ui.label('Last Activity:').classes('font-bold')
229 |                         ui.label().bind_text_from(
    |                         ^^ F821
230 |                             self.last_messages, instance_id,
231 |                             lambda x: x.strftime("%Y-%m-%d %H:%M:%S") if x else 'Never'
    |

toolboxv2\mods\WhatsAppTb\server.py:235:26: F821 Undefined name `ui`
    |
234 |                     # Action Controls
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
    |                          ^^ F821
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
237 |                             ui.tooltip('Configure')
    |

toolboxv2\mods\WhatsAppTb\server.py:236:30: F821 Undefined name `ui`
    |
234 |                     # Action Controls
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
    |                              ^^ F821
237 |                             ui.tooltip('Configure')
    |

toolboxv2\mods\WhatsAppTb\server.py:237:29: F821 Undefined name `ui`
    |
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
237 |                             ui.tooltip('Configure')
    |                             ^^ F821
238 |
239 |                         with ui.button(icon='refresh', color='orange',
    |

toolboxv2\mods\WhatsAppTb\server.py:239:30: F821 Undefined name `ui`
    |
237 |                             ui.tooltip('Configure')
238 |
239 |                         with ui.button(icon='refresh', color='orange',
    |                              ^^ F821
240 |                                        on_click=lambda: self.restart_instance(instance_id)):
241 |                             ui.tooltip('Restart')
    |

toolboxv2\mods\WhatsAppTb\server.py:241:29: F821 Undefined name `ui`
    |
239 |                         with ui.button(icon='refresh', color='orange',
240 |                                        on_click=lambda: self.restart_instance(instance_id)):
241 |                             ui.tooltip('Restart')
    |                             ^^ F821
242 |
243 |                         with ui.button(icon='stop', color='red',
    |

toolboxv2\mods\WhatsAppTb\server.py:243:30: F821 Undefined name `ui`
    |
241 |                             ui.tooltip('Restart')
242 |
243 |                         with ui.button(icon='stop', color='red',
    |                              ^^ F821
244 |                                        on_click=lambda: self.stop_instance(instance_id)):
245 |                             ui.tooltip('Stop')
    |

toolboxv2\mods\WhatsAppTb\server.py:245:29: F821 Undefined name `ui`
    |
243 |                         with ui.button(icon='stop', color='red',
244 |                                        on_click=lambda: self.stop_instance(instance_id)):
245 |                             ui.tooltip('Stop')
    |                             ^^ F821
246 |
247 |                     # Edit Configuration Dialog
    |

toolboxv2\mods\WhatsAppTb\server.py:248:26: F821 Undefined name `ui`
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
    |                          ^^ F821
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

toolboxv2\mods\WhatsAppTb\server.py:248:54: F821 Undefined name `ui`
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
    |                                                      ^^ F821
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

toolboxv2\mods\WhatsAppTb\server.py:249:35: F821 Undefined name `ui`
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
    |                                   ^^ F821
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

toolboxv2\mods\WhatsAppTb\server.py:250:38: F821 Undefined name `ui`
    |
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |                                      ^^ F821
251 |
252 |                         with ui.row().classes('w-full justify-end'):
    |

toolboxv2\mods\WhatsAppTb\server.py:252:30: F821 Undefined name `ui`
    |
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
251 |
252 |                         with ui.row().classes('w-full justify-end'):
    |                              ^^ F821
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
254 |                             ui.button('Save', color='primary', on_click=lambda: (
    |

toolboxv2\mods\WhatsAppTb\server.py:253:29: F821 Undefined name `ui`
    |
252 |                         with ui.row().classes('w-full justify-end'):
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
    |                             ^^ F821
254 |                             ui.button('Save', color='primary', on_click=lambda: (
255 |                                 self.update_instance_config(
    |

toolboxv2\mods\WhatsAppTb\server.py:254:29: F821 Undefined name `ui`
    |
252 |                         with ui.row().classes('w-full justify-end'):
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
254 |                             ui.button('Save', color='primary', on_click=lambda: (
    |                             ^^ F821
255 |                                 self.update_instance_config(
256 |                                     instance_id,
    |

toolboxv2\mods\WhatsAppTb\server.py:264:18: F821 Undefined name `ui`
    |
263 |             # Main UI Layout
264 |             with ui.column().classes('w-full max-w-4xl mx-auto p-4'):
    |                  ^^ F821
265 |                 ui.label('WhatsApp Instance Manager').classes('text-2xl font-bold mb-6')
    |

toolboxv2\mods\WhatsAppTb\server.py:265:17: F821 Undefined name `ui`
    |
263 |             # Main UI Layout
264 |             with ui.column().classes('w-full max-w-4xl mx-auto p-4'):
265 |                 ui.label('WhatsApp Instance Manager').classes('text-2xl font-bold mb-6')
    |                 ^^ F821
266 |
267 |                 # Add Instance Section
    |

toolboxv2\mods\WhatsAppTb\server.py:268:17: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
267 |                   # Add Instance Section
268 | /                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 | |                     with ui.card().classes('w-full p-4 mt-2'):
    | |______________________________________________________________^ SIM117
270 |                           instance_id = ui.input('Instance ID').classes('w-full')
271 |                           token = ui.input('API Token').classes('w-full')
    |
    = help: Combine `with` statements

toolboxv2\mods\WhatsAppTb\server.py:268:22: F821 Undefined name `ui`
    |
267 |                 # Add Instance Section
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
    |                      ^^ F821
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:269:26: F821 Undefined name `ui`
    |
267 |                 # Add Instance Section
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 |                     with ui.card().classes('w-full p-4 mt-2'):
    |                          ^^ F821
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:270:39: F821 Undefined name `ui`
    |
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
    |                                       ^^ F821
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:271:33: F821 Undefined name `ui`
    |
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
    |                                 ^^ F821
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:272:37: F821 Undefined name `ui`
    |
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
    |                                     ^^ F821
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:273:40: F821 Undefined name `ui`
    |
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |                                        ^^ F821
274 |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
    |

toolboxv2\mods\WhatsAppTb\server.py:275:30: F821 Undefined name `ui`
    |
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
274 |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
    |                              ^^ F821
276 |                             ui.button('Clear', on_click=lambda: (
277 |                                 instance_id.set_value(''),
    |

toolboxv2\mods\WhatsAppTb\server.py:276:29: F821 Undefined name `ui`
    |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
276 |                             ui.button('Clear', on_click=lambda: (
    |                             ^^ F821
277 |                                 instance_id.set_value(''),
278 |                                 token.set_value(''),
    |

toolboxv2\mods\WhatsAppTb\server.py:282:29: F821 Undefined name `ui`
    |
280 | …                         phone_number.set_value('')
281 | …                     ))
282 | …                     ui.button('Create', color='positive', on_click=lambda: (
    |                       ^^ F821
283 | …                         self.add_update_instance(
284 | …                             instance_id.value,
    |

toolboxv2\mods\WhatsAppTb\server.py:293:39: F821 Undefined name `ui`
    |
292 |                 # Instances Display
293 |                 instances_container = ui.column().classes('w-full')
    |                                       ^^ F821
294 |                 with instances_container:
295 |                     for instance_id in self.instances:
    |

toolboxv2\mods\WidgetsProvider\__init__.py:1:42: F401 `.board_widget.BoardWidget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .board_widget import BoardWidget as Tools
  |                                          ^^^^^ F401
2 | from .module import open_widget
3 | from .StorageUtil import get_names
  |
  = help: Use an explicit re-export: `BoardWidget as BoardWidget`

toolboxv2\mods\WidgetsProvider\__init__.py:2:21: F401 `.module.open_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .board_widget import BoardWidget as Tools
2 | from .module import open_widget
  |                     ^^^^^^^^^^^ F401
3 | from .StorageUtil import get_names
  |
  = help: Use an explicit re-export: `open_widget as open_widget`

toolboxv2\mods\WidgetsProvider\__init__.py:3:26: F401 `.StorageUtil.get_names` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .board_widget import BoardWidget as Tools
2 | from .module import open_widget
3 | from .StorageUtil import get_names
  |                          ^^^^^^^^^ F401
4 |
5 | Name = 'WidgetsProvider'
  |
  = help: Use an explicit re-export: `get_names as get_names`

toolboxv2\mods\__init__.py:1:8: F401 `toolboxv2` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | import toolboxv2
  |        ^^^^^^^^^ F401
2 | from toolboxv2.utils.toolbox import get_app
  |
  = help: Use an explicit re-export: `toolboxv2 as toolboxv2`

toolboxv2\mods\__init__.py:2:37: F401 `toolboxv2.utils.toolbox.get_app` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | import toolboxv2
2 | from toolboxv2.utils.toolbox import get_app
  |                                     ^^^^^^^ F401
3 |
4 | BROWSER = 'chrome'  # firefox 'chrome'
  |
  = help: Use an explicit re-export: `get_app as get_app`

toolboxv2\mods\cli_functions.py:16:8: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
14 |     READCHAR = True
15 |     READCHAR_error = None
16 | except ImportError and ModuleNotFoundError:
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
17 |     READCHAR = False
   |

toolboxv2\mods\cli_functions.py:35:8: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
33 |     PROMPT_TOOLKIT = True
34 |     PROMPT_TOOLKIT_error = None
35 | except ImportError and ModuleNotFoundError:
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
36 |     PROMPT_TOOLKIT = False
   |

toolboxv2\mods\cli_functions.py:200:9: SIM113 Use `enumerate()` for index variable `i` in `for` loop
    |
198 |             else:
199 |                 as_list[i] = replacements[key]
200 |         i += 1
    |         ^^^^^^ SIM113
201 |     if not inlist:
202 |         return text
    |

toolboxv2\mods\cli_functions.py:384:28: B008 Do not perform function call `node` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
382 |                password=False,
383 |                bindings=None,
384 |                message=f"~{node()}@>", fh=None) -> CallingObject:
    |                            ^^^^^^ B008
385 |     if app is None:
386 |         app = get_app(from_="cliF.user_input")
    |

toolboxv2\mods\cli_functions.py:422:13: S605 Starting a process with a shell, possible injection detected
    |
420 |                 return
421 |             fh.append_string(buff)
422 |             os.system(buff)
    |             ^^^^^^^^^ S605
423 |
424 |         run_in_terminal(run_in_console)
    |

toolboxv2\mods\cli_functions.py:428:9: F811 Redefinition of unused `run_in_shell` from line 414
    |
427 |     @bindings.add('c-up')
428 |     def run_in_shell(event):
    |         ^^^^^^^^^^^^ F811
429 |         buff = event.st_router.current_buffer.text
    |
    = help: Remove definition: `run_in_shell`

toolboxv2\mods\cli_functions.py:440:26: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
439 |             try:
440 |                 result = eval(buff, app.globals['root'], app.locals['user'])
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
441 |                 if result is not None:
442 |                     print(f"+ {buff}\n#{app.locals['user']['counts']}>", result)
    |

toolboxv2\mods\cli_functions.py:446:17: S102 Use of `exec` detected
    |
444 |                     print(f"- {buff}\n#{app.locals['user']['counts']}>")
445 |             except SyntaxError:
446 |                 exec(buff, app.globals['root'], app.locals['user'])
    |                 ^^^^ S102
447 |                 print(f"* {buff}\n#{app.locals['user']['counts']}> Statement executed")
448 |             except Exception as e:
    |

toolboxv2\mods\cli_functions.py:469:21: S605 Starting a process with a shell, possible injection detected
    |
467 |                     print("Avalabel functions:", completer_dict[user_input_buffer_info[0]])
468 |                 else:
469 |                     os.system(f"{user_input_buffer_info[0]} --help")
    |                     ^^^^^^^^^ S605
470 |             if len(user_input_buffer_info) > 1:
471 |                 if user_input_buffer_info[0] in completer_dict:
    |

toolboxv2\mods\cli_functions.py:489:9: SIM115 Use a context manager for opening files
    |
488 |     if not os.path.exists(f'{app.data_dir}/{app.args_sto.modi}-cli.txt'):
489 |         open(f'{app.data_dir}/{app.args_sto.modi}-cli.txt', "a")
    |         ^^^^ SIM115
490 |
491 |     session = PromptSession(message=message,
    |

toolboxv2\mods\isaa\CodingAgent\live.py:795:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
793 |                 subprocess.run([sys.executable, "-m", "venv", str(self._venv_path)], check=True)
794 |             except subprocess.CalledProcessError as e:
795 |                 raise RuntimeError(f"Failed to create virtual environment: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
796 |
797 |     def _virtual_open(self, filepath, mode='r', *args, **kwargs):
    |

toolboxv2\mods\isaa\CodingAgent\live.py:806:21: SIM115 Use a context manager for opening files
    |
805 |         # Use actual filesystem but track in virtual fs
806 |         real_file = open(abs_path, mode, *args, **kwargs)
    |                     ^^^^ SIM115
807 |
808 |         if 'r' in mode:
    |

toolboxv2\mods\isaa\CodingAgent\live.py:981:33: S102 Use of `exec` detected
    |
979 | …                     try:
980 | …                         # Execute wrapped code and await it
981 | …                         exec(exec_code, self.user_ns)
    |                           ^^^^ S102
982 | …                         result = self.user_ns['__wrapper']()
983 | …                         if asyncio.iscoroutine(result):
    |

toolboxv2\mods\isaa\CodingAgent\live.py:989:29: S102 Use of `exec` detected
    |
987 |                         elif is_async:
988 |                             # Execute async code
989 |                             exec(exec_code, self.user_ns)
    |                             ^^^^ S102
990 |                             if eval_code:
991 |                                 result = eval(eval_code, self.user_ns)
    |

toolboxv2\mods\isaa\CodingAgent\live.py:991:42: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
989 | …                     exec(exec_code, self.user_ns)
990 | …                     if eval_code:
991 | …                         result = eval(eval_code, self.user_ns)
    |                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
992 | …                         if asyncio.iscoroutine(result):
993 | …                             result = await result
    |

toolboxv2\mods\isaa\CodingAgent\live.py:996:29: S102 Use of `exec` detected
    |
994 |                         else:
995 |                             # Execute sync code
996 |                             exec(exec_code, self.user_ns)
    |                             ^^^^ S102
997 |                             if eval_code:
998 |                                 result = eval(eval_code, self.user_ns)
    |

toolboxv2\mods\isaa\CodingAgent\live.py:998:42: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
 996 |                             exec(exec_code, self.user_ns)
 997 |                             if eval_code:
 998 |                                 result = eval(eval_code, self.user_ns)
     |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
 999 |
1000 |                         if result is not None:
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1042:21: B012 `return` inside `finally` blocks cause exceptions to be silenced
     |
1040 |                         self.vfs.delete_file(temp_file)
1041 |
1042 |                     return result
     |                     ^^^^^^^^^^^^^ B012
1043 |
1044 |         except Exception as e:
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1129:21: S102 Use of `exec` detected
     |
1127 |                     # Create a new function object from the code
1128 |                     method_locals = {}
1129 |                     exec(
     |                     ^^^^ S102
1130 |                         f"def _temp_func{signature(getattr(base_obj.__class__, attr_name, None))}: {method_ast.body[0].value.s}",
1131 |                         globals(), method_locals)
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1139:50: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1137 |                 else:
1138 |                     # For simple attributes
1139 |                     setattr(base_obj, attr_name, eval(code, self.user_ns))
     |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^ S307
1140 |                     result_message.append(f"Updated attribute '{clean_object_name}' in memory")
1141 |             else:
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1145:33: B005 Using `.strip()` with multi-character strings is misleading
     |
1143 |                 if code.startswith('"""') and code.endswith('"""'):
1144 |                     # Handle function definitions
1145 |                     func_code = code.strip('"""')
     |                                 ^^^^^^^^^^^^^^^^^ B005
1146 |                     func_ast = ast.parse(func_code).body[0]
1147 |                     func_name = func_ast.name
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1151:21: S102 Use of `exec` detected
     |
1149 |                     # Create a new function object from the code
1150 |                     func_locals = {}
1151 |                     exec(f"{func_code}", globals(), func_locals)
     |                     ^^^^ S102
1152 |                     self.user_ns[clean_object_name] = func_locals[func_name]
1153 |                     result_message.append(f"Updated function '{clean_object_name}' in memory")
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1156:55: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1154 |                 else:
1155 |                     # Simple variable assignment
1156 |                     self.user_ns[clean_object_name] = eval(code, self.user_ns)
     |                                                       ^^^^^^^^^^^^^^^^^^^^^^^^ S307
1157 |                     result_message.append(f"Updated variable '{clean_object_name}' in memory")
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1179:39: B005 Using `.strip()` with multi-character strings is misleading
     |
1178 |                     if code.startswith('"""') and code.endswith('"""'):
1179 |                         method_code = code.strip('"""')
     |                                       ^^^^^^^^^^^^^^^^^ B005
1180 |
1181 |                         # Use ast to parse the file and find the method to replace
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1205:37: B005 Using `.strip()` with multi-character strings is misleading
     |
1203 |                     if code.startswith('"""') and code.endswith('"""'):
1204 |                         # Handle function updates
1205 |                         func_code = code.strip('"""')
     |                                     ^^^^^^^^^^^^^^^^^ B005
1206 |                         func_pattern = fr"def {clean_object_name}.*?:(.*?)(?=\n\w|\Z)"
1207 |                         func_match = re.search(func_pattern, original_content, re.DOTALL)
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1281:32: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1279 |         if session_file.exists():
1280 |             with open(session_file, 'rb') as f:
1281 |                 session_data = pickle.load(f)
     |                                ^^^^^^^^^^^^^^ S301
1282 |                 # self.user_ns.update(session_data['user_ns'])
1283 |                 self.output_history.update(session_data['output_history'])
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1707:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     |
1705 |             if self.browser:
1706 |                 await self.browser.close()
1707 |             raise Exception(f"Failed to initialize browser: {str(e)}")
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
1708 |
1709 |     async def create_agent(self, task: str, initial_actions=None):
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1746:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     |
1744 |             return page
1745 |         except Exception as e:
1746 |             raise Exception(f"Failed to navigate to {url}: {str(e)}")
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
1747 |
1748 |     async def get_tabs(self):
     |

toolboxv2\mods\isaa\CodingAgent\live.py:2409:17: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
2407 |                                   args.append(f"{name}={param.default}")
2408 |                           desc_parts.append(f"**Init Args:** `{', '.join(args)}`")
2409 | /                 except:
2410 | |                     pass
     | |________________________^ S110
2411 |
2412 |                   # Instance state
     |

toolboxv2\mods\isaa\SearchAgentCluster\search_tool.py:91:36: B008 Do not perform function call `WebScraperConfig` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
89 |     def __init__(
90 |         self,
91 |         config: WebScraperConfig = WebScraperConfig(),
   |                                    ^^^^^^^^^^^^^^^^^^ B008
92 |         llm: str | BaseChatModel | None = None,
93 |         chrome_path: str | None = None,
   |

toolboxv2\mods\isaa\__init__.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | from .module import Tools, version
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
  |
  = help: Organize imports

toolboxv2\mods\isaa\base\AgentUtils.py:77:16: S113 Probable use of `requests` call without timeout
   |
76 | def get_ip():
77 |     response = requests.get('https://api64.ipify.org?format=json').json()
   |                ^^^^^^^^^^^^ S113
78 |     return response["ip"]
   |

toolboxv2\mods\isaa\base\AgentUtils.py:84:16: S113 Probable use of `requests` call without timeout
   |
82 | def get_location():
83 |     ip_address = get_ip()
84 |     response = requests.get(f'https://ipapi.co/{ip_address}/json/').json()
   |                ^^^^^^^^^^^^ S113
85 |     location_data = f"city: {response.get('city')},region: {response.get('region')},country: {response.get('country_name')},"
   |

toolboxv2\mods\isaa\base\AgentUtils.py:141:12: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
139 |         process = get_location()
140 |         info['location'], info['ip'] = process.result()
141 |     except TimeoutError and Exception:
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
142 |         info['location'] = "Berlin Schöneberg"
143 |     initialize_system_infos(info)
    |

toolboxv2\mods\isaa\base\AgentUtils.py:188:32: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
186 |                 data = f.read()
187 |             if data:
188 |                 self.scripts = pickle.loads(data)
    |                                ^^^^^^^^^^^^^^^^^^ S301
189 |         else:
190 |             os.makedirs(self.filename, exist_ok=True)
    |

toolboxv2\mods\isaa\base\AgentUtils.py:252:13: SIM108 Use ternary operator `node_info = node_dict if index == 'question' else node_dict[index]` instead of `if`-`else`-block
    |
251 |               index = list(node_dict.keys())[0]  # Get the node's index.
252 | /             if index == 'question':
253 | |                 node_info = node_dict
254 | |             else:
255 | |                 node_info = node_dict[index]  # Get the node's info.
    | |____________________________________________^ SIM108
256 |               return IsaaQuestionNode(
257 |                   node_info['question'],
    |
    = help: Replace `if`-`else`-block with `node_info = node_dict if index == 'question' else node_dict[index]`

toolboxv2\mods\isaa\base\AgentUtils.py:761:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
759 |                 texts = [text.replace('\\t', '').replace('\t', '')]
760 |             except Exception as e:
761 |                 raise ValueError(f"File processing failed: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
762 |         elif isinstance(data, str):
763 |             texts = [data.replace('\\t', '').replace('\t', '')]
    |

toolboxv2\mods\isaa\base\AgentUtils.py:779:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
777 |             import traceback
778 |             print(traceback.format_exc())
779 |             raise RuntimeError(f"Data addition failed: {str(e)}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
780 |
781 |     def get(self, names):
    |

toolboxv2\mods\isaa\base\AgentUtils.py:1091:13: S102 Use of `exec` detected
     |
1089 |     def eval_code(self, code):
1090 |         try:
1091 |             exec(code, self.global_env, self.local_env)
     |             ^^^^ S102
1092 |             result = eval(code, self.global_env, self.local_env)
1093 |             return self.format_output(result)
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1092:22: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1090 |         try:
1091 |             exec(code, self.global_env, self.local_env)
1092 |             result = eval(code, self.global_env, self.local_env)
     |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
1093 |             return self.format_output(result)
1094 |         except Exception as e:
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1454:24: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1452 |             # If the parsed value is a string, it might be a JSON string, so we try to parse it again
1453 |             if isinstance(parsed_value, str):
1454 |                 return eval(parsed_value)
     |                        ^^^^^^^^^^^^^^^^^^ S307
1455 |             else:
1456 |                 return parsed_value
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1673:24: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1671 |     json_objects = find_json_objects_in_str(data)
1672 |     if not json_objects and data.startswith('[') and data.endswith(']'):
1673 |         json_objects = eval(data)
     |                        ^^^^^^^^^^ S307
1674 |     if json_objects and len(json_objects) > 0 and isinstance(json_objects[0], dict):
1675 |         result.extend([{**expected_keys, **ob} for ob in json_objects])
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1766:1: I001 [*] Import block is un-sorted or un-formatted
     |
1766 | / import os
1767 | | import re
1768 | | import threading
1769 | | from dataclasses import dataclass, field, asdict
1770 | | from datetime import datetime
     | |_____________________________^ I001
     |
     = help: Organize imports

toolboxv2\mods\isaa\base\AgentUtils.py:1767:8: F811 [*] Redefinition of unused `re` from line 5
     |
1766 | import os
1767 | import re
     |        ^^ F811
1768 | import threading
1769 | from dataclasses import dataclass, field, asdict
     |
     = help: Remove definition: `re`

toolboxv2\mods\isaa\base\AgentUtils.py:1768:8: F811 [*] Redefinition of unused `threading` from line 8
     |
1766 | import os
1767 | import re
1768 | import threading
     |        ^^^^^^^^^ F811
1769 | from dataclasses import dataclass, field, asdict
1770 | from datetime import datetime
     |
     = help: Remove definition: `threading`

toolboxv2\mods\isaa\base\AgentUtils.py:1770:22: F811 [*] Redefinition of unused `datetime` from line 9
     |
1768 | import threading
1769 | from dataclasses import dataclass, field, asdict
1770 | from datetime import datetime
     |                      ^^^^^^^^ F811
     |
     = help: Remove definition: `datetime`

toolboxv2\mods\isaa\base\Agent\agent.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import contextlib
 3 | | import io
 4 | | import json
 5 | | import logging
 6 | | import os
 7 | | import re
 8 | | import threading
 9 | | import time
10 | | import uuid
11 | | from collections.abc import Awaitable, Callable
12 | | from dataclasses import dataclass
13 | | from dataclasses import field as dataclass_field
14 | | from enum import Enum
15 | | from importlib.metadata import version
16 | | from inspect import iscoroutinefunction
17 | | from typing import (
18 | |     Any,
19 | |     Literal, Optional,
20 | | )
21 | |
22 | | from pydantic import (
23 | |     BaseModel,
24 | |     ConfigDict,
25 | |     Field,
26 | |     SkipValidation,
27 | |     TypeAdapter,
28 | |     ValidationError,
29 | |     model_validator,
30 | | )
31 | |
32 | | from toolboxv2 import get_logger
   | |________________________________^ I001
33 |
34 |   # --- Framework Imports with Availability Checks ---
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\agent.py:75:45: F401 `google.adk.tools.agent_tool.AgentTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
73 |         def adk_google_search():
74 |             return None  # Dummy
75 |     from google.adk.tools.agent_tool import AgentTool
   |                                             ^^^^^^^^^ F401
76 |     from google.adk.tools.mcp_tool.mcp_toolset import (
77 |         MCPToolset,
   |
   = help: Remove unused import: `google.adk.tools.agent_tool.AgentTool`

toolboxv2\mods\isaa\base\Agent\agent.py:84:45: F401 `google.genai.types.FunctionCall` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
82 |     # ADK Artifacts (Optional, for advanced use cases)
83 |     # from google.adk.artifacts import ArtifactService, InMemoryArtifactService
84 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                             ^^^^^^^^^^^^ F401
85 |
86 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:84:59: F401 `google.genai.types.FunctionResponse` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
82 |     # ADK Artifacts (Optional, for advanced use cases)
83 |     # from google.adk.artifacts import ArtifactService, InMemoryArtifactService
84 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                                           ^^^^^^^^^^^^^^^^ F401
85 |
86 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:180:12: F401 `mcp.server.stdio` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
178 | # If using ADK's MCPToolset, explicit MCP imports might not be needed here.
179 | try:
180 |     import mcp.server.stdio
    |            ^^^^^^^^^^^^^^^^ F401
181 |     import mcp.types as mcp_types  # For building MCP servers
182 |     from google.adk.tools.mcp_tool.conversion_utils import (
    |
    = help: Remove unused import: `mcp.server.stdio`

toolboxv2\mods\isaa\base\Agent\agent.py:188:37: F401 `mcp.server.lowlevel.NotificationOptions` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
186 |     from mcp.client.sse import sse_client as mcp_sse_client
187 |     from mcp.server.fastmcp import FastMCP
188 |     from mcp.server.lowlevel import NotificationOptions
    |                                     ^^^^^^^^^^^^^^^^^^^ F401
189 |     from mcp.server.lowlevel import Server as MCPServerBase
190 |     from mcp.server.models import InitializationOptions
    |
    = help: Remove unused import: `mcp.server.lowlevel.NotificationOptions`

toolboxv2\mods\isaa\base\Agent\agent.py:190:35: F401 `mcp.server.models.InitializationOptions` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
188 |     from mcp.server.lowlevel import NotificationOptions
189 |     from mcp.server.lowlevel import Server as MCPServerBase
190 |     from mcp.server.models import InitializationOptions
    |                                   ^^^^^^^^^^^^^^^^^^^^^ F401
191 |
192 |     MCP_AVAILABLE = True
    |
    = help: Remove unused import: `mcp.server.models.InitializationOptions`

toolboxv2\mods\isaa\base\Agent\agent.py:222:12: F401 `litellm` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
220 | # LiteLLM
221 | try:
222 |     import litellm
    |            ^^^^^^^ F401
223 |     from litellm import BudgetManager, acompletion, completion_cost, token_counter
224 |     from litellm.exceptions import (
    |
    = help: Remove unused import: `litellm`

toolboxv2\mods\isaa\base\Agent\agent.py:251:41: F401 `opentelemetry.sdk.trace.TracerProvider` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
249 | try:
250 |     from opentelemetry import trace
251 |     from opentelemetry.sdk.trace import TracerProvider
    |                                         ^^^^^^^^^^^^^^ F401
252 |     from opentelemetry.sdk.trace.export import (  # Example exporter
253 |         BatchSpanProcessor,
    |
    = help: Remove unused import: `opentelemetry.sdk.trace.TracerProvider`

toolboxv2\mods\isaa\base\Agent\agent.py:253:9: F401 `opentelemetry.sdk.trace.export.BatchSpanProcessor` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
251 |     from opentelemetry.sdk.trace import TracerProvider
252 |     from opentelemetry.sdk.trace.export import (  # Example exporter
253 |         BatchSpanProcessor,
    |         ^^^^^^^^^^^^^^^^^^ F401
254 |         ConsoleSpanExporter,
255 |     )
    |
    = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:254:9: F401 `opentelemetry.sdk.trace.export.ConsoleSpanExporter` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
252 |     from opentelemetry.sdk.trace.export import (  # Example exporter
253 |         BatchSpanProcessor,
254 |         ConsoleSpanExporter,
    |         ^^^^^^^^^^^^^^^^^^^ F401
255 |     )
256 |     # Add other exporters (OTLP, Jaeger, etc.) as needed
    |
    = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:442:21: S102 Use of `exec` detected
    |
440 |             try:
441 |                 with contextlib.redirect_stdout(stdout_capture), contextlib.redirect_stderr(stderr_capture):
442 |                     exec(code_execution_input.code, globals(), local_vars) # <<< UNSAFE!
    |                     ^^^^ S102
443 |                 stdout = stdout_capture.getvalue().strip()
444 |                 stderr = stderr_capture.getvalue().strip()
    |

toolboxv2\mods\isaa\base\Agent\agent.py:639:37: B023 Function definition does not bind loop variable `adk_tool`
    |
637 |                          # This simple version calls the tool's underlying function if possible.
638 |                          # WARNING: This bypasses ADK's standard tool execution flow.
639 |                          if hasattr(adk_tool, 'func') and callable(adk_tool.func):
    |                                     ^^^^^^^^ B023
640 |                              # This assumes the function doesn't need ToolContext
641 |                              result = await adk_tool.func(**kwargs)
    |

toolboxv2\mods\isaa\base\Agent\agent.py:639:68: B023 Function definition does not bind loop variable `adk_tool`
    |
637 |                          # This simple version calls the tool's underlying function if possible.
638 |                          # WARNING: This bypasses ADK's standard tool execution flow.
639 |                          if hasattr(adk_tool, 'func') and callable(adk_tool.func):
    |                                                                    ^^^^^^^^ B023
640 |                              # This assumes the function doesn't need ToolContext
641 |                              result = await adk_tool.func(**kwargs)
    |

toolboxv2\mods\isaa\base\Agent\agent.py:641:45: B023 Function definition does not bind loop variable `adk_tool`
    |
639 |                          if hasattr(adk_tool, 'func') and callable(adk_tool.func):
640 |                              # This assumes the function doesn't need ToolContext
641 |                              result = await adk_tool.func(**kwargs)
    |                                             ^^^^^^^^ B023
642 |                              # Convert result to MCP content (e.g., TextContent)
643 |                              if isinstance(result, str):
    |

toolboxv2\mods\isaa\base\Agent\agent.py:1172:13: SIM102 Use a single `if` statement instead of nested `if` statements
     |
1170 |                        return ProcessingStrategy.ADK_RUN, "Input mentions specific ADK tool or requests tool use."
1171 |               # General ADK case: If ADK is primary mode and input isn't trivial
1172 | /             if len(user_input.split()) > 5: # Simple heuristic for non-trivial input
1173 | |                 # If ADK tools exist, assume ADK might be needed for planning
1174 | |                 if has_adk_tools:
     | |_________________________________^ SIM102
1175 |                       return ProcessingStrategy.ADK_RUN, "Complex input and ADK tools available, using ADK planning."
1176 |                   # If only basic LLM agent, still might use ADK runner for session mgmt? Check config.
     |
     = help: Combine `if` statements using `and`

toolboxv2\mods\isaa\base\Agent\agent.py:1505:33: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
1503 | …                  if err_content:
1504 | …                      error_message = err_content.get('text') if isinstance(err_content, dict) else getattr(err_content, 'text', 'U…
1505 | …             except: pass # Ignore parsing errors here
     |               ^^^^^^^^^^^^ S110
1506 | …         return f"Error: A2A task failed on {target_url}: {error_message or final_text}"
1507 | …     elif current_state == TaskState.CANCELLED:
     |

toolboxv2\mods\isaa\base\Agent\agent.py:1914:54: UP007 [*] Use `X | Y` for type annotations
     |
1912 |                                          target_agent_url: str,
1913 |                                          task_prompt: str,
1914 |                                          session_id: Optional[str] = None
     |                                                      ^^^^^^^^^^^^^ UP007
1915 |                                          ) -> str:
1916 |         """ADK Tool: Sends a task to another agent via A2A and waits for the final text result."""
     |
     = help: Convert to `X | Y`

toolboxv2\mods\isaa\base\Agent\builder.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import contextlib
 3 | | import json
 4 | | import logging
 5 | | import os
 6 | | import threading
 7 | | from collections.abc import Awaitable, Callable
 8 | | from pathlib import Path
 9 | | from typing import (
10 | |     Any,
11 | |     Literal,
12 | |     Protocol,
13 | |     TypeVar, Optional,
14 | | )
15 | |
16 | | from pydantic import (
17 | |     BaseModel,
18 | |     ConfigDict,
19 | |     Field,
20 | |     ValidationError,
21 | |     field_validator,
22 | |     model_validator,
23 | | )
24 | |
25 | | from toolboxv2 import get_logger
   | |________________________________^ I001
26 |
27 |   # Framework Imports & Availability Checks (mirrored from agent.py)
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:28:6: I001 Import block is un-sorted or un-formatted
   |
27 | # Framework Imports & Availability Checks (mirrored from agent.py)
28 | try: from google.adk.agents import LlmAgent; ADK_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
29 | except ImportError: LlmAgent = object; ADK_AVAILABLE_BLD = False # Need LlmAgent for isinstance check
30 | try: from google.adk.tools import BaseTool, FunctionTool, AgentTool; from google.adk.tools.mcp_tool import MCPToolset, StdioServerPara…
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:30:6: I001 Import block is un-sorted or un-formatted
   |
28 | try: from google.adk.agents import LlmAgent; ADK_AVAILABLE_BLD = True
29 | except ImportError: LlmAgent = object; ADK_AVAILABLE_BLD = False # Need LlmAgent for isinstance check
30 | try: from google.adk.tools import BaseTool, FunctionTool, AgentTool; from google.adk.tools.mcp_tool import MCPToolset, StdioServerParameters, SseServerParams; from google.adk.runners import Runner, InMemoryRunner, AsyncWebRunner; from google.adk.sessions import SessionService, InMemorySessionService; from google.adk.code_executors import BaseCodeExecutor as ADKBaseCodeExecutor; from google.adk.planners import BasePlanner; from google.adk.examples import Example
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
31 | except ImportError: BaseTool = object; FunctionTool = object; AgentTool = object; MCPToolset = object; Runner = object; InMemoryRunner = object; AsyncWebRunner = object; SessionService = object; InMemorySessionService = object; ADKBaseCodeExecutor = object; BasePlanner = object; Example = object; StdioServerParameters = object; SseServerParams = object
32 | try: from python_a2a.server import A2AServer; from python_a2a.models import AgentCard; A2A_AVAILABLE_BLD = True
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:32:6: I001 Import block is un-sorted or un-formatted
   |
30 | try: from google.adk.tools import BaseTool, FunctionTool, AgentTool; from google.adk.tools.mcp_tool import MCPToolset, StdioServerPara…
31 | except ImportError: BaseTool = object; FunctionTool = object; AgentTool = object; MCPToolset = object; Runner = object; InMemoryRunner…
32 | try: from python_a2a.server import A2AServer; from python_a2a.models import AgentCard; A2A_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
33 | except ImportError: A2AServer = object; AgentCard = object; A2A_AVAILABLE_BLD = False
34 | try: from mcp.server.fastmcp import FastMCP; MCP_AVAILABLE_BLD = True
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:34:6: I001 Import block is un-sorted or un-formatted
   |
32 | try: from python_a2a.server import A2AServer; from python_a2a.models import AgentCard; A2A_AVAILABLE_BLD = True
33 | except ImportError: A2AServer = object; AgentCard = object; A2A_AVAILABLE_BLD = False
34 | try: from mcp.server.fastmcp import FastMCP; MCP_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
35 | except ImportError: FastMCP = object; MCP_AVAILABLE_BLD = False
36 | try: from litellm import BudgetManager; LITELLM_AVAILABLE_BLD = True
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:36:6: I001 Import block is un-sorted or un-formatted
   |
34 | try: from mcp.server.fastmcp import FastMCP; MCP_AVAILABLE_BLD = True
35 | except ImportError: FastMCP = object; MCP_AVAILABLE_BLD = False
36 | try: from litellm import BudgetManager; LITELLM_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
37 | except ImportError: BudgetManager = object; LITELLM_AVAILABLE_BLD = False
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:42:35: F401 `google.adk.agents.BaseAgent` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
40 | # Google ADK
41 | try:
42 |     from google.adk.agents import BaseAgent, LlmAgent
   |                                   ^^^^^^^^^ F401
43 |     from google.adk.agents.callback_context import CallbackContext
44 |     from google.adk.agents.invocation_context import InvocationContext
   |
   = help: Remove unused import: `google.adk.agents.BaseAgent`

toolboxv2\mods\isaa\base\Agent\builder.py:43:52: F401 `google.adk.agents.callback_context.CallbackContext` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
41 | try:
42 |     from google.adk.agents import BaseAgent, LlmAgent
43 |     from google.adk.agents.callback_context import CallbackContext
   |                                                    ^^^^^^^^^^^^^^^ F401
44 |     from google.adk.agents.invocation_context import InvocationContext
45 |     from google.adk.code_executors import BaseCodeExecutor
   |
   = help: Remove unused import: `google.adk.agents.callback_context.CallbackContext`

toolboxv2\mods\isaa\base\Agent\builder.py:44:54: F401 `google.adk.agents.invocation_context.InvocationContext` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
42 |     from google.adk.agents import BaseAgent, LlmAgent
43 |     from google.adk.agents.callback_context import CallbackContext
44 |     from google.adk.agents.invocation_context import InvocationContext
   |                                                      ^^^^^^^^^^^^^^^^^ F401
45 |     from google.adk.code_executors import BaseCodeExecutor
46 |     from google.adk.code_executors.code_execution_utils import (
   |
   = help: Remove unused import: `google.adk.agents.invocation_context.InvocationContext`

toolboxv2\mods\isaa\base\Agent\builder.py:47:9: F401 `google.adk.code_executors.code_execution_utils.CodeExecutionInput` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
45 |     from google.adk.code_executors import BaseCodeExecutor
46 |     from google.adk.code_executors.code_execution_utils import (
47 |         CodeExecutionInput,
   |         ^^^^^^^^^^^^^^^^^^ F401
48 |         CodeExecutionResult,
49 |     )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:48:9: F401 `google.adk.code_executors.code_execution_utils.CodeExecutionResult` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
46 |     from google.adk.code_executors.code_execution_utils import (
47 |         CodeExecutionInput,
48 |         CodeExecutionResult,
   |         ^^^^^^^^^^^^^^^^^^^ F401
49 |     )
50 |     from google.adk.events import Event
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:50:35: F401 `google.adk.events.Event` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
48 |         CodeExecutionResult,
49 |     )
50 |     from google.adk.events import Event
   |                                   ^^^^^ F401
51 |     from google.adk.examples import Example  # For few-shot
52 |     from google.adk.models import BaseLlm, Gemini
   |
   = help: Remove unused import: `google.adk.events.Event`

toolboxv2\mods\isaa\base\Agent\builder.py:52:35: F401 `google.adk.models.BaseLlm` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
50 |     from google.adk.events import Event
51 |     from google.adk.examples import Example  # For few-shot
52 |     from google.adk.models import BaseLlm, Gemini
   |                                   ^^^^^^^ F401
53 |     from google.adk.models.lite_llm import LiteLlm  # ADK Wrapper for LiteLLM
54 |     from google.adk.planners import BasePlanner
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:52:44: F401 `google.adk.models.Gemini` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
50 |     from google.adk.events import Event
51 |     from google.adk.examples import Example  # For few-shot
52 |     from google.adk.models import BaseLlm, Gemini
   |                                            ^^^^^^ F401
53 |     from google.adk.models.lite_llm import LiteLlm  # ADK Wrapper for LiteLLM
54 |     from google.adk.planners import BasePlanner
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:60:37: F401 `google.adk.sessions.Session` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
58 |         Runner,
59 |     )
60 |     from google.adk.sessions import Session, State
   |                                     ^^^^^^^ F401
61 |     from google.adk.tools import (
62 |         BaseTool,
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:60:46: F401 `google.adk.sessions.State` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
58 |         Runner,
59 |     )
60 |     from google.adk.sessions import Session, State
   |                                              ^^^^^ F401
61 |     from google.adk.tools import (
62 |         BaseTool,
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:64:9: F401 `google.adk.tools.LongRunningFunctionTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
62 |         BaseTool,
63 |         FunctionTool,
64 |         LongRunningFunctionTool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^ F401
65 |         ToolContext,
66 |     )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:65:9: F401 `google.adk.tools.ToolContext` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
63 |         FunctionTool,
64 |         LongRunningFunctionTool,
65 |         ToolContext,
   |         ^^^^^^^^^^^ F401
66 |     )
67 |     from google.adk.tools import VertexAiSearchTool as AdkVertexAiSearchTool
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:67:56: F401 `google.adk.tools.VertexAiSearchTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
65 |         ToolContext,
66 |     )
67 |     from google.adk.tools import VertexAiSearchTool as AdkVertexAiSearchTool
   |                                                        ^^^^^^^^^^^^^^^^^^^^^ F401
68 |     from google.adk.tools import (
69 |         built_in_code_execution as adk_built_in_code_execution,  # Secure option
   |
   = help: Remove unused import: `google.adk.tools.VertexAiSearchTool`

toolboxv2\mods\isaa\base\Agent\builder.py:71:51: F401 `google.adk.tools.google_search` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
69 |         built_in_code_execution as adk_built_in_code_execution,  # Secure option
70 |     )
71 |     from google.adk.tools import google_search as adk_google_search
   |                                                   ^^^^^^^^^^^^^^^^^ F401
72 |     from google.adk.tools.agent_tool import AgentTool
73 |     from google.adk.tools.mcp_tool.mcp_toolset import (
   |
   = help: Remove unused import: `google.adk.tools.google_search`

toolboxv2\mods\isaa\base\Agent\builder.py:72:45: F401 `google.adk.tools.agent_tool.AgentTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
70 |     )
71 |     from google.adk.tools import google_search as adk_google_search
72 |     from google.adk.tools.agent_tool import AgentTool
   |                                             ^^^^^^^^^ F401
73 |     from google.adk.tools.mcp_tool.mcp_toolset import (
74 |         MCPToolset,
   |
   = help: Remove unused import: `google.adk.tools.agent_tool.AgentTool`

toolboxv2\mods\isaa\base\Agent\builder.py:78:36: F401 `google.genai.types.Content` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
76 |         StdioServerParameters,
77 |     )
78 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                    ^^^^^^^ F401
79 |
80 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:78:45: F401 `google.genai.types.FunctionCall` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
76 |         StdioServerParameters,
77 |     )
78 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                             ^^^^^^^^^^^^ F401
79 |
80 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:78:59: F401 `google.genai.types.FunctionResponse` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
76 |         StdioServerParameters,
77 |     )
78 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                                           ^^^^^^^^^^^^^^^^ F401
79 |
80 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:78:77: F401 `google.genai.types.Part` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
76 |         StdioServerParameters,
77 |     )
78 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                                                             ^^^^ F401
79 |
80 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:131:12: F401 `litellm` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
129 | # LiteLLM
130 | try:
131 |     import litellm
    |            ^^^^^^^ F401
132 |     from litellm import BudgetManager
133 |     from litellm.utils import get_max_tokens
    |
    = help: Remove unused import: `litellm`

toolboxv2\mods\isaa\base\Agent\builder.py:570:60: UP007 [*] Use `X | Y` for type annotations
    |
568 |         return self
569 |
570 |     def with_adk_tool_function(self, func: Callable, name: Optional[str] = None,
    |                                                            ^^^^^^^^^^^^^ UP007
571 |                                description: Optional[str] = None) -> 'EnhancedAgentBuilder':
572 |         """Adds a callable function as an ADK tool (transient)."""
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\base\Agent\builder.py:571:45: UP007 [*] Use `X | Y` for type annotations
    |
570 |     def with_adk_tool_function(self, func: Callable, name: Optional[str] = None,
571 |                                description: Optional[str] = None) -> 'EnhancedAgentBuilder':
    |                                             ^^^^^^^^^^^^^ UP007
572 |         """Adds a callable function as an ADK tool (transient)."""
573 |         if not self._ensure_adk("Tool Function"):
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\base\Agent\executors.py:131:13: S102 Use of `exec` detected
    |
129 |             # Execute the compiled code
130 |             # Note: restrictedpython does not inherently support robust timeouts during exec
131 |             exec(byte_code, exec_globals, local_vars)
    |             ^^^^ S102
132 |
133 |             # Check execution time again
    |

toolboxv2\mods\isaa\base\Agent\executors.py:337:32: F821 Undefined name `AgentConfig`
    |
336 | # --- Factory function ---
337 | def get_code_executor(config: 'AgentConfig') -> RestrictedPythonExecutor | DockerCodeExecutor | BaseCodeExecutor | None:
    |                                ^^^^^^^^^^^ F821
338 |     """Creates a code executor instance based on configuration."""
339 |     executor_type = config.code_executor_type
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import hashlib
 3 | | import json
 4 | | import math
 5 | | import os
 6 | | import pickle
 7 | | import re
 8 | | import time
 9 | | import uuid
10 | | from collections import defaultdict
11 | | from dataclasses import dataclass
12 | | from typing import Any, NamedTuple
13 | |
14 | | import networkx as nx
15 | | import numpy as np
16 | | from pydantic import BaseModel
17 | | from sklearn.cluster import HDBSCAN
18 | |
19 | | from toolboxv2 import Spinner, get_app, get_logger
20 | | from toolboxv2.mods.isaa.base.VectorStores import AbstractVectorStore
21 | | from toolboxv2.mods.isaa.base.VectorStores.FaissVectorStore import FaissVectorStore
22 | |
23 | | from toolboxv2.mods.isaa.extras.adapter import litellm_complete
24 | | from toolboxv2.mods.isaa.extras.filter import after_format
   | |__________________________________________________________^ I001
25 |
26 |   i__ = [0, 0, 0]
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\KnowledgeBase.py:9:8: F401 [*] `uuid` imported but unused
   |
 7 | import re
 8 | import time
 9 | import uuid
   |        ^^^^ F401
10 | from collections import defaultdict
11 | from dataclasses import dataclass
   |
   = help: Remove unused import: `uuid`

toolboxv2\mods\isaa\base\KnowledgeBase.py:253:17: SIM115 Use a context manager for opening files
    |
251 |         print(f"Graph saved to {output_file} Open in browser to view.", len(nx_graph))
252 |         if get_output:
253 |             c = open(output_file, encoding="utf-8").read()
    |                 ^^^^ SIM115
254 |             os.remove(output_file)
255 |             return c
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:607:89: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
605 | class KnowledgeBase:
606 |     def __init__(self, embedding_dim: int = 768, similarity_threshold: float = 0.61, batch_size: int = 64,
607 |                  n_clusters: int = 4, deduplication_threshold: float = 0.85, model_name=os.getenv("DEFAULTMODELSUMMERY"),
    |                                                                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
608 |                  embedding_model=os.getenv("DEFAULTMODELEMBEDDING"),
609 |                  vis_class:str | None = "FaissVectorStore",
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:608:34: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
606 |     def __init__(self, embedding_dim: int = 768, similarity_threshold: float = 0.61, batch_size: int = 64,
607 |                  n_clusters: int = 4, deduplication_threshold: float = 0.85, model_name=os.getenv("DEFAULTMODELSUMMERY"),
608 |                  embedding_model=os.getenv("DEFAULTMODELEMBEDDING"),
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
609 |                  vis_class:str | None = "FaissVectorStore",
610 |                  vis_kwargs:dict[str, Any] | None=None,
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1706:28: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1704 |                 # Load data from disk
1705 |                 with open(path, 'rb') as f:
1706 |                     data = pickle.load(f)
     |                            ^^^^^^^^^^^^^^ S301
1707 |             elif isinstance(path, bytes):
1708 |                 data = pickle.loads(path)
     |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1708:24: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1706 |                     data = pickle.load(f)
1707 |             elif isinstance(path, bytes):
1708 |                 data = pickle.loads(path)
     |                        ^^^^^^^^^^^^^^^^^^ S301
1709 |             else:
1710 |                 raise ValueError("Invalid path type")
     |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1885:11: F811 Redefinition of unused `math` from line 4
     |
1883 | text = "test 123".encode("utf-8", errors="replace").decode("utf-8")
1884 |
1885 | async def math():
     |           ^^^^ F811
1886 |     kb = KnowledgeBase(n_clusters=3, model_name="openrouter/mistralai/mistral-7b-instruct", requests_per_second=10, batch_size=20, c…
     |
     = help: Remove definition: `math`

toolboxv2\mods\isaa\base\VectorStores\FaissVectorStore.py:49:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
47 |         import faiss
48 |
49 |         loaded = pickle.loads(data)
   |                  ^^^^^^^^^^^^^^^^^^ S301
50 |         self.dimension = loaded['dimension']
51 |         self.index = faiss.deserialize_index(loaded['index_bytes'])
   |

toolboxv2\mods\isaa\base\VectorStores\RedisVectorStore.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / import contextlib
2 | | import json
3 | | import pickle
4 | | import redis
  | |____________^ I001
5 |
6 |   try:
  |
  = help: Organize imports

toolboxv2\mods\isaa\base\VectorStores\RedisVectorStore.py:20:1: I001 [*] Import block is un-sorted or un-formatted
   |
18 |           return None
19 |
20 | / import numpy as np
21 | |
22 | | from toolboxv2.mods.isaa.base.VectorStores.types import AbstractVectorStore, Chunk
   | |__________________________________________________________________________________^ I001
23 |
24 |   class RedisVectorStore(AbstractVectorStore):
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\VectorStores\RedisVectorStore.py:109:23: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
107 |     def load(self, data: bytes) -> 'RedisVectorStore':
108 |         self.clear()
109 |         loaded_data = pickle.loads(data)
    |                       ^^^^^^^^^^^^^^^^^^ S301
110 |         pipe = self.redis_client.pipeline()
111 |         for hash_data in loaded_data:
    |

toolboxv2\mods\isaa\base\VectorStores\__init__.py:5:68: F401 `toolboxv2.mods.isaa.base.VectorStores.RedisVectorStore.RedisVectorStore` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
3 | """
4 | from toolboxv2.mods.isaa.base.VectorStores.FaissVectorStore import FaissVectorStore
5 | from toolboxv2.mods.isaa.base.VectorStores.RedisVectorStore import RedisVectorStore
  |                                                                    ^^^^^^^^^^^^^^^^ F401
6 | from toolboxv2.mods.isaa.base.VectorStores.types import AbstractVectorStore
  |
  = help: Add unused import `RedisVectorStore` to __all__

toolboxv2\mods\isaa\base\VectorStores\__init__.py:6:57: F401 `toolboxv2.mods.isaa.base.VectorStores.types.AbstractVectorStore` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
4 | from toolboxv2.mods.isaa.base.VectorStores.FaissVectorStore import FaissVectorStore
5 | from toolboxv2.mods.isaa.base.VectorStores.RedisVectorStore import RedisVectorStore
6 | from toolboxv2.mods.isaa.base.VectorStores.types import AbstractVectorStore
  |                                                         ^^^^^^^^^^^^^^^^^^^ F401
7 |
8 | try:
  |
  = help: Add unused import `AbstractVectorStore` to __all__

toolboxv2\mods\isaa\base\VectorStores\__init__.py:9:68: F401 `toolboxv2.mods.isaa.base.VectorStores.qdrant_store.QdrantVectorStore` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 8 | try:
 9 |     from toolboxv2.mods.isaa.base.VectorStores.qdrant_store import QdrantVectorStore
   |                                                                    ^^^^^^^^^^^^^^^^^ F401
10 |     QDRANT_AVAILABLE = True
11 | except ImportError:
   |
   = help: Remove unused import: `toolboxv2.mods.isaa.base.VectorStores.qdrant_store.QdrantVectorStore`

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:11:8: F401 [*] `json` imported but unused
   |
 9 |     numba.njit =lambda **_:lambda x:x
10 | import contextlib
11 | import json
   |        ^^^^ F401
12 | import os
13 | import pickle
   |
   = help: Remove unused import: `json`

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:15:17: F401 [*] `abc.ABC` imported but unused
   |
13 | import pickle
14 | import threading
15 | from abc import ABC, abstractmethod
   |                 ^^^ F401
16 | from dataclasses import dataclass
17 | from typing import Any
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:15:22: F401 [*] `abc.abstractmethod` imported but unused
   |
13 | import pickle
14 | import threading
15 | from abc import ABC, abstractmethod
   |                      ^^^^^^^^^^^^^^ F401
16 | from dataclasses import dataclass
17 | from typing import Any
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:17:20: F401 [*] `typing.Any` imported but unused
   |
15 | from abc import ABC, abstractmethod
16 | from dataclasses import dataclass
17 | from typing import Any
   |                    ^^^ F401
18 |
19 | import numpy as np
   |
   = help: Remove unused import: `typing.Any`

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:183:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
182 |     def load(self, data: bytes) -> 'NumpyVectorStore':
183 |         loaded = pickle.loads(data)
    |                  ^^^^^^^^^^^^^^^^^^ S301
184 |         self.embeddings = loaded['embeddings']
185 |         self.chunks = loaded['chunks']
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:391:26: SIM115 Use a context manager for opening files
    |
389 |             f.write(b'\0' * initial_size)
390 |
391 |         self.mmap_file = open(mmap_path, 'r+b')
    |                          ^^^^ SIM115
392 |         self.mmap_array = np.memmap(
393 |             self.mmap_file,
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:402:21: SIM115 Use a context manager for opening files
    |
400 |         with self.lock:
401 |             self.index.save_index("index.temp")
402 |             index = open("index.s.temp", 'rb').read()
    |                     ^^^^ SIM115
403 |             os.remove("index.s.temp")
404 |             state = {
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:413:21: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
411 |     def load(self, data: bytes) -> 'EnhancedVectorStore':
412 |         with self.lock:
413 |             state = pickle.loads(data)
    |                     ^^^^^^^^^^^^^^^^^^ S301
414 |             self.chunks = state['chunks']
415 |             self.config = state['config']
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:416:13: SIM115 Use a context manager for opening files
    |
414 |             self.chunks = state['chunks']
415 |             self.config = state['config']
416 |             open("index.l.temp", "wb").write(state['index'])
    |             ^^^^ SIM115
417 |             self.index.load_index("index.l.temp")
418 |             os.remove("index.f.temp")
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:634:17: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
633 |     def load(self, data: bytes) -> 'FastVectorStore':
634 |         state = pickle.loads(data)
    |                 ^^^^^^^^^^^^^^^^^^ S301
635 |         self.current_size = state['current_size']
636 |         self.chunks = state['chunks']
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:831:17: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
830 |     def load(self, data: bytes) -> 'FastVectorStore':
831 |         state = pickle.loads(data)
    |                 ^^^^^^^^^^^^^^^^^^ S301
832 |         self.current_size = state['current_size']
833 |         self.chunks = state['chunks']
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:952:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
951 |     def load(self, data: bytes) -> 'FastVectorStoreO':
952 |         loaded = pickle.loads(data)
    |                  ^^^^^^^^^^^^^^^^^^ S301
953 |         self.embeddings = loaded['embeddings'].astype(np.float32)
954 |         self.chunks = loaded['chunks']
    |

toolboxv2\mods\isaa\base\VectorStores\taichiNumpyNumbaVectorStores.py:1208:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1207 |     def load(self, data: bytes) -> 'FastVectorStoreO':
1208 |         loaded = pickle.loads(data)
     |                  ^^^^^^^^^^^^^^^^^^ S301
1209 |         self.embeddings = loaded['embeddings'].astype(np.float32)
1210 |         self.chunks = loaded['chunks']
     |

toolboxv2\mods\isaa\base\VectorStores\types.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / from abc import ABC, abstractmethod
2 | | from dataclasses import dataclass
3 | | from typing import Any
4 | | import numpy as np
  | |__________________^ I001
  |
  = help: Organize imports

toolboxv2\mods\isaa\chainUi.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # Add these to your existing module.py or a new one
 2 |   # ... (keep existing imports and MOD_NAME, VERSION, export, etc.)
 3 | / import json
 4 | | import os  # Ensure os is imported
 5 | | import uuid
 6 | | from typing import Optional, List, Dict, Any
 7 | |
 8 | | # Assuming Task and TaskChain are defined, e.g., in toolboxv2.mods.isaa.types
 9 | | # from toolboxv2.mods.isaa.types import Task, TaskChain
10 | | # If they are not, here are placeholder Pydantic models:
11 | | from pydantic import BaseModel, Field as PydanticField
12 | |
13 | | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
14 | | from toolboxv2.mods.isaa.types import TaskChain, Task
   | |_____________________________________________________^ I001
15 |
16 |   # Moduldefinition
   |
   = help: Organize imports

toolboxv2\mods\isaa\chainUi.py:4:8: F401 [*] `os` imported but unused
  |
2 | # ... (keep existing imports and MOD_NAME, VERSION, export, etc.)
3 | import json
4 | import os  # Ensure os is imported
  |        ^^ F401
5 | import uuid
6 | from typing import Optional, List, Dict, Any
  |
  = help: Remove unused import: `os`

toolboxv2\mods\isaa\chainUi.py:6:1: UP035 `typing.List` is deprecated, use `list` instead
  |
4 | import os  # Ensure os is imported
5 | import uuid
6 | from typing import Optional, List, Dict, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
7 |
8 | # Assuming Task and TaskChain are defined, e.g., in toolboxv2.mods.isaa.types
  |

toolboxv2\mods\isaa\chainUi.py:6:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
4 | import os  # Ensure os is imported
5 | import uuid
6 | from typing import Optional, List, Dict, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
7 |
8 | # Assuming Task and TaskChain are defined, e.g., in toolboxv2.mods.isaa.types
  |

toolboxv2\mods\isaa\chainUi.py:11:22: F401 [*] `pydantic.BaseModel` imported but unused
   |
 9 | # from toolboxv2.mods.isaa.types import Task, TaskChain
10 | # If they are not, here are placeholder Pydantic models:
11 | from pydantic import BaseModel, Field as PydanticField
   |                      ^^^^^^^^^ F401
12 |
13 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |
   = help: Remove unused import

toolboxv2\mods\isaa\chainUi.py:11:42: F401 [*] `pydantic.Field` imported but unused
   |
 9 | # from toolboxv2.mods.isaa.types import Task, TaskChain
10 | # If they are not, here are placeholder Pydantic models:
11 | from pydantic import BaseModel, Field as PydanticField
   |                                          ^^^^^^^^^^^^^ F401
12 |
13 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |
   = help: Remove unused import

toolboxv2\mods\isaa\chainUi.py:13:58: F401 [*] `toolboxv2.ToolBoxError` imported but unused
   |
11 | from pydantic import BaseModel, Field as PydanticField
12 |
13 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                          ^^^^^^^^^^^^ F401
14 | from toolboxv2.mods.isaa.types import TaskChain, Task
   |
   = help: Remove unused import

toolboxv2\mods\isaa\chainUi.py:13:72: F401 [*] `toolboxv2.ToolBoxResult` imported but unused
   |
11 | from pydantic import BaseModel, Field as PydanticField
12 |
13 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                                        ^^^^^^^^^^^^^ F401
14 | from toolboxv2.mods.isaa.types import TaskChain, Task
   |
   = help: Remove unused import

toolboxv2\mods\isaa\chainUi.py:13:87: F401 [*] `toolboxv2.ToolBoxInfo` imported but unused
   |
11 | from pydantic import BaseModel, Field as PydanticField
12 |
13 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                                                       ^^^^^^^^^^^ F401
14 | from toolboxv2.mods.isaa.types import TaskChain, Task
   |
   = help: Remove unused import

toolboxv2\mods\isaa\chainUi.py:13:100: F401 [*] `toolboxv2.ToolBoxInterfaces` imported but unused
   |
11 | from pydantic import BaseModel, Field as PydanticField
12 |
13 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                                                                    ^^^^^^^^^^^^^^^^^ F401
14 | from toolboxv2.mods.isaa.types import TaskChain, Task
   |
   = help: Remove unused import

toolboxv2\mods\isaa\chainUi.py:26:35: UP007 [*] Use `X | Y` for type annotations
   |
24 | CHAIN_LIST_KEY_SUFFIX = "_list"
25 |
26 | def get_current_username(request: Optional[RequestData] = None) -> str:
   |                                   ^^^^^^^^^^^^^^^^^^^^^ UP007
27 |     if request:
28 |         return request.session.user_name
   |
   = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:32:75: UP006 [*] Use `list` instead of `List` for type annotation
   |
32 | async def get_user_data(app: App, username: str, data_key_prefix: str) -> List[Dict[str, Any]]:
   |                                                                           ^^^^ UP006
33 |     db = app.get_mod("DB")  # Standard DB-Instanz
34 |     # db.edit_cli("RR") # Sicherstellen, dass der Read-Replica-Modus aktiv ist, falls nötig
   |
   = help: Replace with `list`

toolboxv2\mods\isaa\chainUi.py:32:80: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
32 | async def get_user_data(app: App, username: str, data_key_prefix: str) -> List[Dict[str, Any]]:
   |                                                                                ^^^^ UP006
33 |     db = app.get_mod("DB")  # Standard DB-Instanz
34 |     # db.edit_cli("RR") # Sicherstellen, dass der Read-Replica-Modus aktiv ist, falls nötig
   |
   = help: Replace with `dict`

toolboxv2\mods\isaa\chainUi.py:55:79: UP006 [*] Use `list` instead of `List` for type annotation
   |
55 | async def save_user_data(app: App, username: str, data_key_prefix: str, data: List[Dict[str, Any]]):
   |                                                                               ^^^^ UP006
56 |     db = app.get_mod("DB")
57 |     # db.edit_cli("RR") # oder einen spezifischen Schreibmodus, falls konfiguriert
   |
   = help: Replace with `list`

toolboxv2\mods\isaa\chainUi.py:55:84: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
55 | async def save_user_data(app: App, username: str, data_key_prefix: str, data: List[Dict[str, Any]]):
   |                                                                                    ^^^^ UP006
56 |     db = app.get_mod("DB")
57 |     # db.edit_cli("RR") # oder einen spezifischen Schreibmodus, falls konfiguriert
   |
   = help: Replace with `dict`

toolboxv2\mods\isaa\chainUi.py:62:60: UP006 [*] Use `list` instead of `List` for type annotation
   |
62 | async def get_task_chain_names(app: App, username: str) -> List[str]:
   |                                                            ^^^^ UP006
63 |     # Chains are stored individually, but we maintain a list of names for discovery
64 |     chain_list_data = await get_user_data(app, username, f"{CHAIN_DATA_PREFIX}{CHAIN_LIST_KEY_SUFFIX}")
   |
   = help: Replace with `list`

toolboxv2\mods\isaa\chainUi.py:70:71: UP006 [*] Use `list` instead of `List` for type annotation
   |
70 | async def save_task_chain_names(app: App, username: str, chain_names: List[str]):
   |                                                                       ^^^^ UP006
71 |     await save_user_data(app, username, f"{CHAIN_DATA_PREFIX}{CHAIN_LIST_KEY_SUFFIX}", chain_names)
   |
   = help: Replace with `list`

toolboxv2\mods\isaa\chainUi.py:75:50: UP007 [*] Use `X | Y` for type annotations
   |
74 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
75 | async def get_task_chain_list(app: App, request: Optional[RequestData] = None):
   |                                                  ^^^^^^^^^^^^^^^^^^^^^ UP007
76 |     username = get_current_username(request)
77 |     chain_names = await get_task_chain_names(app, username)
   |
   = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:82:56: UP007 [*] Use `X | Y` for type annotations
   |
81 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
82 | async def get_task_chain_definition(app: App, request: Optional[RequestData] = None):
   |                                                        ^^^^^^^^^^^^^^^^^^^^^ UP007
83 |     username = get_current_username(request)
84 |     chain_name = request.query_params.get("chain_name") if request and request.query_params else None
   |
   = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:101:57: UP007 [*] Use `X | Y` for type annotations
    |
100 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
101 | async def save_task_chain_definition(app: App, request: Optional[RequestData] = None,
    |                                                         ^^^^^^^^^^^^^^^^^^^^^ UP007
102 |                                      data: Optional[Dict[str, Any]] = None, **kwargs):
103 |     username = get_current_username(request)
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:102:44: UP007 [*] Use `X | Y` for type annotations
    |
100 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
101 | async def save_task_chain_definition(app: App, request: Optional[RequestData] = None,
102 |                                      data: Optional[Dict[str, Any]] = None, **kwargs):
    |                                            ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
103 |     username = get_current_username(request)
104 |     if data and not getattr(request, 'body', None):  # Compatibility for direct data passthrough
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:102:53: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
100 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
101 | async def save_task_chain_definition(app: App, request: Optional[RequestData] = None,
102 |                                      data: Optional[Dict[str, Any]] = None, **kwargs):
    |                                                     ^^^^ UP006
103 |     username = get_current_username(request)
104 |     if data and not getattr(request, 'body', None):  # Compatibility for direct data passthrough
    |
    = help: Replace with `dict`

toolboxv2\mods\isaa\chainUi.py:156:48: UP007 [*] Use `X | Y` for type annotations
    |
155 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['DELETE'])
156 | async def delete_task_chain(app: App, request: Optional[RequestData] = None, **kwargs):
    |                                                ^^^^^^^^^^^^^^^^^^^^^ UP007
157 |     username = get_current_username(request)
158 |     chain_name = request.query_params.get("chain_name") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:165:5: F841 Local variable `key_to_delete` is assigned to but never used
    |
163 |     # Delete the specific chain data
164 |     db = app.get_mod("DB")
165 |     key_to_delete = f"{CHAIN_DATA_PREFIX}_{chain_name}_{username}"  # Note: DB key format was prefix_username
    |     ^^^^^^^^^^^^^ F841
166 |
167 |     # Correct key to delete based on save_user_data structure
    |
    = help: Remove assignment to unused variable `key_to_delete`

toolboxv2\mods\isaa\chainUi.py:170:5: F841 Local variable `key_to_delete_actual` is assigned to but never used
    |
168 |     # save_user_data creates key as: f"{data_key_prefix}_{username}"
169 |     # So, data_key_prefix here is f"{CHAIN_DATA_PREFIX}_{chain_name}"
170 |     key_to_delete_actual = f"{CHAIN_DATA_PREFIX}_{chain_name}_{username}"
    |     ^^^^^^^^^^^^^^^^^^^^ F841
171 |     # Ah, the save_user_data appends username to the prefix. So, the prefix passed to it should be just CHAIN_DATA_PREFIX_{chain_name}
172 |     # Let's fix the key for deletion:
    |
    = help: Remove assignment to unused variable `key_to_delete_actual`

toolboxv2\mods\isaa\chainUi.py:199:57: UP007 [*] Use `X | Y` for type annotations
    |
197 | # --- Endpoint for the Task Chain Editor UI ---
198 | @export(mod_name=MOD_NAME, api=True, version=VERSION, name="task_chain_editor", api_methods=['GET'])
199 | async def get_task_chain_editor_page(app: App, request: Optional[RequestData] = None):
    |                                                         ^^^^^^^^^^^^^^^^^^^^^ UP007
200 |     if app is None:
201 |         app = get_app()
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\chainUi.py:202:20: S608 Possible SQL injection vector through string-based query construction
    |
200 |       if app is None:
201 |           app = get_app()
202 |       html_content = app.web_context() + """
    |  ____________________^
203 | |     <div class="main-content frosted-glass">
204 | |     <title>Task Chain Editor</title>
205 | |     <style>
206 | |         .task-chain-editor-grid { display: grid; grid-template-columns: 300px 1fr; gap: 1.5rem; height: calc(100vh - 150px); }
207 | |         .chain-list-panel { border-right: 1px solid var(--tb-border-color, #e5e7eb); padding-right: 1rem; overflow-y: auto; }
208 | |         .task-editor-panel { overflow-y: auto; }
209 | |         .task-card {
210 | |             background-color: var(--tb-card-bg, #ffffff);
211 | |             border: 1px solid var(--tb-border-color, #e0e0e0);
212 | |             border-radius: 0.5rem;
213 | |             padding: 1rem;
214 | |             margin-bottom: 0.75rem;
215 | |             cursor: grab;
216 | |             box-shadow: var(--tb-shadow-md, 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06));
217 | |         }
218 | |         .dark .task-card { background-color: var(--tb-card-bg-dark, #374151); border-color: var(--tb-border-color-dark, #4b5563); }
219 | |         .task-card:active { cursor: grabbing; background-color: var(--tb-primary-100, #ebf8ff); }
220 | |         .dark .task-card:active { background-color: var(--tb-primary-700, #2c5282); }
221 | |         .drag-over-placeholder { border: 2px dashed var(--tb-primary-500, #4299e1); background-color: var(--tb-primary-50, #ebf8ff); …
222 | |         .task-actions button { margin-left: 0.5rem; }
223 | |     </style>
224 | |     <div id="app-root" class="tb-container tb-mx-auto tb-p-4">
225 | |         <header class="tb-flex tb-justify-between tb-items-center tb-mb-6">
226 | |             <h1 class="tb-text-3xl tb-font-bold">Task Chain Editor</h1>
227 | |             <div>
228 | |                 <span id="currentUserChainEditor" class="tb-mr-4"></span>
229 | |                  <div id="darkModeToggleContainerChainEditor" style="display: inline-block;"></div>
230 | |             </div>
231 | |         </header>
232 | |
233 | |         <div class="task-chain-editor-grid">
234 | |             <!-- Linke Spalte: Kettenauswahl und -verwaltung -->
235 | |             <div class="chain-list-panel">
236 | |                 <h2 class="tb-text-xl tb-font-semibold tb-mb-3">Chains</h2>
237 | |                 <div class="tb-mb-3">
238 | |                     <select id="chainSelector" class="tb-input tb-w-full"></select>
239 | |                 </div>
240 | |                 <button id="newChainBtn" class="tb-btn tb-btn-success tb-w-full tb-mb-2">
241 | |                     <span class="material-symbols-outlined tb-mr-1">add</span> Neue Kette
242 | |                 </button>
243 | |                 <button id="deleteChainBtn" class="tb-btn tb-btn-danger tb-w-full tb-mb-2" disabled>
244 | |                     <span class="material-symbols-outlined tb-mr-1">delete</span> Kette Löschen
245 | |                 </button>
246 | |                  <button id="saveChainBtn" class="tb-btn tb-btn-primary tb-w-full" disabled>
247 | |                     <span class="material-symbols-outlined tb-mr-1">save</span> Kette Speichern
248 | |                 </button>
249 | |             </div>
250 | |
251 | |             <!-- Rechte Spalte: Aufgaben-Editor für ausgewählte Kette -->
252 | |             <div class="task-editor-panel">
253 | |                 <div class="tb-mb-4">
254 | |                     <label for="chainNameInput" class="tb-label">Name der Kette:</label>
255 | |                     <input type="text" id="chainNameInput" class="tb-input tb-w-full" placeholder="Name der Kette" disabled>
256 | |                 </div>
257 | |                 <div class="tb-mb-4">
258 | |                     <label for="chainDescriptionInput" class="tb-label">Beschreibung:</label>
259 | |                     <textarea id="chainDescriptionInput" class="tb-input tb-w-full" rows="2" placeholder="Optionale Beschreibung" dis…
260 | |                 </div>
261 | |
262 | |                 <div class="tb-flex tb-justify-between tb-items-center tb-mb-3">
263 | |                     <h2 class="tb-text-xl tb-font-semibold">Aufgaben</h2>
264 | |                     <button id="addTaskToChainBtn" class="tb-btn tb-btn-primary" disabled>
265 | |                          <span class="material-symbols-outlined tb-mr-1">playlist_add</span> Aufgabe Hinzufügen
266 | |                     </button>
267 | |                 </div>
268 | |                 <div id="taskListContainer" class="tb-min-h-[200px] tb-border tb-p-2 tb-rounded tb-bg-gray-50 dark:tb-bg-gray-800">
269 | |                     <!-- Aufgaben werden hier per Drag & Drop eingefügt und sortiert -->
270 | |                     <p id="noTasksMessage" class="tb-text-gray-500 dark:tb-text-gray-400">Wähle oder erstelle eine Kette.</p>
271 | |                 </div>
272 | |             </div>
273 | |         </div>
274 | |     </div>
275 | |     <script defer src="https://unpkg.com/htmx.org@2.0.2/dist/htmx.min.js"></script>
276 | |     <script defer src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.153.0/three.min.js"></script>
277 | |     <script defer src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
278 | |     <script defer src="https://cdn.jsdelivr.net/npm/marked-highlight/lib/index.umd.min.js"></script>
279 | |     <script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
280 | |     <script defer type="module">
281 | |         let currentChainName = null;
282 | |         let currentTasks = [];
283 | |         let currentChainDescription = "";
284 | |         let allChainNames = [];
285 | |         let draggedTaskElement = null;
286 | |         let placeholder = null;
287 | |
288 | |         const TASK_TYPES = ["agent", "tool", "chain"]; // For 'use' field
289 | |
290 | |         if (window.TB?.events) {
291 | |             if (window.TB.config?.get('appRootId')) {
292 | |                  initializeChainEditor();
293 | |             } else {
294 | |                 window.TB.events.on('tbjs:initialized', initializeChainEditor, { once: true });
295 | |             }
296 | |         } else {
297 | |             document.addEventListener('tbjs:initialized', initializeChainEditor, { once: true });
298 | |         }
299 | |
300 | |         function initializeChainEditor() {
301 | |             const username = TB.user.getUsername() || 'default_user';
302 | |             document.getElementById('currentUserChainEditor').textContent = `Benutzer: ${username}`;
303 | |
304 | |             // Init DarkModeToggle specifically for this container if it's separate
305 | |             if (TB.ui && TB.ui.DarkModeToggle && document.getElementById('darkModeToggleContainerChainEditor')) {
306 | |                 new TB.ui.DarkModeToggle({target: document.getElementById('darkModeToggleContainerChainEditor')});
307 | |             }
308 | |
309 | |
310 | |             document.getElementById('chainSelector').addEventListener('change', handleChainSelectionChange);
311 | |             document.getElementById('newChainBtn').addEventListener('click', handleNewChain);
312 | |             document.getElementById('saveChainBtn').addEventListener('click', handleSaveChain);
313 | |             document.getElementById('deleteChainBtn').addEventListener('click', handleDeleteChain);
314 | |             document.getElementById('addTaskToChainBtn').addEventListener('click', () => showTaskModal(null));
315 | |
316 | |             document.getElementById('chainNameInput').addEventListener('input', (e) => {
317 | |                 if (currentChainName !== null) { // Only allow editing name if a chain is "active"
318 | |                      // This change is temporary until saved.
319 | |                 }
320 | |             });
321 | |             document.getElementById('chainDescriptionInput').addEventListener('input', (e) => {
322 | |                 currentChainDescription = e.target.value;
323 | |             });
324 | |
325 | |
326 | |             loadChainList();
327 | |             updateEditorState(); // Initial UI state
328 | |         }
329 | |
330 | |         async function loadChainList() {
331 | |             TB.ui.Loader.show('Lade Ketten...');
332 | |             try {
333 | |                 const response = await TB.api.request('isaa.chainUi', 'get_task_chain_list', null, 'GET');
334 | |                 if (response.error === TB.ToolBoxError.none && response.get()) {
335 | |                     allChainNames = response.get();
336 | |                     populateChainSelector();
337 | |                 } else {
338 | |                     TB.ui.Toast.showError('Fehler beim Laden der Kettenliste: ' + response.info.help_text);
339 | |                     allChainNames = [];
340 | |                 }
341 | |             } catch (e) {
342 | |                 TB.ui.Toast.showError('Netzwerkfehler beim Laden der Kettenliste.');
343 | |                 console.error(e);
344 | |                 allChainNames = [];
345 | |             } finally {
346 | |                 TB.ui.Loader.hide();
347 | |             }
348 | |         }
349 | |
350 | |         function populateChainSelector() {
351 | |             const selector = document.getElementById('chainSelector');
352 | |             selector.innerHTML = '<option value="">-- Kette auswählen --</option>';
353 | |             allChainNames.forEach(name => {
354 | |                 const option = document.createElement('option');
355 | |                 option.value = name;
356 | |                 option.textContent = name;
357 | |                 selector.appendChild(option);
358 | |             });
359 | |         }
360 | |
361 | |         async function handleChainSelectionChange() {
362 | |             const selector = document.getElementById('chainSelector');
363 | |             const selectedName = selector.value;
364 | |             if (selectedName) {
365 | |                 await loadChainDefinition(selectedName);
366 | |             } else {
367 | |                 currentChainName = null;
368 | |                 currentTasks = [];
369 | |                 currentChainDescription = "";
370 | |                 updateEditorState();
371 | |                 renderCurrentTasks();
372 | |             }
373 | |         }
374 | |
375 | |         async function loadChainDefinition(chainName) {
376 | |             TB.ui.Loader.show('Lade Kettendetails...');
377 | |             try {
378 | |                 const response = await TB.api.request('isaa.chainUi', `get_task_chain_definition?chain_name=${encodeURIComponent(chai…
379 | |                 if (response.error === TB.ToolBoxError.none && response.get()) {
380 | |                     const chainDef = response.get();
381 | |                     currentChainName = chainDef.name;
382 | |                     currentTasks = chainDef.tasks.map(task => ({ ...task, id: task.id || TB.utils.uuidv4() })); // Ensure client-side…
383 | |                     currentChainDescription = chainDef.description || "";
384 | |                     renderCurrentTasks();
385 | |                 } else {
386 | |                     TB.ui.Toast.showError('Kette nicht gefunden oder Fehler: ' + response.info.help_text);
387 | |                     currentChainName = null; // Or perhaps set to chainName to allow creating it if not found
388 | |                     currentTasks = [];
389 | |                     currentChainDescription = "";
390 | |                     renderCurrentTasks();
391 | |                 }
392 | |             } catch (e) {
393 | |                 TB.ui.Toast.showError('Netzwerkfehler beim Laden der Kettendetails.');
394 | |                 console.error(e);
395 | |                 currentChainName = null; currentTasks = []; currentChainDescription = ""; renderCurrentTasks();
396 | |             } finally {
397 | |                 updateEditorState();
398 | |                 TB.ui.Loader.hide();
399 | |             }
400 | |         }
401 | |
402 | |         function updateEditorState() {
403 | |             const chainSelected = currentChainName !== null;
404 | |             document.getElementById('chainNameInput').value = currentChainName || '';
405 | |             document.getElementById('chainNameInput').disabled = !chainSelected; // Allow editing name if a chain is "active" for sav…
406 | |             document.getElementById('chainDescriptionInput').value = currentChainDescription || '';
407 | |             document.getElementById('chainDescriptionInput').disabled = !chainSelected;
408 | |             document.getElementById('saveChainBtn').disabled = !chainSelected;
409 | |             document.getElementById('deleteChainBtn').disabled = !chainSelected;
410 | |             document.getElementById('addTaskToChainBtn').disabled = !chainSelected;
411 | |             document.getElementById('noTasksMessage').style.display = (chainSelected && currentTasks.length === 0) ? 'block' : 'none';
412 | |              if (!chainSelected) {
413 | |                  document.getElementById('taskListContainer').innerHTML = '<p id="noTasksMessage" class="tb-text-gray-500 dark:tb-tex…
414 | |              }
415 | |         }
416 | |
417 | |
418 | |         function renderCurrentTasks() {
419 | |             const container = document.getElementById('taskListContainer');
420 | |             container.innerHTML = ''; // Clear previous tasks
421 | |             if (currentChainName === null) {
422 | |                  container.innerHTML = '<p id="noTasksMessage" class="tb-text-gray-500 dark:tb-text-gray-400">Wähle oder erstelle ein…
423 | |                  return;
424 | |             }
425 | |              if (currentTasks.length === 0) {
426 | |                 container.innerHTML = '<p id="noTasksMessage" class="tb-text-gray-500 dark:tb-text-gray-400">Noch keine Aufgaben in d…
427 | |                 return;
428 | |             }
429 | |
430 | |
431 | |             currentTasks.forEach((task, index) => {
432 | |                 const taskCard = document.createElement('div');
433 | |                 taskCard.className = 'task-card';
434 | |                 taskCard.setAttribute('draggable', 'true');
435 | |                 taskCard.dataset.taskId = task.id; // Use client-side ID for dragging
436 | |                 taskCard.dataset.index = index; // Keep original index for actions
437 | |
438 | |                 taskCard.innerHTML = `
439 | |                     <div class="tb-font-semibold tb-text-lg">${TB.utils.escapeHtml(task.name)} (${TB.utils.escapeHtml(task.use)})</di…
440 | |                     <p class="tb-text-sm tb-text-gray-600 dark:tb-text-gray-300 tb-mb-1">Args: <code class="tb-bg-gray-200 dark:tb-bg…
441 | |                     <p class="tb-text-sm tb-text-gray-600 dark:tb-text-gray-300">Return Key: <code class="tb-bg-gray-200 dark:tb-bg-g…
442 | |                     <div class="task-actions tb-text-right tb-mt-2">
443 | |                         <button class="tb-btn tb-btn-sm tb-btn-icon tb-text-blue-500" data-action="edit" title="Bearbeiten"><span cla…
444 | |                         <button class="tb-btn tb-btn-sm tb-btn-icon tb-text-red-500" data-action="delete" title="Löschen"><span class…
445 | |                     </div>
446 | |                 `;
447 | |                 taskCard.addEventListener('dragstart', dragStart);
448 | |                 taskCard.addEventListener('dragend', dragEnd);
449 | |                 container.appendChild(taskCard);
450 | |
451 | |                 taskCard.querySelector('button[data-action="edit"]').addEventListener('click', () => showTaskModal(task, index));
452 | |                 taskCard.querySelector('button[data-action="delete"]').addEventListener('click', () => handleRemoveTask(index));
453 | |             });
454 | |             // Add dragover and drop listeners to the container
455 | |             container.addEventListener('dragover', dragOver);
456 | |             container.addEventListener('drop', dropTask);
457 | |         }
458 | |
459 | |         function handleNewChain() {
460 | |             TB.ui.Modal.show({
461 | |                 title: 'Neue Kette erstellen',
462 | |                 content: '<input type="text" id="newChainNameModal" class="tb-input tb-w-full" placeholder="Name der neuen Kette">',
463 | |                 buttons: [
464 | |                     {text: 'Abbrechen', action: modal => modal.close()},
465 | |                     {
466 | |                         text: 'Erstellen',
467 | |                         variant: 'primary',
468 | |                         action: async modal => {
469 | |                             const newName = document.getElementById('newChainNameModal').value.trim();
470 | |                             if (!newName) {
471 | |                                 TB.ui.Toast.showWarning('Kettenname darf nicht leer sein.');
472 | |                                 return;
473 | |                             }
474 | |                             if (allChainNames.includes(newName)) {
475 | |                                 TB.ui.Toast.showWarning('Eine Kette mit diesem Namen existiert bereits.');
476 | |                                 return;
477 | |                             }
478 | |                             currentChainName = newName;
479 | |                             currentTasks = [];
480 | |                             currentChainDescription = ""; // Reset description for new chain
481 | |
482 | |                             // Add to selector and select it
483 | |                             const selector = document.getElementById('chainSelector');
484 | |                             const option = document.createElement('option');
485 | |                             option.value = newName;
486 | |                             option.textContent = newName;
487 | |                             selector.appendChild(option);
488 | |                             selector.value = newName;
489 | |                             allChainNames.push(newName); // Add to client-side list
490 | |
491 | |                             updateEditorState();
492 | |                             renderCurrentTasks();
493 | |                             modal.close();
494 | |                             TB.ui.Toast.showSuccess(`Neue Kette '${newName}' initialisiert. Speichern nicht vergessen!`);
495 | |                         }
496 | |                     }
497 | |                 ],
498 | |                 onOpen: () => document.getElementById('newChainNameModal')?.focus()
499 | |             });
500 | |         }
501 | |
502 | |         async function handleSaveChain() {
503 | |             const chainNameFromInput = document.getElementById('chainNameInput').value.trim();
504 | |             if (!chainNameFromInput) {
505 | |                 TB.ui.Toast.showWarning('Kettenname darf nicht leer sein.');
506 | |                 return;
507 | |             }
508 | |
509 | |             // If the name was changed from the input field and it's different from currentChainName,
510 | |             // it implies a rename or saving a new chain if currentChainName was from a newly created (unsaved) chain.
511 | |             let effectiveChainName = chainNameFromInput;
512 | |             let isRename = currentChainName && currentChainName !== chainNameFromInput && allChainNames.includes(currentChainName);
513 | |
514 | |             if (isRename) {
515 | |                 // Potentially handle rename logic: delete old, save new.
516 | |                 // For simplicity now, we'll just save under the new name.
517 | |                 // User might need to delete the old one manually if this is a "Save As" like behavior.
518 | |                 // Or, prompt for confirmation of rename.
519 | |                  const confirmRename = await TB.ui.Modal.confirm({title: 'Kette umbenennen?', content: `Möchtest du die Kette von '${…
520 | |                  if (!confirmRename) return;
521 | |             }
522 | |
523 | |
524 | |             const payload = {
525 | |                 name: effectiveChainName,
526 | |                 description: currentChainDescription,
527 | |                 tasks: currentTasks.map(({id, ...task}) => task) // Remove client-side ID before saving
528 | |             };
529 | |
530 | |             TB.ui.Loader.show('Speichere Kette...');
531 | |             try {
532 | |                 const response = await TB.api.request('isaa.chainUi', 'save_task_chain_definition', payload, 'POST');
533 | |                 if (response.error === TB.ToolBoxError.none) {
534 | |                     TB.ui.Toast.showSuccess(`Kette '${effectiveChainName}' erfolgreich gespeichert.`);
535 | |                      if (isRename) {
536 | |                         // Delete the old chain definition from backend
537 | |                         await TB.api.request('isaa.chainUi', `delete_task_chain?chain_name=${encodeURIComponent(currentChainName)}`, …
538 | |                         // Update client-side list
539 | |                         allChainNames = allChainNames.filter(name => name !== currentChainName);
540 | |                     }
541 | |                     currentChainName = effectiveChainName; // Update current name to the saved one
542 | |                     if (!allChainNames.includes(effectiveChainName)) {
543 | |                         allChainNames.push(effectiveChainName);
544 | |                     }
545 | |                     populateChainSelector(); // Repopulate to reflect changes
546 | |                     document.getElementById('chainSelector').value = effectiveChainName; // Reselect
547 | |                     updateEditorState();
548 | |                 } else {
549 | |                     TB.ui.Toast.showError('Fehler beim Speichern der Kette: ' + response.info.help_text);
550 | |                 }
551 | |             } catch (e) {
552 | |                 TB.ui.Toast.showError('Netzwerkfehler beim Speichern der Kette.');
553 | |                 console.error(e);
554 | |             } finally {
555 | |                 TB.ui.Loader.hide();
556 | |             }
557 | |         }
558 | |
559 | |         async function handleDeleteChain() {
560 | |             if (!currentChainName) return;
561 | |             const confirmed = await TB.ui.Modal.confirm({
562 | |                 title: 'Kette löschen?',
563 | |                 content: `Möchtest du die Kette '${currentChainName}' wirklich löschen? Dies kann nicht rückgängig gemacht werden.`
564 | |             });
565 | |             if (!confirmed) return;
566 | |
567 | |             TB.ui.Loader.show('Lösche Kette...');
568 | |             try {
569 | |                 const response = await TB.api.request('isaa.chainUi', `delete_task_chain?chain_name=${encodeURIComponent(currentChain…
570 | |                  if (response.error === TB.ToolBoxError.none) {
571 | |                     TB.ui.Toast.showSuccess(`Kette '${currentChainName}' gelöscht.`);
572 | |                     currentChainName = null;
573 | |                     currentTasks = [];
574 | |                     currentChainDescription = "";
575 | |                     await loadChainList(); // Reload list from server
576 | |                     updateEditorState();
577 | |                     renderCurrentTasks();
578 | |                 } else {
579 | |                     TB.ui.Toast.showError('Fehler beim Löschen der Kette: ' + response.info.help_text);
580 | |                 }
581 | |             } catch (e) {
582 | |                 TB.ui.Toast.showError('Netzwerkfehler beim Löschen der Kette.');
583 | |                 console.error(e);
584 | |             } finally {
585 | |                 TB.ui.Loader.hide();
586 | |             }
587 | |         }
588 | |
589 | |
590 | |         function showTaskModal(task = null, index = -1) {
591 | |             const isEditing = task !== null;
592 | |             const modalTitle = isEditing ? 'Aufgabe bearbeiten' : 'Neue Aufgabe hinzufügen';
593 | |             const useOptions = TASK_TYPES.map(type => `<option value="${type}" ${task?.use === type ? 'selected' : ''}>${type}</optio…
594 | |
595 | |             TB.ui.Modal.show({
596 | |                 title: modalTitle,
597 | |                 content: `
598 | |                     <form id="taskFormModal" class="tb-space-y-4">
599 | |                         <div>
600 | |                             <label for="taskUse" class="tb-label">Use (Typ):</label>
601 | |                             <select id="taskUseModal" class="tb-input tb-w-full">${useOptions}</select>
602 | |                         </div>
603 | |                         <div>
604 | |                             <label for="taskName" class="tb-label">Name (Agent/Tool/Chain):</label>
605 | |                             <input type="text" id="taskNameModal" class="tb-input tb-w-full" value="${TB.utils.escapeHtml(task?.name …
606 | |                         </div>
607 | |                         <div>
608 | |                             <label for="taskArgs" class="tb-label">Argumente:</label>
609 | |                             <input type="text" id="taskArgsModal" class="tb-input tb-w-full" value="${TB.utils.escapeHtml(task?.args …
610 | |                             <p class="tb-text-xs tb-text-gray-500 dark:tb-text-gray-400">Verwende $user-input für Benutzereingabe, $v…
611 | |                         </div>
612 | |                         <div>
613 | |                             <label for="taskReturnKey" class="tb-label">Return Key (Variable für Ergebnis):</label>
614 | |                             <input type="text" id="taskReturnKeyModal" class="tb-input tb-w-full" value="${TB.utils.escapeHtml(task?.…
615 | |                         </div>
616 | |                     </form>
617 | |                 `,
618 | |                 buttons: [
619 | |                     { text: 'Abbrechen', action: modal => modal.close(), variant: 'secondary' },
620 | |                     {
621 | |                         text: isEditing ? 'Speichern' : 'Hinzufügen',
622 | |                         variant: 'primary',
623 | |                         action: modal => {
624 | |                             const newTask = {
625 | |                                 id: task?.id || TB.utils.uuidv4(), // Retain ID if editing, else new
626 | |                                 use: document.getElementById('taskUseModal').value,
627 | |                                 name: document.getElementById('taskNameModal').value.trim(),
628 | |                                 args: document.getElementById('taskArgsModal').value.trim(),
629 | |                                 return_key: document.getElementById('taskReturnKeyModal').value.trim()
630 | |                             };
631 | |                             if (!newTask.name) {
632 | |                                 TB.ui.Toast.showWarning('Aufgabenname darf nicht leer sein.');
633 | |                                 return;
634 | |                             }
635 | |                             if (isEditing) {
636 | |                                 currentTasks[index] = newTask;
637 | |                             } else {
638 | |                                 currentTasks.push(newTask);
639 | |                             }
640 | |                             renderCurrentTasks();
641 | |                             modal.close();
642 | |                         }
643 | |                     }
644 | |                 ],
645 | |                 onOpen: () => document.getElementById('taskNameModal')?.focus()
646 | |             });
647 | |         }
648 | |
649 | |         function handleRemoveTask(index) {
650 | |             currentTasks.splice(index, 1);
651 | |             renderCurrentTasks();
652 | |         }
653 | |
654 | |         // --- Drag and Drop Logic ---
655 | |         function dragStart(e) {
656 | |             draggedTaskElement = e.target;
657 | |             e.dataTransfer.effectAllowed = 'move';
658 | |             e.dataTransfer.setData('text/plain', e.target.dataset.taskId); // Use client-side ID
659 | |             // Optional: add a class for styling the dragged item
660 | |             setTimeout(() => e.target.classList.add('tb-opacity-50'), 0);
661 | |
662 | |             // Create placeholder
663 | |             placeholder = document.createElement('div');
664 | |             placeholder.className = 'drag-over-placeholder';
665 | |             placeholder.style.height = `${draggedTaskElement.offsetHeight}px`;
666 | |         }
667 | |
668 | |         function dragEnd(e) {
669 | |             draggedTaskElement.classList.remove('tb-opacity-50');
670 | |             draggedTaskElement = null;
671 | |             if (placeholder && placeholder.parentNode) {
672 | |                 placeholder.parentNode.removeChild(placeholder);
673 | |             }
674 | |             placeholder = null;
675 | |             // Remove any hover styles from all cards
676 | |              document.querySelectorAll('.task-card').forEach(card => card.style.borderTop = '');
677 | |         }
678 | |
679 | |         function dragOver(e) {
680 | |             e.preventDefault(); // Necessary to allow dropping
681 | |             e.dataTransfer.dropEffect = 'move';
682 | |             const targetCard = e.target.closest('.task-card');
683 | |             const taskList = document.getElementById('taskListContainer');
684 | |
685 | |             if (targetCard && targetCard !== draggedTaskElement) {
686 | |                 const rect = targetCard.getBoundingClientRect();
687 | |                 const isAfter = e.clientY > rect.top + rect.height / 2;
688 | |
689 | |                 // Remove existing placeholder before inserting new one
690 | |                 if (placeholder && placeholder.parentNode) {
691 | |                     placeholder.parentNode.removeChild(placeholder);
692 | |                 }
693 | |
694 | |                 if (isAfter) {
695 | |                     taskList.insertBefore(placeholder, targetCard.nextSibling);
696 | |                 } else {
697 | |                     taskList.insertBefore(placeholder, targetCard);
698 | |                 }
699 | |             } else if (!targetCard && taskList.children.length > 0 && placeholder && !placeholder.parentNode) {
700 | |                  // If dragging over empty space in container, append placeholder at the end
701 | |                  taskList.appendChild(placeholder);
702 | |             } else if (taskList.children.length === 0 && placeholder && !placeholder.parentNode) {
703 | |                 taskList.appendChild(placeholder);
704 | |             }
705 | |         }
706 | |
707 | |         function dropTask(e) {
708 | |             e.preventDefault();
709 | |             if (!draggedTaskElement || !placeholder || !placeholder.parentNode) return;
710 | |
711 | |             const draggedTaskId = draggedTaskElement.dataset.taskId;
712 | |             const originalIndex = currentTasks.findIndex(t => t.id === draggedTaskId);
713 | |             if (originalIndex === -1) return;
714 | |
715 | |             const taskToMove = currentTasks[originalIndex];
716 | |
717 | |             // Find new index based on placeholder's position
718 | |             const children = Array.from(placeholder.parentNode.children);
719 | |             let newIndex = children.indexOf(placeholder);
720 | |
721 | |             // Remove the task from its original position
722 | |             currentTasks.splice(originalIndex, 1);
723 | |
724 | |             // Adjust newIndex if the original item was before the drop target
725 | |             if (originalIndex < newIndex && placeholder.previousSibling === draggedTaskElement) {
726 | |                 // This case might not be perfectly accurate with placeholder logic, test thoroughly
727 | |             } else if (draggedTaskElement === placeholder.previousSibling) {
728 | |                 // If dragging down, the placeholder index is effectively one less for the splice
729 | |                 newIndex = Math.max(0, newIndex -1);
730 | |             }
731 | |
732 | |
733 | |             // Insert the task at the new position
734 | |             currentTasks.splice(newIndex, 0, taskToMove);
735 | |
736 | |             renderCurrentTasks(); // Re-render the entire list to reflect new order
737 | |         }
738 | |
739 | |     </script>
740 | |     </div>
741 | |     """
    | |_______^ S608
742 |       return Result.html(data=html_content)
    |

toolboxv2\mods\isaa\extras\filter.py:32:9: S605 Starting a process with a shell, possible injection detected
   |
30 |         from rapidfuzz import fuzz
31 |     except Exception:
32 |         os.system([sys.executable, '-m', 'pip', 'install', 'RapidFuzz'])
   |         ^^^^^^^^^ S605
33 |         from rapidfuzz import fuzz
34 |     try:
   |

toolboxv2\mods\isaa\extras\filter.py:37:9: S605 Starting a process with a shell, possible injection detected
   |
35 |         from sentence_transformers import SentenceTransformer, util
36 |     except Exception:
37 |         os.system([sys.executable, '-m', 'pip', 'install', 'sentence-transformers'])
   |         ^^^^^^^^^ S605
38 |         from sentence_transformers import SentenceTransformer, util
   |

toolboxv2\mods\isaa\extras\filter.py:148:13: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
146 |         return _d.encode("utf-8", errors='replace').decode("utf-8", errors='replace')
147 |     try:
148 |         d = eval(clean(d))
    |             ^^^^^^^^^^^^^^ S307
149 |     except SyntaxError:
150 |         print("Invalid syntax in input data")
    |

toolboxv2\mods\isaa\extras\filter.py:154:17: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
152 |     if isinstance(d, str):
153 |         try:
154 |             d = eval(clean(d, ex=True))
    |                 ^^^^^^^^^^^^^^^^^^^^^^^ S307
155 |         except Exception:
156 |             d = after_format_(d1)
    |

toolboxv2\mods\isaa\extras\modes.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import os
 2 | | from dataclasses import dataclass
 3 | | from platform import system
 4 | | from typing import Any, Callable
 5 | |
 6 | | from langchain_community.agent_toolkits.load_tools import (
 7 | |     load_huggingface_tool,
 8 | |     load_tools,
 9 | | )
10 | | from langchain_core.tools import BaseTool
   | |_________________________________________^ I001
   |
   = help: Organize imports

toolboxv2\mods\isaa\extras\modes.py:4:1: UP035 [*] Import from `collections.abc` instead: `Callable`
  |
2 | from dataclasses import dataclass
3 | from platform import system
4 | from typing import Any, Callable
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
5 |
6 | from langchain_community.agent_toolkits.load_tools import (
  |
  = help: Import from `collections.abc`

toolboxv2\mods\isaa\isaa_modi.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / import os
2 | | from toolboxv2.mods import BROWSER
  | |__________________________________^ I001
3 |
4 |   PIPLINE = None
  |
  = help: Organize imports

toolboxv2\mods\isaa\isaa_modi.py:8:9: F401 `toolboxv2.mods.isaa_audio.get_audio_transcribe` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 6 | try:
 7 |     from toolboxv2.mods.isaa_audio import (
 8 |         get_audio_transcribe,
   |         ^^^^^^^^^^^^^^^^^^^^ F401
 9 |         s30sek_mean,
10 |         speech_stream,
   |
   = help: Remove unused import

toolboxv2\mods\isaa\isaa_modi.py:9:9: F401 `toolboxv2.mods.isaa_audio.s30sek_mean` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 7 |     from toolboxv2.mods.isaa_audio import (
 8 |         get_audio_transcribe,
 9 |         s30sek_mean,
   |         ^^^^^^^^^^^ F401
10 |         speech_stream,
11 |         text_to_speech3,
   |
   = help: Remove unused import

toolboxv2\mods\isaa\isaa_modi.py:10:9: F401 `toolboxv2.mods.isaa_audio.speech_stream` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 8 |         get_audio_transcribe,
 9 |         s30sek_mean,
10 |         speech_stream,
   |         ^^^^^^^^^^^^^ F401
11 |         text_to_speech3,
12 |     )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\isaa_modi.py:11:9: F401 `toolboxv2.mods.isaa_audio.text_to_speech3` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 9 |         s30sek_mean,
10 |         speech_stream,
11 |         text_to_speech3,
   |         ^^^^^^^^^^^^^^^ F401
12 |     )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\isaa_modi.py:25:1: I001 [*] Import block is un-sorted or un-formatted
   |
23 |     INQUIRER = False
24 |
25 | from toolboxv2.utils.extras.Style import Style, print_to_console
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
26 |
27 | def sys_print(x, **kwargs):
   |
   = help: Organize imports

toolboxv2\mods\isaa\isaa_modi.py:79:9: S605 Starting a process with a shell, possible injection detected
   |
77 |         images_url = [images_url]
78 |     for image_url in images_url:
79 |         os.system(f'start {browser} {image_url}')
   |         ^^^^^^^^^ S605
   |

toolboxv2\mods\isaa\module.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import copy
 2 | | import os
 3 | | import threading
 4 | | import time
 5 | | from collections.abc import Callable
 6 | | from dataclasses import field
 7 | | from enum import Enum
 8 | | from inspect import signature
 9 | | import asyncio  # Added for async operations
10 | | from pathlib import Path
11 | |
12 | | import requests
13 | | import torch
14 | | from langchain_community.agent_toolkits.load_tools import (
15 | |     load_huggingface_tool,
16 | |     load_tools,
17 | | )
18 | | from langchain_community.chat_models import ChatOpenAI
19 | | from langchain_community.llms import HuggingFaceHub, OpenAI
20 | | from langchain_community.tools import AIPluginTool
21 | | from pebble import concurrent
22 | | from pydantic import BaseModel
23 | |
24 | | from .base.KnowledgeBase import TextSplitter
25 | | from .extras.filter import filter_relevant_texts
26 | | from .types import TaskChain
27 | |
28 | | from toolboxv2.utils.system import FileCache
29 | |
30 | | from ...utils.toolbox import stram_print
   | |________________________________________^ I001
31 |
32 |   try:
   |
   = help: Organize imports

toolboxv2\mods\isaa\module.py:4:8: F401 [*] `time` imported but unused
  |
2 | import os
3 | import threading
4 | import time
  |        ^^^^ F401
5 | from collections.abc import Callable
6 | from dataclasses import field
  |
  = help: Remove unused import: `time`

toolboxv2\mods\isaa\module.py:6:25: F401 [*] `dataclasses.field` imported but unused
  |
4 | import time
5 | from collections.abc import Callable
6 | from dataclasses import field
  |                         ^^^^^ F401
7 | from enum import Enum
8 | from inspect import signature
  |
  = help: Remove unused import: `dataclasses.field`

toolboxv2\mods\isaa\module.py:7:18: F401 [*] `enum.Enum` imported but unused
  |
5 | from collections.abc import Callable
6 | from dataclasses import field
7 | from enum import Enum
  |                  ^^^^ F401
8 | from inspect import signature
9 | import asyncio  # Added for async operations
  |
  = help: Remove unused import: `enum.Enum`

toolboxv2\mods\isaa\module.py:8:21: F401 [*] `inspect.signature` imported but unused
   |
 6 | from dataclasses import field
 7 | from enum import Enum
 8 | from inspect import signature
   |                     ^^^^^^^^^ F401
 9 | import asyncio  # Added for async operations
10 | from pathlib import Path
   |
   = help: Remove unused import: `inspect.signature`

toolboxv2\mods\isaa\module.py:13:8: F401 [*] `torch` imported but unused
   |
12 | import requests
13 | import torch
   |        ^^^^^ F401
14 | from langchain_community.agent_toolkits.load_tools import (
15 |     load_huggingface_tool,
   |
   = help: Remove unused import: `torch`

toolboxv2\mods\isaa\module.py:15:5: F401 [*] `langchain_community.agent_toolkits.load_tools.load_huggingface_tool` imported but unused
   |
13 | import torch
14 | from langchain_community.agent_toolkits.load_tools import (
15 |     load_huggingface_tool,
   |     ^^^^^^^^^^^^^^^^^^^^^ F401
16 |     load_tools,
17 | )
   |
   = help: Remove unused import: `langchain_community.agent_toolkits.load_tools.load_huggingface_tool`

toolboxv2\mods\isaa\module.py:18:45: F401 [*] `langchain_community.chat_models.ChatOpenAI` imported but unused
   |
16 |     load_tools,
17 | )
18 | from langchain_community.chat_models import ChatOpenAI
   |                                             ^^^^^^^^^^ F401
19 | from langchain_community.llms import HuggingFaceHub, OpenAI
20 | from langchain_community.tools import AIPluginTool
   |
   = help: Remove unused import: `langchain_community.chat_models.ChatOpenAI`

toolboxv2\mods\isaa\module.py:19:38: F401 [*] `langchain_community.llms.HuggingFaceHub` imported but unused
   |
17 | )
18 | from langchain_community.chat_models import ChatOpenAI
19 | from langchain_community.llms import HuggingFaceHub, OpenAI
   |                                      ^^^^^^^^^^^^^^ F401
20 | from langchain_community.tools import AIPluginTool
21 | from pebble import concurrent
   |
   = help: Remove unused import

toolboxv2\mods\isaa\module.py:19:54: F401 [*] `langchain_community.llms.OpenAI` imported but unused
   |
17 | )
18 | from langchain_community.chat_models import ChatOpenAI
19 | from langchain_community.llms import HuggingFaceHub, OpenAI
   |                                                      ^^^^^^ F401
20 | from langchain_community.tools import AIPluginTool
21 | from pebble import concurrent
   |
   = help: Remove unused import

toolboxv2\mods\isaa\module.py:20:39: F401 [*] `langchain_community.tools.AIPluginTool` imported but unused
   |
18 | from langchain_community.chat_models import ChatOpenAI
19 | from langchain_community.llms import HuggingFaceHub, OpenAI
20 | from langchain_community.tools import AIPluginTool
   |                                       ^^^^^^^^^^^^ F401
21 | from pebble import concurrent
22 | from pydantic import BaseModel
   |
   = help: Remove unused import: `langchain_community.tools.AIPluginTool`

toolboxv2\mods\isaa\module.py:24:33: F401 [*] `.base.KnowledgeBase.TextSplitter` imported but unused
   |
22 | from pydantic import BaseModel
23 |
24 | from .base.KnowledgeBase import TextSplitter
   |                                 ^^^^^^^^^^^^ F401
25 | from .extras.filter import filter_relevant_texts
26 | from .types import TaskChain
   |
   = help: Remove unused import: `.base.KnowledgeBase.TextSplitter`

toolboxv2\mods\isaa\module.py:25:28: F401 [*] `.extras.filter.filter_relevant_texts` imported but unused
   |
24 | from .base.KnowledgeBase import TextSplitter
25 | from .extras.filter import filter_relevant_texts
   |                            ^^^^^^^^^^^^^^^^^^^^^ F401
26 | from .types import TaskChain
   |
   = help: Remove unused import: `.extras.filter.filter_relevant_texts`

toolboxv2\mods\isaa\module.py:41:1: I001 [*] Import block is un-sorted or un-formatted
   |
39 |       gpt4all.GPT4All = None
40 |
41 | / import json
42 | | import locale
43 | | import platform
44 | | import shlex
45 | | import subprocess
46 | | import sys
47 | | from typing import Any
48 | |
49 | | from toolboxv2 import FileHandler, MainTool, Spinner, Style, get_app, get_logger
50 | |
51 | | # Updated imports for EnhancedAgent
52 | | from .base.Agent.agent import (
53 | |     EnhancedAgent,
54 | |     AgentModelData,  # For type hinting if needed
55 | |     WorldModel,  # For type hinting if needed
56 | | )
57 | | from .base.Agent.builder import (
58 | |     EnhancedAgentBuilder,
59 | |     BuilderConfig,
60 | | )
61 | | # AgentVirtualEnv and LLMFunction might be deprecated or need adaptation
62 | | # For now, keeping them if they are used by other parts not being refactored.
63 | | # from t.base.Agents import AgentVirtualEnv, LLMFunction
64 | |
65 | |
66 | | from .base.AgentUtils import (
67 | |     AgentChain,
68 | |     AISemanticMemory,
69 | |     Scripts,
70 | |     dilate_string, ControllerManager
71 | | )
72 | | from .CodingAgent.live import Pipeline
73 | | from .extras.modes import (
74 | |     ISAA0CODE,  # Assuming this is a constant string
75 | |     ChainTreeExecutor,
76 | |     StrictFormatResponder,
77 | |     SummarizationMode,
78 | |     TaskChainMode,
79 | |     # crate_llm_function_from_langchain_tools, # This will need to adapt to ADK tools
80 | | )
81 | |
82 | | from .SearchAgentCluster.search_tool import web_search
83 | | from .chainUi import initialize_module as initialize_isaa_chains
84 | | from .ui import initialize_isaa_webui_module
   | |____________________________________________^ I001
85 |   PIPLINE = None  # This seems unused or related to old pipeline
86 |   Name = 'isaa'
   |
   = help: Organize imports

toolboxv2\mods\isaa\module.py:54:5: F401 [*] `.base.Agent.agent.AgentModelData` imported but unused
   |
52 | from .base.Agent.agent import (
53 |     EnhancedAgent,
54 |     AgentModelData,  # For type hinting if needed
   |     ^^^^^^^^^^^^^^ F401
55 |     WorldModel,  # For type hinting if needed
56 | )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\module.py:55:5: F401 [*] `.base.Agent.agent.WorldModel` imported but unused
   |
53 |     EnhancedAgent,
54 |     AgentModelData,  # For type hinting if needed
55 |     WorldModel,  # For type hinting if needed
   |     ^^^^^^^^^^ F401
56 | )
57 | from .base.Agent.builder import (
   |
   = help: Remove unused import

toolboxv2\mods\isaa\module.py:70:5: F401 [*] `.base.AgentUtils.dilate_string` imported but unused
   |
68 |     AISemanticMemory,
69 |     Scripts,
70 |     dilate_string, ControllerManager
   |     ^^^^^^^^^^^^^ F401
71 | )
72 | from .CodingAgent.live import Pipeline
   |
   = help: Remove unused import: `.base.AgentUtils.dilate_string`

toolboxv2\mods\isaa\module.py:76:5: F401 [*] `.extras.modes.StrictFormatResponder` imported but unused
   |
74 |     ISAA0CODE,  # Assuming this is a constant string
75 |     ChainTreeExecutor,
76 |     StrictFormatResponder,
   |     ^^^^^^^^^^^^^^^^^^^^^ F401
77 |     SummarizationMode,
78 |     TaskChainMode,
   |
   = help: Remove unused import: `.extras.modes.StrictFormatResponder`

toolboxv2\mods\isaa\module.py:98:16: S113 Probable use of `requests` call without timeout
   |
97 | def get_ip():
98 |     response = requests.get('https://api64.ipify.org?format=json').json()
   |                ^^^^^^^^^^^^ S113
99 |     return response["ip"]
   |

toolboxv2\mods\isaa\module.py:105:16: S113 Probable use of `requests` call without timeout
    |
103 | def get_location():
104 |     ip_address = get_ip()
105 |     response = requests.get(f'https://ipapi.co/{ip_address}/json/').json()
    |                ^^^^^^^^^^^^ S113
106 |     location_data = f"city: {response.get('city')},region: {response.get('region')},country: {response.get('country_name')},"
107 |     return location_data
    |

toolboxv2\mods\isaa\module.py:335:13: F841 Local variable `builder_instance` is assigned to but never used
    |
333 |             builder_instance = self.get_agent_builder(agent_name)  # Gets a builder
334 |         elif isinstance(agent_name, EnhancedAgentBuilder):
335 |             builder_instance = agent_name
    |             ^^^^^^^^^^^^^^^^ F841
336 |         else:
337 |             raise ValueError(f"Invalid Type {type(agent_name)} accept ar: str and EnhancedAgentBuilder")
    |
    = help: Remove assignment to unused variable `builder_instance`

toolboxv2\mods\isaa\module.py:342:13: F841 Local variable `tools_config` is assigned to but never used
    |
341 |         if "tools" in a_keys:
342 |             tools_config = augment['tools']  # This config needs to define how tools are added to builder
    |             ^^^^^^^^^^^^ F841
343 |             # model_for_tools = tools_config.get("tools.model", self.config['DEFAULTMODEL_LF_TOOLS']) # model is per agent
344 |             # Await self.init_tools(tools_config, builder_instance) # init_tools needs to work with builder
    |
    = help: Remove assignment to unused variable `tools_config`

toolboxv2\mods\isaa\module.py:415:13: SIM118 [*] Use `key in dict` instead of `key in dict.keys()`
    |
413 |         self.agent_data.update(data)
414 |         # Clear instances from self.config so they are rebuilt with new configs
415 |         for agent_name in data.keys():
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^ SIM118
416 |             self.config.pop(f'agent-instance-{agent_name}', None)
    |
    = help: Remove `.keys()`

toolboxv2\mods\isaa\module.py:883:19: S602 `subprocess` call with `shell=True` identified, security issue
    |
881 |             full_cmd = f"{shell_cmd} -c {shlex.quote(command)}"
882 |
883 |         process = subprocess.run(full_cmd, shell=True, check=False, capture_output=True, timeout=120, text=False)
    |                   ^^^^^^^^^^^^^^ S602
884 |
885 |         stdout = safe_decode(process.stdout)
    |

toolboxv2\mods\isaa\ui.py:2:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/isaa/ui.py
 2 | / import asyncio
 3 | | import json
 4 | | import time  # Keep for now, might be useful elsewhere
 5 | | import uuid
 6 | | from typing import Dict, Optional, List, Any, AsyncGenerator
 7 | |
 8 | | from pydantic import BaseModel, Field
 9 | |
10 | | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   | |____________________________________________________________________________________________________________________^ I001
11 |
12 |   # Moduldefinition
   |
   = help: Organize imports

toolboxv2\mods\isaa\ui.py:3:8: F401 [*] `json` imported but unused
  |
1 | # toolboxv2/mods/isaa/ui.py
2 | import asyncio
3 | import json
  |        ^^^^ F401
4 | import time  # Keep for now, might be useful elsewhere
5 | import uuid
  |
  = help: Remove unused import: `json`

toolboxv2\mods\isaa\ui.py:4:8: F401 [*] `time` imported but unused
  |
2 | import asyncio
3 | import json
4 | import time  # Keep for now, might be useful elsewhere
  |        ^^^^ F401
5 | import uuid
6 | from typing import Dict, Optional, List, Any, AsyncGenerator
  |
  = help: Remove unused import: `time`

toolboxv2\mods\isaa\ui.py:6:1: UP035 [*] Import from `collections.abc` instead: `AsyncGenerator`
  |
4 | import time  # Keep for now, might be useful elsewhere
5 | import uuid
6 | from typing import Dict, Optional, List, Any, AsyncGenerator
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
7 |
8 | from pydantic import BaseModel, Field
  |
  = help: Import from `collections.abc`

toolboxv2\mods\isaa\ui.py:6:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
4 | import time  # Keep for now, might be useful elsewhere
5 | import uuid
6 | from typing import Dict, Optional, List, Any, AsyncGenerator
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
7 |
8 | from pydantic import BaseModel, Field
  |

toolboxv2\mods\isaa\ui.py:6:1: UP035 `typing.List` is deprecated, use `list` instead
  |
4 | import time  # Keep for now, might be useful elsewhere
5 | import uuid
6 | from typing import Dict, Optional, List, Any, AsyncGenerator
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
7 |
8 | from pydantic import BaseModel, Field
  |

toolboxv2\mods\isaa\ui.py:6:36: F401 [*] `typing.List` imported but unused
  |
4 | import time  # Keep for now, might be useful elsewhere
5 | import uuid
6 | from typing import Dict, Optional, List, Any, AsyncGenerator
  |                                    ^^^^ F401
7 |
8 | from pydantic import BaseModel, Field
  |
  = help: Remove unused import: `typing.List`

toolboxv2\mods\isaa\ui.py:8:33: F401 [*] `pydantic.Field` imported but unused
   |
 6 | from typing import Dict, Optional, List, Any, AsyncGenerator
 7 |
 8 | from pydantic import BaseModel, Field
   |                                 ^^^^^ F401
 9 |
10 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |
   = help: Remove unused import: `pydantic.Field`

toolboxv2\mods\isaa\ui.py:10:58: F401 [*] `toolboxv2.ToolBoxError` imported but unused
   |
 8 | from pydantic import BaseModel, Field
 9 |
10 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                          ^^^^^^^^^^^^ F401
11 |
12 | # Moduldefinition
   |
   = help: Remove unused import

toolboxv2\mods\isaa\ui.py:10:72: F401 [*] `toolboxv2.ToolBoxResult` imported but unused
   |
 8 | from pydantic import BaseModel, Field
 9 |
10 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                                        ^^^^^^^^^^^^^ F401
11 |
12 | # Moduldefinition
   |
   = help: Remove unused import

toolboxv2\mods\isaa\ui.py:10:87: F401 [*] `toolboxv2.ToolBoxInfo` imported but unused
   |
 8 | from pydantic import BaseModel, Field
 9 |
10 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                                                       ^^^^^^^^^^^ F401
11 |
12 | # Moduldefinition
   |
   = help: Remove unused import

toolboxv2\mods\isaa\ui.py:10:100: F401 [*] `toolboxv2.ToolBoxInterfaces` imported but unused
   |
 8 | from pydantic import BaseModel, Field
 9 |
10 | from toolboxv2 import get_app, App, RequestData, Result, ToolBoxError, ToolBoxResult, ToolBoxInfo, ToolBoxInterfaces
   |                                                                                                    ^^^^^^^^^^^^^^^^^ F401
11 |
12 | # Moduldefinition
   |
   = help: Remove unused import

toolboxv2\mods\isaa\ui.py:42:17: UP007 [*] Use `X | Y` for type annotations
   |
40 |     agent_name: str = "self"
41 |     prompt: str
42 |     session_id: Optional[str] = None
   |                 ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

toolboxv2\mods\isaa\ui.py:60:59: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
58 |         agent = await isaa.get_agent(agent_name)  # isaa.get_agent is async
59 |
60 |         async def sse_event_generator() -> AsyncGenerator[Dict[str, Any], None]:
   |                                                           ^^^^ UP006
61 |             # This generator yields dictionaries that SSEGenerator will format.
62 |             # SSEGenerator will add 'stream_start' and 'stream_end' events.
   |
   = help: Replace with `dict`

toolboxv2\mods\isaa\ui.py:95:28: UP041 [*] Replace aliased errors with `TimeoutError`
   |
93 |                         yield event_dict
94 |                         event_queue.task_done()
95 |                     except asyncio.TimeoutError:
   |                            ^^^^^^^^^^^^^^^^^^^^ UP041
96 |                         if agent_processing_task.done() and event_queue.empty():
97 |                             break  # Agent finished and queue is empty
   |
   = help: Replace `asyncio.TimeoutError` with builtin `TimeoutError`

toolboxv2\mods\isaa\ui.py:121:62: F821 Undefined name `e_outer`
    |
119 |         # For setup errors, we also need to yield through an async generator for Result.sse
120 |         async def error_event_generator():
121 |             yield {'event': 'error', 'data': {'message': str(e_outer)}}
    |                                                              ^^^^^^^ F821
122 |
123 |         return Result.sse(stream_generator=error_event_generator())
    |

toolboxv2\mods\isaa\ui.py:129:17: UP007 [*] Use `X | Y` for type annotations
    |
127 |     agent_name: str = "self"
128 |     prompt: str
129 |     session_id: Optional[str] = None
    |                 ^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\ui.py:161:42: UP007 [*] Use `X | Y` for type annotations
    |
160 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
161 | async def list_agents(app: App, request: Optional[RequestData] = None):
    |                                          ^^^^^^^^^^^^^^^^^^^^^ UP007
162 |     isaa = get_isaa_instance(app)
163 |     agent_names = []
    |
    = help: Convert to `X | Y`

toolboxv2\mods\isaa\ui.py:198:50: UP007 [*] Use `X | Y` for type annotations
    |
196 | # --- Hauptseite ---
197 | @export(mod_name=MOD_NAME, api=True, version=VERSION, name="main", api_methods=['GET'])
198 | async def get_isaa_webui_page(app: App, request: Optional[RequestData] = None):
    |                                                  ^^^^^^^^^^^^^^^^^^^^^ UP007
199 |     if app is None:  # Should not happen if called via export
200 |         app = get_app()
    |
    = help: Convert to `X | Y`

toolboxv2\mods\talk.py:223:25: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
221 |                               print("NEW Magic-Number:", i)
222 |                               break
223 | /                         except Exception:
224 | |                             pass
    | |________________________________^ S110
225 |               else:
226 |                   text = stt(audio_data)['text']
    |

toolboxv2\mods\welcome.py:51:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
49 | def cls():
50 |     if system() == "Windows":
51 |         os.system("cls")
   |         ^^^^^^^^^ S605
52 |     else:
53 |         os.system("clear")
   |

toolboxv2\mods\welcome.py:53:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
51 |         os.system("cls")
52 |     else:
53 |         os.system("clear")
   |         ^^^^^^^^^ S605
   |

toolboxv2\setup_helper.py:66:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
64 |       print("🔧 Installiere Dev-Tools...")
65 |       d = ["cargo", "node"]
66 | /     if a := input("With docker (N/y)"):
67 | |         if a.lower() == 'y':
   | |____________________________^ SIM102
68 |               d.append("docker")
69 |       for _d in d.copy():
   |
   = help: Combine `if` statements using `and`

toolboxv2\setup_helper.py:152:9: S602 `subprocess` call with `shell=True` identified, security issue
    |
150 |         cwd = _cwd
151 |     try:
152 |         subprocess.run(command, cwd=cwd, shell=True, check=True,
    |         ^^^^^^^^^^^^^^ S602
153 |                        stdout=subprocess.PIPE if silent else None)
154 |         return True
    |

toolboxv2\tests\test_utils\test_daemon\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(daemon_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await DaemonUtil(class_instance=None, host='0.0.0.0', port=6582, t=False,
20 |                              app=None, peer=False, name='daemonApp-server',
   |

toolboxv2\tests\test_utils\test_proxy\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(proxy_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
20 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

toolboxv2\tests\test_utils\test_proxy\test.py:23:14: B017 Do not assert blind exception: `Exception`
   |
21 |                                          unix_socket=False)
22 |
23 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
24 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
25 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:63:22: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
   |
61 |         # Check if the environment exists
62 |         try:
63 |             output = subprocess.check_output("conda env list", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
64 |             self.assertIn(self.test_env_name, output)
65 |         except subprocess.CalledProcessError:
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:81:13: S602 `subprocess` call with `shell=True` identified, security issue
   |
79 |         # Check that the environment no longer exists
80 |         with self.assertRaises(subprocess.CalledProcessError):
81 |             subprocess.check_output(f"conda env list | grep {self.test_env_name}", shell=True, text=True)
   |             ^^^^^^^^^^^^^^^^^^^^^^^ S602
82 |
83 |     def test_add_dependency(self):
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:96:22: S602 `subprocess` call with `shell=True` identified, security issue
   |
94 |         # Verify the dependency was added by checking conda list
95 |         try:
96 |             output = subprocess.check_output(f"conda list -n {self.test_env_name} numpy", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
97 |             self.assertIn("numpy", output)
98 |         except subprocess.CalledProcessError:
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:148:14: B017 Do not assert blind exception: `Exception`
    |
146 |         if not get_app(name="test").local_test:
147 |             return
148 |         with self.assertRaises(Exception):
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
149 |             res = add_dependency(self.test_env_name, "non_existent_package_xyz")
150 |             if res is False:
    |

toolboxv2\tests\web_test\__init__.py:1:1: F403 `from .test_main_page import *` used; unable to detect undefined names
  |
1 | from .test_main_page import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 |
3 | in_valid_session_tests = []
  |

toolboxv2\tests\web_test\__init__.py:4:24: F405 `contact_page_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                        ^^^^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

toolboxv2\tests\web_test\__init__.py:4:51: F405 `installer_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                                                   ^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

toolboxv2\tests\web_util.py:22:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
20 |     )
21 | except ImportError:
22 |     os.system("pip install playwright")
   |     ^^^^^^^^^ S605
23 |     from playwright.async_api import Browser as ABrowser
24 |     from playwright.async_api import BrowserContext as ABrowserContext
   |

toolboxv2\utils\__init__.py:1:8: F401 `os` imported but unused
  |
1 | import os
  |        ^^ F401
2 |
3 | from yaml import safe_load
  |
  = help: Remove unused import: `os`

toolboxv2\utils\__init__.py:3:18: F401 `yaml.safe_load` imported but unused
  |
1 | import os
2 |
3 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
4 |
5 | from .extras.show_and_hide_console import show_console
  |
  = help: Remove unused import: `yaml.safe_load`

toolboxv2\utils\brodcast\server.py:32:5: S110 `try`-`except`-`pass` detected, consider logging the exception
   |
30 |           data = server.recv(1024)
31 |           print(f"data received! {data.decode()}", flush=True)
32 | /     except:
33 | |         pass
   | |____________^ S110
34 |       finally:
35 |           server.close()
   |

toolboxv2\utils\daemon\daemon_util.py:10:1: F403 `from ..system.all_functions_enums import *` used; unable to detect undefined names
   |
 8 | from ..extras.show_and_hide_console import show_console
 9 | from ..extras.Style import Style
10 | from ..system.all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
11 | from ..system.getting_and_closing_app import get_app
12 | from ..system.tb_logger import get_logger
   |

toolboxv2\utils\daemon\daemon_util.py:85:45: F405 `SOCKETMANAGER` may be undefined, or defined from star imports
   |
83 |         if not app.mod_online("SocketManager"):
84 |             await app.load_mod("SocketManager")
85 |         server_result = await app.a_run_any(SOCKETMANAGER.CREATE_SOCKET,
   |                                             ^^^^^^^^^^^^^ F405
86 |                                             get_results=True,
87 |                                             name=self._name,
   |

toolboxv2\utils\daemon\daemon_util.py:216:71: B023 Function definition does not bind loop variable `name`
    |
214 |                         async def _helper_runner():
215 |                             try:
216 |                                 attr_f = getattr(self.class_instance, name)
    |                                                                       ^^^^ B023
217 |
218 |                                 if asyncio.iscoroutinefunction(attr_f):
    |

toolboxv2\utils\daemon\daemon_util.py:219:57: B023 Function definition does not bind loop variable `args`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                               ^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

toolboxv2\utils\daemon\daemon_util.py:219:65: B023 Function definition does not bind loop variable `kwargs`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                                       ^^^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

toolboxv2\utils\daemon\daemon_util.py:221:51: B023 Function definition does not bind loop variable `args`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                   ^^^^ B023
222 |
223 |                                 if res is None:
    |

toolboxv2\utils\daemon\daemon_util.py:221:59: B023 Function definition does not bind loop variable `kwargs`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                           ^^^^^^ B023
222 |
223 |                                 if res is None:
    |

toolboxv2\utils\daemon\daemon_util.py:237:51: B023 Function definition does not bind loop variable `identifier`
    |
235 |                                 get_logger().info(f"sending response {res} {type(res)}")
236 |
237 |                                 await sender(res, identifier)
    |                                                   ^^^^^^^^^^ B023
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |

toolboxv2\utils\daemon\daemon_util.py:239:64: B023 Function definition does not bind loop variable `identifier`
    |
237 |                                 await sender(res, identifier)
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |                                                                ^^^^^^^^^^ B023
240 |
241 |                         await _helper_runner()
    |

toolboxv2\utils\extras\Style.py:45:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
43 | def cls():
44 |     if system() == "Windows":
45 |         os.system("cls")
   |         ^^^^^^^^^ S605
46 |     if system() == "Linux":
47 |         os.system("clear")
   |

toolboxv2\utils\extras\Style.py:47:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
45 |         os.system("cls")
46 |     if system() == "Linux":
47 |         os.system("clear")
   |         ^^^^^^^^^ S605
   |

toolboxv2\utils\extras\Style.py:369:28: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
367 |             if i < len(words) - 1:
368 |                 print(" ", end="", flush=True)
369 |             typing_speed = uniform(min_typing_speed, max_typing_speed)
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
370 |             time.sleep(typing_speed)
371 |             # type faster after each word
    |

toolboxv2\utils\extras\base_widget.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / import asyncio
2 | | import uuid
  | |___________^ I001
  |
  = help: Organize imports

toolboxv2\utils\extras\base_widget.py:84:24: B023 Function definition does not bind loop variable `fuction`
   |
82 |         for fuction in functions:
83 |             def x(r):
84 |                 return fuction(request=r)
   |                        ^^^^^^^ B023
85 |             self.onReload.append(x)
   |

toolboxv2\utils\extras\base_widget.py:152:66: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
150 |         return asset
151 |
152 |     def generate_html(self, app, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                  ^^^^^^^^^^^^ B008
153 |         return app.run_any(MINIMALHTML.GENERATE_HTML,
154 |                            group_name=self.name,
    |

toolboxv2\utils\extras\base_widget.py:157:73: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
155 |                            collection_name=f"{name}-{asset_id}")
156 |
157 |     def load_widget(self, app, request, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                         ^^^^^^^^^^^^ B008
158 |         app.run_any(MINIMALHTML.ADD_GROUP, command=self.name)
159 |         self.reload(request)
    |

toolboxv2\utils\extras\blobs.py:55:13: SIM113 Use `enumerate()` for index variable `current_blob_id` in `for` loop
   |
53 |                 if index_ + 1 > len(blob_ids) and len(all_link[i + splitter:]) > 1:
54 |                     self.add_link(blob_ids[current_blob_id], blob_ids[current_blob_id], link_port)
55 |             current_blob_id += 1
   |             ^^^^^^^^^^^^^^^^^^^^ SIM113
56 |
57 |     def recover_blob(self, blob_ids, check_blobs_ids):
   |

toolboxv2\utils\extras\blobs.py:158:20: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
156 |             return self.create_blob(pickle.dumps({}), blob_id)
157 |         with open(blob_file, 'rb') as f:
158 |             return pickle.load(f)
    |                    ^^^^^^^^^^^^^^ S301
159 |
160 |     def _generate_recovery_bytes(self, blob_id):
    |

toolboxv2\utils\extras\blobs.py:180:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
178 |           self.storage = storage
179 |           self.data = b""
180 | /         if key is not None:
181 | |             if Code.decrypt_symmetric(Code.encrypt_symmetric("test", key), key) != "test":
    | |__________________________________________________________________________________________^ SIM102
182 |                   raise ValueError("Invalid Key")
183 |           self.key = key
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\extras\blobs.py:202:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
200 |     def __enter__(self):
201 |         if 'r' in self.mode:
202 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
203 |             if self.folder in blob_data:
204 |                 blob_folder = blob_data[self.folder]
    |

toolboxv2\utils\extras\blobs.py:220:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
218 |             if self.key is not None:
219 |                 data = Code.encrypt_symmetric(data, self.key)
220 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
221 |             if self.folder not in blob_data:
222 |                 blob_data[self.folder] = {self.datei: data}
    |

toolboxv2\utils\extras\blobs.py:268:16: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
266 |         if self.data == b"":
267 |             return {}
268 |         return pickle.loads(self.data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^ S301
269 |
270 |     def write_pickle(self, data):
    |

toolboxv2\utils\extras\bottleup.py:18:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
16 |     except ImportError:
17 |         print("Bottle is not available auto installation")
18 |         os.system("pip install bottle")
   |         ^^^^^^^^^ S605
19 |         return bottle_up(tb_app, user=user, main_route=main_route, **kwargs)
   |

toolboxv2\utils\extras\bottleup.py:125:112: B023 Function definition does not bind loop variable `tb_func`
    |
123 |                         if request_as_kwarg:
124 |                             def tb_func_(**kw):
125 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read()+tb_func(**kw)
    |                                                                                                                ^^^^^^^ B023
126 |                         else:
127 |                             def tb_func_():
    |

toolboxv2\utils\extras\bottleup.py:128:114: B023 Function definition does not bind loop variable `tb_func`
    |
126 |                         else:
127 |                             def tb_func_():
128 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read() + tb_func()
    |                                                                                                                  ^^^^^^^ B023
129 |                         self.route(f'/{mod_name}', method='GET')(tb_func_)
130 |                         print("adding root:", f'/{mod_name}')
    |

toolboxv2\utils\extras\gist_control.py:20:9: S102 Use of `exec` detected
   |
18 |         # Erstelle ein neues Modul
19 |         module = importlib.util.module_from_spec(self.get_spec(module_name))
20 |         exec(self.module_code, module.__dict__)
   |         ^^^^ S102
21 |         return module
   |

toolboxv2\utils\extras\gist_control.py:35:20: S113 Probable use of `requests` call without timeout
   |
33 |         api_url = f"https://api.github.com/gists/{gist_id}"
34 |
35 |         response = requests.get(api_url)
   |                    ^^^^^^^^^^^^ S113
36 |
37 |         if response.status_code == 200:
   |

toolboxv2\utils\extras\gist_control.py:77:20: S113 Probable use of `requests` call without timeout
   |
75 |         # Update an existing Gist
76 |         url = f"https://api.github.com/gists/{gist_id}"
77 |         response = requests.patch(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^^ S113
78 |     else:
79 |         # Create a new Gist
   |

toolboxv2\utils\extras\gist_control.py:81:20: S113 Probable use of `requests` call without timeout
   |
79 |         # Create a new Gist
80 |         url = "https://api.github.com/gists"
81 |         response = requests.post(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^ S113
82 |
83 |     # Check if the request was successful
   |

toolboxv2\utils\extras\helper_test_functions.py:52:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
50 |     """
51 |     if param_type in [int, float]:
52 |         return random.randint(0, 100)  # Zufällige normale Zahlen
   |                ^^^^^^^^^^^^^^^^^^^^^^ S311
53 |     elif param_type == str:
54 |         return "test" # Zufälliges Wort
   |

toolboxv2\utils\proxy\prox_util.py:149:21: SIM102 Use a single `if` statement instead of nested `if` statements
    |
147 |                               return await app_attr(*args, **kwargs)
148 |                           return app_attr(*args, **kwargs)
149 | /                     if (name == 'run_any' or name == 'a_run_any') and kwargs.get('get_results', False):
150 | |                         if isinstance(args[0], Enum):
    | |_____________________________________________________^ SIM102
151 |                               args = (args[0].__class__.NAME.value, args[0].value), args[1:]
152 |                       self.app.sprint(f"Calling method {name}, {args=}, {kwargs}=")
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\security\cryp.py:76:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
74 |             int: Eine zufällige Zahl.
75 |         """
76 |         return random.randint(2 ** 32 - 1, 2 ** 64 - 1)
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
77 |
78 |     @staticmethod
   |

toolboxv2\utils\security\cryp.py:334:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
332 |               )
333 |               return True
334 | /         except:
335 | |             pass
    | |________________^ S110
336 |           return False
    |

toolboxv2\utils\security\cryp.py:361:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
359 |               )
360 |               return True
361 | /         except:
362 | |             pass
    | |________________^ S110
363 |           return False
    |

toolboxv2\utils\system\__init__.py:1:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
  |
1 | from .all_functions_enums import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 | from .cache import FileCache, MemoryCache
3 | from .file_handler import FileHandler
  |

toolboxv2\utils\system\api.py:151:20: S113 Probable use of `requests` call without timeout
    |
149 |     print(f"Attempting to download executable from {url}...")
150 |     try:
151 |         response = requests.get(url, stream=True)
    |                    ^^^^^^^^^^^^ S113
152 |     except Exception as e:
153 |         print(f"Download error: {e}")
    |

toolboxv2\utils\system\api.py:163:33: S103 `os.chmod` setting a permissive mask `0o755` on file or directory
    |
161 |         # Make the file executable on non-Windows systems
162 |         if platform.system().lower() != "windows":
163 |             os.chmod(file_name, 0o755)
    |                                 ^^^^^ S103
164 |         return file_name
165 |     else:
    |

toolboxv2\utils\system\api.py:350:20: S108 Probable insecure usage of temporary file or directory: "/tmp/dill_package"
    |
348 |     """Package dill and all dependencies into a single .dill archive."""
349 |     try:
350 |         temp_dir = "/tmp/dill_package"
    |                    ^^^^^^^^^^^^^^^^^^^ S108
351 |         os.makedirs(temp_dir, exist_ok=True)
    |

toolboxv2\utils\system\api.py:541:13: I001 [*] Import block is un-sorted or un-formatted
    |
539 |             os.set_inheritable(fd_num, True)
540 |         else: # POSIX, Python < 3.4 (fcntl not on Windows)
541 |             import fcntl # Import fcntl here as it's POSIX specific
    |             ^^^^^^^^^^^^ I001
542 |             flags = fcntl.fcntl(fd_num, fcntl.F_GETFD)
543 |             fcntl.fcntl(fd_num, fcntl.F_SETFD, flags & ~fcntl.FD_CLOEXEC) # Ensure inheritable
    |
    = help: Organize imports

toolboxv2\utils\system\api.py:762:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
760 |                     with open(PERSISTENT_FD_FILE) as f_fd: fd_val = f_fd.read().strip()
761 |                     print(f"  Listening FD (from file, POSIX only): {fd_val}")
762 |                 except Exception: pass
    |                 ^^^^^^^^^^^^^^^^^^^^^^ S110
763 |         else:
764 |             print("Server is STOPPED (or state inconsistent).")
    |

toolboxv2\utils\system\api.py:865:9: SIM105 Use `contextlib.suppress(Exception)` instead of `try`-`except`-`pass`
    |
863 |       if args.watch:
864 |           from toolboxv2 import tb_root_dir
865 | /         try:
866 | |             subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
867 | |         except Exception as e:
868 | |             pass
    | |________________^ SIM105
869 |           return
870 |       api_manager(args.action, args.debug, args.exe, args.version)
    |
    = help: Replace with `contextlib.suppress(Exception)`

toolboxv2\utils\system\api.py:866:13: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
    |
864 |         from toolboxv2 import tb_root_dir
865 |         try:
866 |             subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
    |             ^^^^^^^^^^^^^^ S602
867 |         except Exception as e:
868 |             pass
    |

toolboxv2\utils\system\api.py:867:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
865 |           try:
866 |               subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
867 | /         except Exception as e:
868 | |             pass
    | |________________^ S110
869 |           return
870 |       api_manager(args.action, args.debug, args.exe, args.version)
    |

toolboxv2\utils\system\api.py:867:29: F841 [*] Local variable `e` is assigned to but never used
    |
865 |         try:
866 |             subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
867 |         except Exception as e:
    |                             ^ F841
868 |             pass
869 |         return
    |
    = help: Remove assignment to unused variable `e`

toolboxv2\utils\system\cache.py:16:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
14 |     def get(self, key):
15 |         try:
16 |             with shelve.open(self.filename) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
17 |                 return db.get(key.replace('\x00', ''))
18 |         except Exception:
   |

toolboxv2\utils\system\cache.py:23:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
21 |     def set(self, key, value):
22 |         try:
23 |             with shelve.open(self.filename, writeback=True) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
24 |                 db[key.replace('\x00', '')] = value
25 |         except Exception:
   |

toolboxv2\utils\system\conda_runner.py:15:19: S602 `subprocess` call with `shell=True` identified, security issue
   |
13 |     if live:
14 |         # Using subprocess.Popen to stream stdout and stderr live
15 |         process = subprocess.Popen(command, shell=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
   |                   ^^^^^^^^^^^^^^^^ S602
16 |         process.communicate()  # Wait for the process to complete
17 |         return process.returncode == 0, None
   |

toolboxv2\utils\system\conda_runner.py:21:18: S602 `subprocess` call with `shell=True` identified, security issue
   |
19 |     try:
20 |         # If not live, capture output and return it
21 |         result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True, encoding='cp850')
   |                  ^^^^^^^^^^^^^^ S602
22 |         return True, result.stdout
23 |     except subprocess.CalledProcessError as e:
   |

toolboxv2\utils\system\file_handler.py:37:41: SIM115 Use a context manager for opening files
   |
35 |             self.file_handler_storage = None
36 |         try:
37 |             self.file_handler_storage = open(self.file_handler_file_prefix + self.file_handler_filename, mode)
   |                                         ^^^^ SIM115
38 |             self.file_handler_max_loaded_index_ += 1
39 |         except FileNotFoundError:
   |

toolboxv2\utils\system\file_handler.py:60:16: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
58 |                 self.file_handler_max_loaded_index_ = -1
59 |             rdu()
60 |         except OSError and PermissionError as e:
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
61 |             raise e
   |

toolboxv2\utils\system\file_handler.py:111:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
109 |               )
110 |               return False
111 | /         if key not in self.file_handler_load:
112 | |             if key in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
113 |                   key = self.file_handler_key_mapper[key]
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\system\file_handler.py:148:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
146 |                 self.file_handler_load[key] = self.decode_code(line)
147 |
148 |         except json.decoder.JSONDecodeError and Exception:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
149 |
150 |             for line in self.file_handler_storage:
    |

toolboxv2\utils\system\file_handler.py:166:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
164 |       def get_file_handler(self, obj: str, default=None) -> str or None:
165 |           logger = get_logger()
166 | /         if obj not in self.file_handler_load:
167 | |             if obj in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
168 |                   obj = self.file_handler_key_mapper[obj]
169 |           logger.info(Style.ITALIC(Style.GREY(f"Collecting data from storage key : {obj}")))
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\system\getting_and_closing_app.py:15:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
13 |   def override_main_app(app):
14 |       global registered_apps
15 | /     if registered_apps[0] is not None:
16 | |         if time.time() - registered_apps[0].called_exit[1] > 30:
   | |________________________________________________________________^ SIM102
17 |               raise PermissionError("Permission denied because of overtime fuction override_main_app sud only be called "
18 |                                     f"once and ontime overtime {time.time() - registered_apps[0].called_exit[1]}")
   |
   = help: Combine `if` statements using `and`

toolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^^^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

toolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

toolboxv2\utils\system\ipy_completer.py:64:9: SIM102 Use a single `if` statement instead of nested `if` statements
   |
62 |       for _name, obj in inspect.getmembers(module, inspect.isclass):
63 |           # Check if the class is defined in the current module
64 | /         if obj.__module__ == module.__name__:
65 | |             # Check if the class is a dataclass
66 | |             if is_dataclass(obj):
   | |_________________________________^ SIM102
67 |                   dataclasses.append(obj)
   |
   = help: Combine `if` statements using `and`

toolboxv2\utils\system\main_tool.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import inspect
 3 | | import os
 4 | | from toolboxv2.utils.extras import Style
 5 | |
 6 | | from .getting_and_closing_app import get_app
 7 | | from .tb_logger import get_logger
 8 | | from .types import Result, ToolBoxError, ToolBoxInfo, ToolBoxInterfaces, ToolBoxResult
   | |______________________________________________________________________________________^ I001
 9 |
10 |   try:
   |
   = help: Organize imports

toolboxv2\utils\system\session.py:264:24: S113 Probable use of `requests` call without timeout
    |
262 |             except Exception as e:
263 |                 print("Error session fetch:", e, self.username)
264 |                 return requests.request(method, url, data=data)
    |                        ^^^^^^^^^^^^^^^^ S113
265 |         else:
266 |             print(f"Could not find session using request on {url}")
    |

toolboxv2\utils\system\session.py:268:24: S113 Probable use of `requests` call without timeout
    |
266 |             print(f"Could not find session using request on {url}")
267 |             if method.upper() == 'POST':
268 |                 return requests.request(method, url, json=data)
    |                        ^^^^^^^^^^^^^^^^ S113
269 |             return requests.request(method, url, data=data)
270 |             # raise Exception("Session not initialized. Please login first.")
    |

toolboxv2\utils\system\session.py:269:20: S113 Probable use of `requests` call without timeout
    |
267 |             if method.upper() == 'POST':
268 |                 return requests.request(method, url, json=data)
269 |             return requests.request(method, url, data=data)
    |                    ^^^^^^^^^^^^^^^^ S113
270 |             # raise Exception("Session not initialized. Please login first.")
    |

toolboxv2\utils\system\session.py:350:20: S113 Probable use of `requests` call without timeout
    |
348 | def get_public_ip():
349 |     try:
350 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^ S113
351 |         ip_address = response.json()['ip']
352 |         return ip_address
    |

toolboxv2\utils\system\tb_logger.py:56:29: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
54 |         log_info_data_str = li.read()
55 |         try:
56 |             log_info_data = eval(log_info_data_str)
   |                             ^^^^^^^^^^^^^^^^^^^^^^^ S307
57 |         except SyntaxError:
58 |             if log_info_data_str:
   |

toolboxv2\utils\system\types.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import cProfile
 3 | | import io
 4 | | import logging
 5 | | import multiprocessing as mp
 6 | | import os
 7 | | import pstats
 8 | | import time
 9 | | from collections.abc import Callable
10 | | from contextlib import contextmanager
11 | | from dataclasses import dataclass, field
12 | | from inspect import signature
13 | | from types import ModuleType
14 | | from typing import Any, Union
15 | |
16 | | from pydantic import BaseModel
17 | |
18 | | from ..extras import generate_test_cases
19 | | from ..extras.Style import Spinner
20 | | from .all_functions_enums import *
21 | | from .file_handler import FileHandler
22 | |
23 | | import asyncio
24 | | import base64
25 | | import inspect
26 | | import json
27 | | import traceback
28 | | from collections.abc import AsyncGenerator, Callable
29 | | from typing import Any, TypeVar
   | |_______________________________^ I001
30 |
31 |   T = TypeVar('T')
   |
   = help: Organize imports

toolboxv2\utils\system\types.py:20:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
   |
18 | from ..extras import generate_test_cases
19 | from ..extras.Style import Spinner
20 | from .all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
21 | from .file_handler import FileHandler
   |

toolboxv2\utils\system\types.py:23:8: F811 [*] Redefinition of unused `asyncio` from line 1
   |
21 | from .file_handler import FileHandler
22 |
23 | import asyncio
   |        ^^^^^^^ F811
24 | import base64
25 | import inspect
   |
   = help: Remove definition: `asyncio`

toolboxv2\utils\system\types.py:28:45: F811 [*] Redefinition of unused `Callable` from line 9
   |
26 | import json
27 | import traceback
28 | from collections.abc import AsyncGenerator, Callable
   |                                             ^^^^^^^^ F811
29 | from typing import Any, TypeVar
   |
   = help: Remove definition: `Callable`

toolboxv2\utils\system\types.py:29:20: F811 [*] Redefinition of unused `Any` from line 14
   |
27 | import traceback
28 | from collections.abc import AsyncGenerator, Callable
29 | from typing import Any, TypeVar
   |                    ^^^ F811
30 |
31 | T = TypeVar('T')
   |
   = help: Remove definition: `Any`

toolboxv2\utils\system\types.py:499:25: F405 `Enum` may be undefined, or defined from star imports
    |
499 | class ToolBoxError(str, Enum):
    |                         ^^^^ F405
500 |     none = "none"
501 |     input_error = "InputError"
    |

toolboxv2\utils\system\types.py:506:30: F405 `Enum` may be undefined, or defined from star imports
    |
506 | class ToolBoxInterfaces(str, Enum):
    |                              ^^^^ F405
507 |     cli = "CLI"
508 |     api = "API"
    |

toolboxv2\utils\system\types.py:549:62: F405 `Enum` may be undefined, or defined from star imports
    |
547 |     def as_result(self):
548 |         return Result(
549 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
550 |             result=ToolBoxResult(
551 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

toolboxv2\utils\system\types.py:551:86: F405 `Enum` may be undefined, or defined from star imports
    |
549 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
550 |             result=ToolBoxResult(
551 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
552 |                 data_info=self.result.data_info,
553 |                 data=self.result.data,
    |

toolboxv2\utils\system\types.py:591:64: F405 `Enum` may be undefined, or defined from star imports
    |
589 |     def as_dict(self):
590 |         return {
591 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                                ^^^^ F405
592 |         "result" : {
593 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

toolboxv2\utils\system\types.py:593:84: F405 `Enum` may be undefined, or defined from star imports
    |
591 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
592 |         "result" : {
593 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                    ^^^^ F405
594 |             "data_info":self.result.data_info,
595 |             "data":self.result.data,
    |

toolboxv2\utils\system\types.py:628:62: F405 `Enum` may be undefined, or defined from star imports
    |
626 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
627 |         return ApiResult(
628 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
629 |             result=ToolBoxResultBM(
630 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

toolboxv2\utils\system\types.py:630:86: F405 `Enum` may be undefined, or defined from star imports
    |
628 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
629 |             result=ToolBoxResultBM(
630 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
631 |                 data_info=self.result.data_info,
632 |                 data=self.result.data,
    |

toolboxv2\utils\system\types.py:650:46: F405 `Enum` may be undefined, or defined from star imports
    |
648 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
649 |         return ApiResult(
650 |             error=error if isinstance(error, Enum) else error,
    |                                              ^^^^ F405
651 |             result=ToolBoxResultBM(
652 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |

toolboxv2\utils\system\types.py:652:84: F405 `Enum` may be undefined, or defined from star imports
    |
650 |             error=error if isinstance(error, Enum) else error,
651 |             result=ToolBoxResultBM(
652 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |                                                                                    ^^^^ F405
653 |                 data_info=result.get('data_info', '404'),
654 |                 data=result.get('data'),
    |

toolboxv2\utils\system\types.py:676:25: UP007 [*] Use `X | Y` for type annotations
    |
674 |                stream_generator: Any,  # Renamed from source for clarity
675 |                content_type: str = "text/event-stream",  # Default to SSE
676 |                headers: Union[dict, None] = None,
    |                         ^^^^^^^^^^^^^^^^^ UP007
677 |                info: str = "OK",
678 |                interface: ToolBoxInterfaces = ToolBoxInterfaces.remote,
    |
    = help: Convert to `X | Y`

toolboxv2\utils\system\types.py:679:30: UP007 [*] Use `X | Y` for type annotations
    |
677 |                  info: str = "OK",
678 |                  interface: ToolBoxInterfaces = ToolBoxInterfaces.remote,
679 |                  cleanup_func: Union[
    |  ______________________________^
680 | |                    Callable[[], None], Callable[[], T], Callable[[], AsyncGenerator[T, None]], None] = None):
    | |____________________________________________________________________________________________________^ UP007
681 |           """
682 |           Create a streaming response Result. Handles SSE and other stream types.
    |
    = help: Convert to `X | Y`

toolboxv2\utils\system\types.py:756:27: UP007 [*] Use `X | Y` for type annotations
    |
754 |               info: str = "OK",
755 |               interface: ToolBoxInterfaces = ToolBoxInterfaces.remote,
756 |               cleanup_func: Union[
    |  ___________________________^
757 | |                 Callable[[], None], Callable[[], T], Callable[[], AsyncGenerator[T, None]], None] = None,
    | |_________________________________________________________________________________________________^ UP007
758 |               # http_headers: Optional[dict] = None # If we want to allow overriding default SSE HTTP headers
759 |               ):
    |
    = help: Convert to `X | Y`

toolboxv2\utils\system\types.py:954:45: F405 `Enum` may be undefined, or defined from star imports
    |
952 |     async def aget(self, key=None, default=None):
953 |         if asyncio.isfuture(self.result.data) or asyncio.iscoroutine(self.result.data) or (
954 |             isinstance(self.result.data_to, Enum) and self.result.data_to.name == ToolBoxInterfaces.future.name):
    |                                             ^^^^ F405
955 |             data = await self.result.data
956 |         else:
    |

toolboxv2\utils\system\types.py:1104:35: F405 `CLOUDM_AUTHMANAGER` may be undefined, or defined from star imports
     |
1103 |     async def get_user(self, username: str) -> Result:
1104 |         return self.app.a_run_any(CLOUDM_AUTHMANAGER.GET_USER_BY_NAME, username=username, get_results=True)
     |                                   ^^^^^^^^^^^^^^^^^^ F405
     |

toolboxv2\utils\system\types.py:1262:29: F405 `Enum` may be undefined, or defined from star imports
     |
1261 |     def _get_function(self,
1262 |                       name: Enum or None,
     |                             ^^^^ F405
1263 |                       state: bool = True,
1264 |                       specification: str = "app",
     |

toolboxv2\utils\system\types.py:1321:34: F405 `Enum` may be undefined, or defined from star imports
     |
1319 |         """proxi attr"""
1320 |
1321 |     def get_function(self, name: Enum or tuple, **kwargs):
     |                                  ^^^^ F405
1322 |         """
1323 |         Kwargs for _get_function
     |

toolboxv2\utils\system\types.py:1336:47: F405 `Enum` may be undefined, or defined from star imports
     |
1334 |         """
1335 |
1336 |     def run_function(self, mod_function_name: Enum or tuple,
     |                                               ^^^^ F405
1337 |                      tb_run_function_with_state=True,
1338 |                      tb_run_with_specification='app',
     |

toolboxv2\utils\system\types.py:1346:55: F405 `Enum` may be undefined, or defined from star imports
     |
1344 |         """proxi attr"""
1345 |
1346 |     async def a_run_function(self, mod_function_name: Enum or tuple,
     |                                                       ^^^^ F405
1347 |                              tb_run_function_with_state=True,
1348 |                              tb_run_with_specification='app',
     |

toolboxv2\utils\system\types.py:1376:49: F405 `Enum` may be undefined, or defined from star imports
     |
1374 |         """
1375 |
1376 |     async def run_http(self, mod_function_name: Enum or str or tuple, function_name=None, method="GET",
     |                                                 ^^^^ F405
1377 |                        args_=None,
1378 |                        kwargs_=None,
     |

toolboxv2\utils\system\types.py:1382:42: F405 `Enum` may be undefined, or defined from star imports
     |
1380 |         """run a function remote via http / https"""
1381 |
1382 |     def run_any(self, mod_function_name: Enum or str or tuple, backwords_compability_variabel_string_holder=None,
     |                                          ^^^^ F405
1383 |                 get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
1384 |                 kwargs_=None,
     |

toolboxv2\utils\system\types.py:1388:50: F405 `Enum` may be undefined, or defined from star imports
     |
1386 |         """proxi attr"""
1387 |
1388 |     async def a_run_any(self, mod_function_name: Enum or str or tuple,
     |                                                  ^^^^ F405
1389 |                         backwords_compability_variabel_string_holder=None,
1390 |                         get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
     |

toolboxv2\utils\system\types.py:1937:13: F841 Local variable `original_data_type_was_complex` is assigned to but never used
     |
1935 |         original_data_type_was_complex = False
1936 |         if not isinstance(data, str):
1937 |             original_data_type_was_complex = True
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F841
1938 |             try:
1939 |                 data_str = json.dumps(data)
     |
     = help: Remove assignment to unused variable `original_data_type_was_complex`

toolboxv2\utils\system\types.py:1960:28: UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
     |
1958 |                         payload_content = json_data['data']
1959 |                         # If payload_content is complex, re-serialize it to JSON string
1960 |                         if isinstance(payload_content, (dict, list)):
     |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP038
1961 |                             sse_data_field = json.dumps(payload_content)
1962 |                         else:  # Simple type (string, number, bool)
     |
     = help: Convert to `X | Y`

toolboxv2\utils\system\types.py:2015:23: UP007 [*] Use `X | Y` for type annotations
     |
2013 |         cls,
2014 |         source: Any,  # Changed from positional arg to keyword for clarity in Result.stream
2015 |         cleanup_func: Union[Callable[[], None], Callable[[], T], Callable[[], AsyncGenerator[T, None]], None] = None
     |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
2016 |     ) -> AsyncGenerator[str, None]:
2017 |         """
     |
     = help: Convert to `X | Y`

toolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^^^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

toolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

toolboxv2\utils\toolbox.py:329:9: S605 Starting a process with a shell, possible injection detected
    |
327 |             return
328 |         self.print(f"Installing {module_name} GREEDY")
329 |         os.system(f"{sys.executable} -m pip install {module_name}")
    |         ^^^^^^^^^ S605
330 |
331 |     def python_module_import_classifier(self, mod_name, error_message):
    |

toolboxv2\utils\toolbox.py:689:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
687 |                           # Allow tasks time to clean up
688 |                           loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
689 | /                 except Exception:
690 | |                     pass
    | |________________________^ S110
691 |
692 |                   loop.close()
    |

toolboxv2\utils\toolbox.py:879:60: B023 Function definition does not bind loop variable `result`
    |
877 | …                     async def _():
878 | …                         try:
879 | …                             if asyncio.iscoroutine(result):
    |                                                      ^^^^^^ B023
880 | …                                 await result
881 | …                             if hasattr(result, 'Name'):
    |

toolboxv2\utils\toolbox.py:880:47: B023 Function definition does not bind loop variable `result`
    |
878 | …                     try:
879 | …                         if asyncio.iscoroutine(result):
880 | …                             await result
    |                                     ^^^^^^ B023
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
    |

toolboxv2\utils\toolbox.py:881:48: B023 Function definition does not bind loop variable `result`
    |
879 | …                     if asyncio.iscoroutine(result):
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
    |                                  ^^^^^^ B023
882 | …                         print('Opened :', result.Name)
883 | …                     elif hasattr(result, 'name'):
    |

toolboxv2\utils\toolbox.py:882:59: B023 Function definition does not bind loop variable `result`
    |
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
882 | …                         print('Opened :', result.Name)
    |                                             ^^^^^^ B023
883 | …                     elif hasattr(result, 'name'):
884 | …                         print('Opened :', result.name)
    |

toolboxv2\utils\toolbox.py:883:50: B023 Function definition does not bind loop variable `result`
    |
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
    |                                        ^^^^^^ B023
884 | …                             print('Opened :', result.name)
885 | …                     except Exception as e:
    |

toolboxv2\utils\toolbox.py:884:59: B023 Function definition does not bind loop variable `result`
    |
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
884 | …                             print('Opened :', result.name)
    |                                                 ^^^^^^ B023
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
    |

toolboxv2\utils\toolbox.py:887:48: B023 Function definition does not bind loop variable `result`
    |
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
887 | …                         if hasattr(result, 'Name'):
    |                                      ^^^^^^ B023
888 | …                             print('Error opening :', result.Name)
889 | …                         elif hasattr(result, 'name'):
    |

toolboxv2\utils\toolbox.py:888:66: B023 Function definition does not bind loop variable `result`
    |
886 | …                     self.debug_rains(e)
887 | …                     if hasattr(result, 'Name'):
888 | …                         print('Error opening :', result.Name)
    |                                                    ^^^^^^ B023
889 | …                     elif hasattr(result, 'name'):
890 | …                         print('Error opening :', result.name)
    |

toolboxv2\utils\toolbox.py:889:50: B023 Function definition does not bind loop variable `result`
    |
887 | …                             if hasattr(result, 'Name'):
888 | …                                 print('Error opening :', result.Name)
889 | …                             elif hasattr(result, 'name'):
    |                                            ^^^^^^ B023
890 | …                                 print('Error opening :', result.name)
891 | …                     asyncio.create_task(_())
    |

toolboxv2\utils\toolbox.py:890:66: B023 Function definition does not bind loop variable `result`
    |
888 |                                         print('Error opening :', result.Name)
889 |                                     elif hasattr(result, 'name'):
890 |                                         print('Error opening :', result.name)
    |                                                                  ^^^^^^ B023
891 |                             asyncio.create_task(_())
892 |                         else:
    |

toolboxv2\utils\toolbox.py:1591:37: SIM115 Use a context manager for opening files
     |
1589 |         if self._web_context is None:
1590 |             try:
1591 |                 self._web_context = open("./dist/helper.html", encoding="utf-8").read()
     |                                     ^^^^ SIM115
1592 |             except Exception as e:
1593 |                 self.logger.error(f"Could not load web context: {e}")
     |

toolboxv2\utils\toolbox.py:1661:21: S102 Use of `exec` detected
     |
1659 |                     code = compile(source, module.__file__, 'exec')
1660 |                     # Execute the code in the module's namespace
1661 |                     exec(code, module.__dict__)
     |                     ^^^^ S102
1662 |                 except Exception:
1663 |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
     |

toolboxv2\utils\toolbox.py:1662:17: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
1660 |                       # Execute the code in the module's namespace
1661 |                       exec(code, module.__dict__)
1662 | /                 except Exception:
1663 | |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
1664 | |                     pass
     | |________________________^ S110
1665 |                   return module
     |

uv_api_python_helper.py:11:9: S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
   |
 9 |     if not os.path.exists(dest):
10 |         print(f"Downloading {url}...")
11 |         urllib.request.urlretrieve(url, dest)
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S310
   |

uv_api_python_helper.py:46:13: S202 Uses of `tarfile.extractall()`
   |
44 |         download_file(python_url, archive_path)
45 |         with tarfile.open(archive_path, "r:gz") as tar:
46 |             tar.extractall(target_dir) # nosec: S202
   |             ^^^^^^^^^^^^^^ S202
47 |         os.remove(archive_path)
48 |         python_extracted = os.path.join(target_dir, f"Python-{version}")
   |

uv_api_python_helper.py:57:18: UP022 Prefer `capture_output` over sending `stdout` and `stderr` to `PIPE`
   |
55 |   def pip_exists(python_exe):
56 |       try:
57 |           result = subprocess.run(
   |  __________________^
58 | |             [python_exe, "-m", "pip", "--version"],
59 | |             stdout=subprocess.PIPE,
60 | |             stderr=subprocess.PIPE,
61 | |             check=True
62 | |         )
   | |_________^ UP022
63 |           print(result.stdout.decode().strip())
64 |           print("pip already installed.")
   |
   = help: Replace with `capture_output` keyword argument

~oolboxv2\__gui__.py:10:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
 8 |     import customtkinter as ctk
 9 | except ImportError:
10 |     os.system("pip install customtkinter")
   |     ^^^^^^^^^ S605
11 |     import customtkinter as ctk
   |

~oolboxv2\__init__.py:4:18: F401 `yaml.safe_load` imported but unused
  |
2 | import os
3 |
4 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
5 |
6 | try:
  |
  = help: Remove unused import: `yaml.safe_load`

~oolboxv2\__init__.py:116:12: F401 `toolboxv2.mods` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
114 | try:
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
    |            ^^^^^^^^^^^^^^ F401
117 |     from toolboxv2.mods import *
118 | except ImportError as e:
    |
    = help: Remove unused import: `toolboxv2.mods`

~oolboxv2\__init__.py:117:5: F403 `from toolboxv2.mods import *` used; unable to detect undefined names
    |
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
117 |     from toolboxv2.mods import *
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
118 | except ImportError as e:
119 |     MODS_ERROR = e
    |

~oolboxv2\__init__.py:129:22: F401 `platform.node` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                      ^^^^ F401
130 |
131 | __cwd__ = cwd = Path.cwd()
    |
    = help: Remove unused import

~oolboxv2\__init__.py:129:28: F401 `platform.system` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                            ^^^^^^ F401
130 |
131 | __cwd__ = cwd = Path.cwd()
    |
    = help: Remove unused import

~oolboxv2\__init__.py:149:5: F405 `mods` may be undefined, or defined from star imports
    |
147 |     "get_logger",
148 |     "flows_dict",
149 |     "mods",
    |     ^^^^^^ F405
150 |     "get_app",
151 |     "TBEF",
    |

~oolboxv2\__main__.py:30:12: F401 `hmr` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
29 | try:
30 |     import hmr
   |            ^^^ F401
31 |
32 |     HOT_RELOADER = True
   |
   = help: Remove unused import: `hmr`

~oolboxv2\__main__.py:104:5: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
102 |     def profile_execute_all_functions(*args):
103 |         return print(args)
104 |     raise ValueError("Failed to import function for profiling")
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
105 |
106 | try:
    |

~oolboxv2\__main__.py:868:17: S605 Starting a process with a shell, possible injection detected
    |
866 |             print(f"Exit app {app_pid}")
867 |             if system() == "Windows":
868 |                 os.system(f"taskkill /pid {app_pid} /F")
    |                 ^^^^^^^^^ S605
869 |             else:
870 |                 os.system(f"kill -9 {app_pid}")
    |

~oolboxv2\__main__.py:870:17: S605 Starting a process with a shell, possible injection detected
    |
868 |                 os.system(f"taskkill /pid {app_pid} /F")
869 |             else:
870 |                 os.system(f"kill -9 {app_pid}")
    |                 ^^^^^^^^^ S605
871 |
872 |     if args.command and not args.background_application:
    |

~oolboxv2\__main__.py:894:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
893 | def install_ipython():
894 |     os.system('pip install ipython prompt_toolkit')
    |     ^^^^^^^^^ S605
    |

~oolboxv2\__main__.py:1025:5: B018 Found useless expression. Either assign it to a variable or remove it.
     |
1024 | """)
1025 |     ()
     |     ^^ B018
1026 |     return c
     |

~oolboxv2\setup_helper.py:66:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
64 |       print("🔧 Installiere Dev-Tools...")
65 |       d = ["cargo", "node"]
66 | /     if a := input("With docker (N/y)"):
67 | |         if a.lower() == 'y':
   | |____________________________^ SIM102
68 |               d.append("docker")
69 |       for _d in d.copy():
   |
   = help: Combine `if` statements using `and`

~oolboxv2\setup_helper.py:152:9: S602 `subprocess` call with `shell=True` identified, security issue
    |
150 |         cwd = _cwd
151 |     try:
152 |         subprocess.run(command, cwd=cwd, shell=True, check=True,
    |         ^^^^^^^^^^^^^^ S602
153 |                        stdout=subprocess.PIPE if silent else None)
154 |         return True
    |

~oolboxv2\tests\test_utils\test_daemon\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(daemon_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await DaemonUtil(class_instance=None, host='0.0.0.0', port=6582, t=False,
20 |                              app=None, peer=False, name='daemonApp-server',
   |

~oolboxv2\tests\test_utils\test_proxy\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(proxy_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
20 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

~oolboxv2\tests\test_utils\test_proxy\test.py:23:14: B017 Do not assert blind exception: `Exception`
   |
21 |                                          unix_socket=False)
22 |
23 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
24 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
25 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:63:22: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
   |
61 |         # Check if the environment exists
62 |         try:
63 |             output = subprocess.check_output("conda env list", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
64 |             self.assertIn(self.test_env_name, output)
65 |         except subprocess.CalledProcessError:
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:81:13: S602 `subprocess` call with `shell=True` identified, security issue
   |
79 |         # Check that the environment no longer exists
80 |         with self.assertRaises(subprocess.CalledProcessError):
81 |             subprocess.check_output(f"conda env list | grep {self.test_env_name}", shell=True, text=True)
   |             ^^^^^^^^^^^^^^^^^^^^^^^ S602
82 |
83 |     def test_add_dependency(self):
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:96:22: S602 `subprocess` call with `shell=True` identified, security issue
   |
94 |         # Verify the dependency was added by checking conda list
95 |         try:
96 |             output = subprocess.check_output(f"conda list -n {self.test_env_name} numpy", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
97 |             self.assertIn("numpy", output)
98 |         except subprocess.CalledProcessError:
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:148:14: B017 Do not assert blind exception: `Exception`
    |
146 |         if not get_app(name="test").local_test:
147 |             return
148 |         with self.assertRaises(Exception):
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
149 |             res = add_dependency(self.test_env_name, "non_existent_package_xyz")
150 |             if res is False:
    |

~oolboxv2\tests\web_test\__init__.py:1:1: F403 `from .test_main_page import *` used; unable to detect undefined names
  |
1 | from .test_main_page import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 |
3 | in_valid_session_tests = []
  |

~oolboxv2\tests\web_test\__init__.py:4:24: F405 `contact_page_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                        ^^^^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

~oolboxv2\tests\web_test\__init__.py:4:51: F405 `installer_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                                                   ^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

~oolboxv2\tests\web_util.py:22:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
20 |     )
21 | except ImportError:
22 |     os.system("pip install playwright")
   |     ^^^^^^^^^ S605
23 |     from playwright.async_api import Browser as ABrowser
24 |     from playwright.async_api import BrowserContext as ABrowserContext
   |

~oolboxv2\utils\__init__.py:1:8: F401 `os` imported but unused
  |
1 | import os
  |        ^^ F401
2 |
3 | from yaml import safe_load
  |
  = help: Remove unused import: `os`

~oolboxv2\utils\__init__.py:3:18: F401 `yaml.safe_load` imported but unused
  |
1 | import os
2 |
3 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
4 |
5 | from .extras.show_and_hide_console import show_console
  |
  = help: Remove unused import: `yaml.safe_load`

~oolboxv2\utils\brodcast\server.py:32:5: S110 `try`-`except`-`pass` detected, consider logging the exception
   |
30 |           data = server.recv(1024)
31 |           print(f"data received! {data.decode()}", flush=True)
32 | /     except:
33 | |         pass
   | |____________^ S110
34 |       finally:
35 |           server.close()
   |

~oolboxv2\utils\daemon\daemon_util.py:10:1: F403 `from ..system.all_functions_enums import *` used; unable to detect undefined names
   |
 8 | from ..extras.show_and_hide_console import show_console
 9 | from ..extras.Style import Style
10 | from ..system.all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
11 | from ..system.getting_and_closing_app import get_app
12 | from ..system.tb_logger import get_logger
   |

~oolboxv2\utils\daemon\daemon_util.py:85:45: F405 `SOCKETMANAGER` may be undefined, or defined from star imports
   |
83 |         if not app.mod_online("SocketManager"):
84 |             await app.load_mod("SocketManager")
85 |         server_result = await app.a_run_any(SOCKETMANAGER.CREATE_SOCKET,
   |                                             ^^^^^^^^^^^^^ F405
86 |                                             get_results=True,
87 |                                             name=self._name,
   |

~oolboxv2\utils\daemon\daemon_util.py:216:71: B023 Function definition does not bind loop variable `name`
    |
214 |                         async def _helper_runner():
215 |                             try:
216 |                                 attr_f = getattr(self.class_instance, name)
    |                                                                       ^^^^ B023
217 |
218 |                                 if asyncio.iscoroutinefunction(attr_f):
    |

~oolboxv2\utils\daemon\daemon_util.py:219:57: B023 Function definition does not bind loop variable `args`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                               ^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

~oolboxv2\utils\daemon\daemon_util.py:219:65: B023 Function definition does not bind loop variable `kwargs`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                                       ^^^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

~oolboxv2\utils\daemon\daemon_util.py:221:51: B023 Function definition does not bind loop variable `args`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                   ^^^^ B023
222 |
223 |                                 if res is None:
    |

~oolboxv2\utils\daemon\daemon_util.py:221:59: B023 Function definition does not bind loop variable `kwargs`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                           ^^^^^^ B023
222 |
223 |                                 if res is None:
    |

~oolboxv2\utils\daemon\daemon_util.py:237:51: B023 Function definition does not bind loop variable `identifier`
    |
235 |                                 get_logger().info(f"sending response {res} {type(res)}")
236 |
237 |                                 await sender(res, identifier)
    |                                                   ^^^^^^^^^^ B023
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |

~oolboxv2\utils\daemon\daemon_util.py:239:64: B023 Function definition does not bind loop variable `identifier`
    |
237 |                                 await sender(res, identifier)
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |                                                                ^^^^^^^^^^ B023
240 |
241 |                         await _helper_runner()
    |

~oolboxv2\utils\extras\Style.py:45:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
43 | def cls():
44 |     if system() == "Windows":
45 |         os.system("cls")
   |         ^^^^^^^^^ S605
46 |     if system() == "Linux":
47 |         os.system("clear")
   |

~oolboxv2\utils\extras\Style.py:47:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
45 |         os.system("cls")
46 |     if system() == "Linux":
47 |         os.system("clear")
   |         ^^^^^^^^^ S605
   |

~oolboxv2\utils\extras\Style.py:369:28: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
367 |             if i < len(words) - 1:
368 |                 print(" ", end="", flush=True)
369 |             typing_speed = uniform(min_typing_speed, max_typing_speed)
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
370 |             time.sleep(typing_speed)
371 |             # type faster after each word
    |

~oolboxv2\utils\extras\base_widget.py:74:24: B023 Function definition does not bind loop variable `fuction`
   |
72 |         for fuction in functions:
73 |             def x(r):
74 |                 return fuction(request=r)
   |                        ^^^^^^^ B023
75 |             self.onReload.append(x)
   |

~oolboxv2\utils\extras\base_widget.py:142:66: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
140 |         return asset
141 |
142 |     def generate_html(self, app, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                  ^^^^^^^^^^^^ B008
143 |         return app.run_any(MINIMALHTML.GENERATE_HTML,
144 |                            group_name=self.name,
    |

~oolboxv2\utils\extras\base_widget.py:147:73: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
145 |                            collection_name=f"{name}-{asset_id}")
146 |
147 |     def load_widget(self, app, request, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                         ^^^^^^^^^^^^ B008
148 |         app.run_any(MINIMALHTML.ADD_GROUP, command=self.name)
149 |         self.reload(request)
    |

~oolboxv2\utils\extras\blobs.py:55:13: SIM113 Use `enumerate()` for index variable `current_blob_id` in `for` loop
   |
53 |                 if index_ + 1 > len(blob_ids) and len(all_link[i + splitter:]) > 1:
54 |                     self.add_link(blob_ids[current_blob_id], blob_ids[current_blob_id], link_port)
55 |             current_blob_id += 1
   |             ^^^^^^^^^^^^^^^^^^^^ SIM113
56 |
57 |     def recover_blob(self, blob_ids, check_blobs_ids):
   |

~oolboxv2\utils\extras\blobs.py:158:20: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
156 |             return self.create_blob(pickle.dumps({}), blob_id)
157 |         with open(blob_file, 'rb') as f:
158 |             return pickle.load(f)
    |                    ^^^^^^^^^^^^^^ S301
159 |
160 |     def _generate_recovery_bytes(self, blob_id):
    |

~oolboxv2\utils\extras\blobs.py:180:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
178 |           self.storage = storage
179 |           self.data = b""
180 | /         if key is not None:
181 | |             if Code.decrypt_symmetric(Code.encrypt_symmetric("test", key), key) != "test":
    | |__________________________________________________________________________________________^ SIM102
182 |                   raise ValueError("Invalid Key")
183 |           self.key = key
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\extras\blobs.py:202:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
200 |     def __enter__(self):
201 |         if 'r' in self.mode:
202 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
203 |             if self.folder in blob_data:
204 |                 blob_folder = blob_data[self.folder]
    |

~oolboxv2\utils\extras\blobs.py:220:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
218 |             if self.key is not None:
219 |                 data = Code.encrypt_symmetric(data, self.key)
220 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
221 |             if self.folder not in blob_data:
222 |                 blob_data[self.folder] = {self.datei: data}
    |

~oolboxv2\utils\extras\blobs.py:268:16: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
266 |         if self.data == b"":
267 |             return {}
268 |         return pickle.loads(self.data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^ S301
269 |
270 |     def write_pickle(self, data):
    |

~oolboxv2\utils\extras\bottleup.py:18:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
16 |     except ImportError:
17 |         print("Bottle is not available auto installation")
18 |         os.system("pip install bottle")
   |         ^^^^^^^^^ S605
19 |         return bottle_up(tb_app, user=user, main_route=main_route, **kwargs)
   |

~oolboxv2\utils\extras\bottleup.py:125:112: B023 Function definition does not bind loop variable `tb_func`
    |
123 |                         if request_as_kwarg:
124 |                             def tb_func_(**kw):
125 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read()+tb_func(**kw)
    |                                                                                                                ^^^^^^^ B023
126 |                         else:
127 |                             def tb_func_():
    |

~oolboxv2\utils\extras\bottleup.py:128:114: B023 Function definition does not bind loop variable `tb_func`
    |
126 |                         else:
127 |                             def tb_func_():
128 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read() + tb_func()
    |                                                                                                                  ^^^^^^^ B023
129 |                         self.route(f'/{mod_name}', method='GET')(tb_func_)
130 |                         print("adding root:", f'/{mod_name}')
    |

~oolboxv2\utils\extras\gist_control.py:20:9: S102 Use of `exec` detected
   |
18 |         # Erstelle ein neues Modul
19 |         module = importlib.util.module_from_spec(self.get_spec(module_name))
20 |         exec(self.module_code, module.__dict__)
   |         ^^^^ S102
21 |         return module
   |

~oolboxv2\utils\extras\gist_control.py:35:20: S113 Probable use of `requests` call without timeout
   |
33 |         api_url = f"https://api.github.com/gists/{gist_id}"
34 |
35 |         response = requests.get(api_url)
   |                    ^^^^^^^^^^^^ S113
36 |
37 |         if response.status_code == 200:
   |

~oolboxv2\utils\extras\gist_control.py:77:20: S113 Probable use of `requests` call without timeout
   |
75 |         # Update an existing Gist
76 |         url = f"https://api.github.com/gists/{gist_id}"
77 |         response = requests.patch(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^^ S113
78 |     else:
79 |         # Create a new Gist
   |

~oolboxv2\utils\extras\gist_control.py:81:20: S113 Probable use of `requests` call without timeout
   |
79 |         # Create a new Gist
80 |         url = "https://api.github.com/gists"
81 |         response = requests.post(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^ S113
82 |
83 |     # Check if the request was successful
   |

~oolboxv2\utils\extras\helper_test_functions.py:52:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
50 |     """
51 |     if param_type in [int, float]:
52 |         return random.randint(0, 100)  # Zufällige normale Zahlen
   |                ^^^^^^^^^^^^^^^^^^^^^^ S311
53 |     elif param_type == str:
54 |         return "test" # Zufälliges Wort
   |

~oolboxv2\utils\proxy\prox_util.py:149:21: SIM102 Use a single `if` statement instead of nested `if` statements
    |
147 |                               return await app_attr(*args, **kwargs)
148 |                           return app_attr(*args, **kwargs)
149 | /                     if (name == 'run_any' or name == 'a_run_any') and kwargs.get('get_results', False):
150 | |                         if isinstance(args[0], Enum):
    | |_____________________________________________________^ SIM102
151 |                               args = (args[0].__class__.NAME.value, args[0].value), args[1:]
152 |                       self.app.sprint(f"Calling method {name}, {args=}, {kwargs}=")
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\security\cryp.py:76:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
74 |             int: Eine zufällige Zahl.
75 |         """
76 |         return random.randint(2 ** 32 - 1, 2 ** 64 - 1)
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
77 |
78 |     @staticmethod
   |

~oolboxv2\utils\security\cryp.py:334:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
332 |               )
333 |               return True
334 | /         except:
335 | |             pass
    | |________________^ S110
336 |           return False
    |

~oolboxv2\utils\security\cryp.py:361:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
359 |               )
360 |               return True
361 | /         except:
362 | |             pass
    | |________________^ S110
363 |           return False
    |

~oolboxv2\utils\system\__init__.py:1:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
  |
1 | from .all_functions_enums import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 | from .cache import FileCache, MemoryCache
3 | from .file_handler import FileHandler
  |

~oolboxv2\utils\system\api.py:151:20: S113 Probable use of `requests` call without timeout
    |
149 |     print(f"Attempting to download executable from {url}...")
150 |     try:
151 |         response = requests.get(url, stream=True)
    |                    ^^^^^^^^^^^^ S113
152 |     except Exception as e:
153 |         print(f"Download error: {e}")
    |

~oolboxv2\utils\system\api.py:163:33: S103 `os.chmod` setting a permissive mask `0o755` on file or directory
    |
161 |         # Make the file executable on non-Windows systems
162 |         if platform.system().lower() != "windows":
163 |             os.chmod(file_name, 0o755)
    |                                 ^^^^^ S103
164 |         return file_name
165 |     else:
    |

~oolboxv2\utils\system\api.py:350:20: S108 Probable insecure usage of temporary file or directory: "/tmp/dill_package"
    |
348 |     """Package dill and all dependencies into a single .dill archive."""
349 |     try:
350 |         temp_dir = "/tmp/dill_package"
    |                    ^^^^^^^^^^^^^^^^^^^ S108
351 |         os.makedirs(temp_dir, exist_ok=True)
    |

~oolboxv2\utils\system\api.py:761:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
759 |                     with open(PERSISTENT_FD_FILE) as f_fd: fd_val = f_fd.read().strip()
760 |                     print(f"  Listening FD (from file, POSIX only): {fd_val}")
761 |                 except Exception: pass
    |                 ^^^^^^^^^^^^^^^^^^^^^^ S110
762 |         else:
763 |             print("Server is STOPPED (or state inconsistent).")
    |

~oolboxv2\utils\system\cache.py:16:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
14 |     def get(self, key):
15 |         try:
16 |             with shelve.open(self.filename) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
17 |                 return db.get(key.replace('\x00', ''))
18 |         except Exception:
   |

~oolboxv2\utils\system\cache.py:23:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
21 |     def set(self, key, value):
22 |         try:
23 |             with shelve.open(self.filename, writeback=True) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
24 |                 db[key.replace('\x00', '')] = value
25 |         except Exception:
   |

~oolboxv2\utils\system\conda_runner.py:15:19: S602 `subprocess` call with `shell=True` identified, security issue
   |
13 |     if live:
14 |         # Using subprocess.Popen to stream stdout and stderr live
15 |         process = subprocess.Popen(command, shell=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
   |                   ^^^^^^^^^^^^^^^^ S602
16 |         process.communicate()  # Wait for the process to complete
17 |         return process.returncode == 0, None
   |

~oolboxv2\utils\system\conda_runner.py:21:18: S602 `subprocess` call with `shell=True` identified, security issue
   |
19 |     try:
20 |         # If not live, capture output and return it
21 |         result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True, encoding='cp850')
   |                  ^^^^^^^^^^^^^^ S602
22 |         return True, result.stdout
23 |     except subprocess.CalledProcessError as e:
   |

~oolboxv2\utils\system\file_handler.py:37:41: SIM115 Use a context manager for opening files
   |
35 |             self.file_handler_storage = None
36 |         try:
37 |             self.file_handler_storage = open(self.file_handler_file_prefix + self.file_handler_filename, mode)
   |                                         ^^^^ SIM115
38 |             self.file_handler_max_loaded_index_ += 1
39 |         except FileNotFoundError:
   |

~oolboxv2\utils\system\file_handler.py:60:16: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
58 |                 self.file_handler_max_loaded_index_ = -1
59 |             rdu()
60 |         except OSError and PermissionError as e:
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
61 |             raise e
   |

~oolboxv2\utils\system\file_handler.py:111:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
109 |               )
110 |               return False
111 | /         if key not in self.file_handler_load:
112 | |             if key in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
113 |                   key = self.file_handler_key_mapper[key]
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\system\file_handler.py:148:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
146 |                 self.file_handler_load[key] = self.decode_code(line)
147 |
148 |         except json.decoder.JSONDecodeError and Exception:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
149 |
150 |             for line in self.file_handler_storage:
    |

~oolboxv2\utils\system\file_handler.py:166:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
164 |       def get_file_handler(self, obj: str, default=None) -> str or None:
165 |           logger = get_logger()
166 | /         if obj not in self.file_handler_load:
167 | |             if obj in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
168 |                   obj = self.file_handler_key_mapper[obj]
169 |           logger.info(Style.ITALIC(Style.GREY(f"Collecting data from storage key : {obj}")))
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\system\getting_and_closing_app.py:15:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
13 |   def override_main_app(app):
14 |       global registered_apps
15 | /     if registered_apps[0] is not None:
16 | |         if time.time() - registered_apps[0].called_exit[1] > 30:
   | |________________________________________________________________^ SIM102
17 |               raise PermissionError("Permission denied because of overtime fuction override_main_app sud only be called "
18 |                                     f"once and ontime overtime {time.time() - registered_apps[0].called_exit[1]}")
   |
   = help: Combine `if` statements using `and`

~oolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^^^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

~oolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

~oolboxv2\utils\system\ipy_completer.py:64:9: SIM102 Use a single `if` statement instead of nested `if` statements
   |
62 |       for _name, obj in inspect.getmembers(module, inspect.isclass):
63 |           # Check if the class is defined in the current module
64 | /         if obj.__module__ == module.__name__:
65 | |             # Check if the class is a dataclass
66 | |             if is_dataclass(obj):
   | |_________________________________^ SIM102
67 |                   dataclasses.append(obj)
   |
   = help: Combine `if` statements using `and`

~oolboxv2\utils\system\main_tool.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import inspect
 3 | | import os
 4 | | from toolboxv2.utils.extras import Style
 5 | |
 6 | | from .getting_and_closing_app import get_app
 7 | | from .tb_logger import get_logger
 8 | | from .types import Result, ToolBoxError, ToolBoxInfo, ToolBoxInterfaces, ToolBoxResult
   | |______________________________________________________________________________________^ I001
 9 |
10 |   try:
   |
   = help: Organize imports

~oolboxv2\utils\system\session.py:265:24: S113 Probable use of `requests` call without timeout
    |
263 |             except Exception as e:
264 |                 print("Error session fetch:", e, self.username)
265 |                 return requests.request(method, url, data=data)
    |                        ^^^^^^^^^^^^^^^^ S113
266 |         else:
267 |             print(f"Could not find session using request on {url}")
    |

~oolboxv2\utils\system\session.py:269:24: S113 Probable use of `requests` call without timeout
    |
267 |             print(f"Could not find session using request on {url}")
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
    |                        ^^^^^^^^^^^^^^^^ S113
270 |             return requests.request(method, url, data=data)
271 |             # raise Exception("Session not initialized. Please login first.")
    |

~oolboxv2\utils\system\session.py:270:20: S113 Probable use of `requests` call without timeout
    |
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
270 |             return requests.request(method, url, data=data)
    |                    ^^^^^^^^^^^^^^^^ S113
271 |             # raise Exception("Session not initialized. Please login first.")
    |

~oolboxv2\utils\system\session.py:351:20: S113 Probable use of `requests` call without timeout
    |
349 | def get_public_ip():
350 |     try:
351 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^ S113
352 |         ip_address = response.json()['ip']
353 |         return ip_address
    |

~oolboxv2\utils\system\tb_logger.py:56:29: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
54 |         log_info_data_str = li.read()
55 |         try:
56 |             log_info_data = eval(log_info_data_str)
   |                             ^^^^^^^^^^^^^^^^^^^^^^^ S307
57 |         except SyntaxError:
58 |             if log_info_data_str:
   |

~oolboxv2\utils\system\types.py:20:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
   |
18 | from ..extras import generate_test_cases
19 | from ..extras.Style import Spinner
20 | from .all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
21 | from .file_handler import FileHandler
   |

~oolboxv2\utils\system\types.py:482:25: F405 `Enum` may be undefined, or defined from star imports
    |
482 | class ToolBoxError(str, Enum):
    |                         ^^^^ F405
483 |     none = "none"
484 |     input_error = "InputError"
    |

~oolboxv2\utils\system\types.py:489:30: F405 `Enum` may be undefined, or defined from star imports
    |
489 | class ToolBoxInterfaces(str, Enum):
    |                              ^^^^ F405
490 |     cli = "CLI"
491 |     api = "API"
    |

~oolboxv2\utils\system\types.py:532:62: F405 `Enum` may be undefined, or defined from star imports
    |
530 |     def as_result(self):
531 |         return Result(
532 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
533 |             result=ToolBoxResult(
534 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

~oolboxv2\utils\system\types.py:534:86: F405 `Enum` may be undefined, or defined from star imports
    |
532 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
533 |             result=ToolBoxResult(
534 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
535 |                 data_info=self.result.data_info,
536 |                 data=self.result.data,
    |

~oolboxv2\utils\system\types.py:574:64: F405 `Enum` may be undefined, or defined from star imports
    |
572 |     def as_dict(self):
573 |         return {
574 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                                ^^^^ F405
575 |         "result" : {
576 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

~oolboxv2\utils\system\types.py:576:84: F405 `Enum` may be undefined, or defined from star imports
    |
574 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
575 |         "result" : {
576 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                    ^^^^ F405
577 |             "data_info":self.result.data_info,
578 |             "data":self.result.data,
    |

~oolboxv2\utils\system\types.py:611:62: F405 `Enum` may be undefined, or defined from star imports
    |
609 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
610 |         return ApiResult(
611 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
612 |             result=ToolBoxResultBM(
613 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

~oolboxv2\utils\system\types.py:613:86: F405 `Enum` may be undefined, or defined from star imports
    |
611 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
612 |             result=ToolBoxResultBM(
613 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
614 |                 data_info=self.result.data_info,
615 |                 data=self.result.data,
    |

~oolboxv2\utils\system\types.py:633:46: F405 `Enum` may be undefined, or defined from star imports
    |
631 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
632 |         return ApiResult(
633 |             error=error if isinstance(error, Enum) else error,
    |                                              ^^^^ F405
634 |             result=ToolBoxResultBM(
635 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |

~oolboxv2\utils\system\types.py:635:84: F405 `Enum` may be undefined, or defined from star imports
    |
633 |             error=error if isinstance(error, Enum) else error,
634 |             result=ToolBoxResultBM(
635 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |                                                                                    ^^^^ F405
636 |                 data_info=result.get('data_info', '404'),
637 |                 data=result.get('data'),
    |

~oolboxv2\utils\system\types.py:884:45: F405 `Enum` may be undefined, or defined from star imports
    |
882 |     async def aget(self, key=None, default=None):
883 |         if asyncio.isfuture(self.result.data) or asyncio.iscoroutine(self.result.data) or (
884 |             isinstance(self.result.data_to, Enum) and self.result.data_to.name == ToolBoxInterfaces.future.name):
    |                                             ^^^^ F405
885 |             data = await self.result.data
886 |         else:
    |

~oolboxv2\utils\system\types.py:1034:35: F405 `CLOUDM_AUTHMANAGER` may be undefined, or defined from star imports
     |
1033 |     async def get_user(self, username: str) -> Result:
1034 |         return self.app.a_run_any(CLOUDM_AUTHMANAGER.GET_USER_BY_NAME, username=username, get_results=True)
     |                                   ^^^^^^^^^^^^^^^^^^ F405
     |

~oolboxv2\utils\system\types.py:1192:29: F405 `Enum` may be undefined, or defined from star imports
     |
1191 |     def _get_function(self,
1192 |                       name: Enum or None,
     |                             ^^^^ F405
1193 |                       state: bool = True,
1194 |                       specification: str = "app",
     |

~oolboxv2\utils\system\types.py:1251:34: F405 `Enum` may be undefined, or defined from star imports
     |
1249 |         """proxi attr"""
1250 |
1251 |     def get_function(self, name: Enum or tuple, **kwargs):
     |                                  ^^^^ F405
1252 |         """
1253 |         Kwargs for _get_function
     |

~oolboxv2\utils\system\types.py:1266:47: F405 `Enum` may be undefined, or defined from star imports
     |
1264 |         """
1265 |
1266 |     def run_function(self, mod_function_name: Enum or tuple,
     |                                               ^^^^ F405
1267 |                      tb_run_function_with_state=True,
1268 |                      tb_run_with_specification='app',
     |

~oolboxv2\utils\system\types.py:1276:55: F405 `Enum` may be undefined, or defined from star imports
     |
1274 |         """proxi attr"""
1275 |
1276 |     async def a_run_function(self, mod_function_name: Enum or tuple,
     |                                                       ^^^^ F405
1277 |                              tb_run_function_with_state=True,
1278 |                              tb_run_with_specification='app',
     |

~oolboxv2\utils\system\types.py:1306:49: F405 `Enum` may be undefined, or defined from star imports
     |
1304 |         """
1305 |
1306 |     async def run_http(self, mod_function_name: Enum or str or tuple, function_name=None, method="GET",
     |                                                 ^^^^ F405
1307 |                        args_=None,
1308 |                        kwargs_=None,
     |

~oolboxv2\utils\system\types.py:1312:42: F405 `Enum` may be undefined, or defined from star imports
     |
1310 |         """run a function remote via http / https"""
1311 |
1312 |     def run_any(self, mod_function_name: Enum or str or tuple, backwords_compability_variabel_string_holder=None,
     |                                          ^^^^ F405
1313 |                 get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
1314 |                 kwargs_=None,
     |

~oolboxv2\utils\system\types.py:1318:50: F405 `Enum` may be undefined, or defined from star imports
     |
1316 |         """proxi attr"""
1317 |
1318 |     async def a_run_any(self, mod_function_name: Enum or str or tuple,
     |                                                  ^^^^ F405
1319 |                         backwords_compability_variabel_string_holder=None,
1320 |                         get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
     |

~oolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^^^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

~oolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

~oolboxv2\utils\toolbox.py:329:9: S605 Starting a process with a shell, possible injection detected
    |
327 |             return
328 |         self.print(f"Installing {module_name} GREEDY")
329 |         os.system(f"{sys.executable} -m pip install {module_name}")
    |         ^^^^^^^^^ S605
330 |
331 |     def python_module_import_classifier(self, mod_name, error_message):
    |

~oolboxv2\utils\toolbox.py:689:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
687 |                           # Allow tasks time to clean up
688 |                           loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
689 | /                 except Exception:
690 | |                     pass
    | |________________________^ S110
691 |
692 |                   loop.close()
    |

~oolboxv2\utils\toolbox.py:879:60: B023 Function definition does not bind loop variable `result`
    |
877 | …                     async def _():
878 | …                         try:
879 | …                             if asyncio.iscoroutine(result):
    |                                                      ^^^^^^ B023
880 | …                                 await result
881 | …                             if hasattr(result, 'Name'):
    |

~oolboxv2\utils\toolbox.py:880:47: B023 Function definition does not bind loop variable `result`
    |
878 | …                     try:
879 | …                         if asyncio.iscoroutine(result):
880 | …                             await result
    |                                     ^^^^^^ B023
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
    |

~oolboxv2\utils\toolbox.py:881:48: B023 Function definition does not bind loop variable `result`
    |
879 | …                     if asyncio.iscoroutine(result):
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
    |                                  ^^^^^^ B023
882 | …                         print('Opened :', result.Name)
883 | …                     elif hasattr(result, 'name'):
    |

~oolboxv2\utils\toolbox.py:882:59: B023 Function definition does not bind loop variable `result`
    |
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
882 | …                         print('Opened :', result.Name)
    |                                             ^^^^^^ B023
883 | …                     elif hasattr(result, 'name'):
884 | …                         print('Opened :', result.name)
    |

~oolboxv2\utils\toolbox.py:883:50: B023 Function definition does not bind loop variable `result`
    |
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
    |                                        ^^^^^^ B023
884 | …                             print('Opened :', result.name)
885 | …                     except Exception as e:
    |

~oolboxv2\utils\toolbox.py:884:59: B023 Function definition does not bind loop variable `result`
    |
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
884 | …                             print('Opened :', result.name)
    |                                                 ^^^^^^ B023
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
    |

~oolboxv2\utils\toolbox.py:887:48: B023 Function definition does not bind loop variable `result`
    |
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
887 | …                         if hasattr(result, 'Name'):
    |                                      ^^^^^^ B023
888 | …                             print('Error opening :', result.Name)
889 | …                         elif hasattr(result, 'name'):
    |

~oolboxv2\utils\toolbox.py:888:66: B023 Function definition does not bind loop variable `result`
    |
886 | …                     self.debug_rains(e)
887 | …                     if hasattr(result, 'Name'):
888 | …                         print('Error opening :', result.Name)
    |                                                    ^^^^^^ B023
889 | …                     elif hasattr(result, 'name'):
890 | …                         print('Error opening :', result.name)
    |

~oolboxv2\utils\toolbox.py:889:50: B023 Function definition does not bind loop variable `result`
    |
887 | …                             if hasattr(result, 'Name'):
888 | …                                 print('Error opening :', result.Name)
889 | …                             elif hasattr(result, 'name'):
    |                                            ^^^^^^ B023
890 | …                                 print('Error opening :', result.name)
891 | …                     asyncio.create_task(_())
    |

~oolboxv2\utils\toolbox.py:890:66: B023 Function definition does not bind loop variable `result`
    |
888 |                                         print('Error opening :', result.Name)
889 |                                     elif hasattr(result, 'name'):
890 |                                         print('Error opening :', result.name)
    |                                                                  ^^^^^^ B023
891 |                             asyncio.create_task(_())
892 |                         else:
    |

~oolboxv2\utils\toolbox.py:1575:33: SIM115 Use a context manager for opening files
     |
1573 |     def web_context(self):
1574 |         if self._web_context is None:
1575 |             self._web_context = open("./dist/helper.html", encoding="utf-8").read()
     |                                 ^^^^ SIM115
1576 |         return self._web_context
     |

~oolboxv2\utils\toolbox.py:1639:21: S102 Use of `exec` detected
     |
1637 |                     code = compile(source, module.__file__, 'exec')
1638 |                     # Execute the code in the module's namespace
1639 |                     exec(code, module.__dict__)
     |                     ^^^^ S102
1640 |                 except Exception:
1641 |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
     |

~oolboxv2\utils\toolbox.py:1640:17: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
1638 |                       # Execute the code in the module's namespace
1639 |                       exec(code, module.__dict__)
1640 | /                 except Exception:
1641 | |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
1642 | |                     pass
     | |________________________^ S110
1643 |                   return module
     |

Found 721 errors.
[*] 147 fixable with the `--fix` option (11 hidden fixes can be enabled with the `--unsafe-fixes` option).

[Safety] Exit Code: 64
[Safety] Output:
+==============================================================================+

                               /$$$$$$            /$$
                              /$$__  $$          | $$
           /$$$$$$$  /$$$$$$ | $$  \__//$$$$$$  /$$$$$$   /$$   /$$
          /$$_____/ |____  $$| $$$$   /$$__  $$|_  $$_/  | $$  | $$
         |  $$$$$$   /$$$$$$$| $$_/  | $$$$$$$$  | $$    | $$  | $$
          \____  $$ /$$__  $$| $$    | $$_____/  | $$ /$$| $$  | $$
          /$$$$$$$/|  $$$$$$$| $$    |  $$$$$$$  |  $$$$/|  $$$$$$$
         |_______/  \_______/|__/     \_______/   \___/   \____  $$
                                                          /$$  | $$
                                                         |  $$$$$$/
  by safetycli.com                                        \______/

+==============================================================================+

 [1mREPORT[0m 

  Safety [1mv3.2.4[0m is scanning for [1mVulnerabilities[0m[1m...[0m
[1m  Scanning dependencies[0m in your [1menvironment:[0m

  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\Pythonwin
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Scripts\safety.exe
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\win32
  -> C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-
  none\Lib
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\win32\lib
  -> C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-
  none\DLLs
  -> C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-
  none\python312.zip
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-
  packages\setuptools\_vendor
  -> c:\users\<USER>\workspace\toolboxv2\.venv\lib\site-packages
  ->
  C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-none
  -> C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages
  -> c:\users\<USER>\workspace\toolboxv2\.venv\lib\site-
  packages\setuptools\_vendor

  Using [1mopen-source vulnerability database[0m
[1m  Found and scanned 349 packages[0m
  Timestamp [1m2025-05-26 00:22:17[0m
[1m  1[0m[1m vulnerability reported[0m
[1m  0[0m[1m vulnerabilities ignored[0m

+==============================================================================+
 [1mVULNERABILITIES REPORTED[0m 
+==============================================================================+

[31m-> Vulnerability found in browser-use version 0.2.2[0m
[1m   Vulnerability ID: [0m76718
[1m   Affected spec: [0m<1.7
[1m   ADVISORY: [0mAffected versions of the server were vulnerable to a
   security misconfiguration where the debug port was exposed on all network
   interfaces.
[1m   PVE-2025-76718[0m
[1m   For more information about this vulnerability, visit
   [0mhttps://data.safetycli.com/v/76718/97c[0m
   To ignore this vulnerability, use PyUp vulnerability id 76718 in safety�s
   ignore command-line argument or add the ignore to your safety policy file.


+==============================================================================+
   [32m[1mREMEDIATIONS[0m

  1 vulnerability was reported in 1 package. For detailed remediation & fix 
  recommendations, upgrade to a commercial license. 

+==============================================================================+

 Scan was completed. 1 vulnerability was reported. 

+==============================================================================+[0m

[Versions] Exit Code: 0
[Versions] Output:
C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\pydantic\_internal\_generate_schema.py:502: UserWarning: <built-in function allocate_lock> is not a Python type (it may be an instance of an object), Pydantic will allow any object with no validation since we cannot even enforce that the input is an instance of the given type. To get rid of this error wrap the type with `pydantic.SkipValidation`.
  warn(
Starting ToolBox as main from : [1m[36mC:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2[0m[0m
Logger in Default
================================
[36mSystem$main-DESKTOP-CI57V1L:[0m Infos:
  Name     -> DESKTOP-CI57V1L
  ID       -> main-DESKTOP-CI57V1L
  Version  -> 0.1.21

LOADING ALL MODS FROM FOLDER : mods
Opened : CodeVerification
Opened : CodeVerification

**************************************************************************
Opened : ProcessManager
Opened : welcome
Opened : WhatsAppTb
Opened : TestWidget
Opened : talk
Opened : <module 'toolboxv2.mods.TruthSeeker' from 'C:\\Users\\<USER>\\Workspace\\ToolBoxV2\\toolboxv2\\mods\\TruthSeeker\\__init__.py'>
Opened : SocketManager
Pers�nlicher Organisationsassistent Modul (POA v0.1.0) initialisiert.
Opened : FileWidget
Opened : EventManager
SchedulerManager try loading from file
SchedulerManager Successfully loaded
STARTING SchedulerManager
Opened : WebSocketManager
Overriding function delete from DB
Overriding function Version from DB
Opened : DB
Opened : SchedulerManager
Opened : FastApi
Overriding function get_system_status from CloudM.AdminDashboard
Overriding function list_users_admin from CloudM.AdminDashboard
Opened : WidgetsProvider.BoardWidget
Opened : cli_functions
Overriding function show_version from CloudM
Admin Panel (CloudM v0.0.4) initialisiert.
ADDING UserDashboard
Overriding function Version from CloudM
Overriding function Version from CloudM
Function CloudM On start result: Function Exec code: 0
Info's: Admin Panel Online <|> 
Origin: CloudM.initialize_admin_panel
Overriding function show_version from CloudM
Overriding function show_version from CloudM
Opened : CloudM
Overriding function get_mod_snapshot from CloudM
Admin Panel (CloudM v0.0.4) initialisiert.
Overriding function get_mod_snapshot from CloudM
Admin Panel (CloudM v0.0.4) initialisiert.
ADDING UserDashboard
ADDING UserDashboard
Function CloudM On start result: Function Exec code: 0
Info's: Admin Panel Online <|> 
Origin: CloudM.initialize_admin_panel
Function CloudM On start result: Function Exec code: 0
Info's: Admin Panel Online <|> 
Origin: CloudM.initialize_admin_panel
ADDING POA
ADDING DoNext
Opened : DoNext
Function POA On start result: Function Exec code: 0
Info's: POA Modul bereit. <|> 
NO Origin
Opened : POA
INFO: Google ADK components found. ADK features enabled. version 0.5.0
INFO: python-a2a components found. A2A features enabled. version 0.5.5
INFO: MCP components found. MCP features enabled (primarily for server building).
INFO: LiteLLM version 1.69.1 found.
INFO: OpenTelemetry SDK found. Basic tracing enabled (requires configuration).
[95misaa[0m: Start app.isaa
Spinner Manager not in the min Thread no signal possible

[KISAA Chains Modul (isaa.chainUi v0.1.0) initialisiert.
ADDING isaa.chainUi_TaskChainEditor
ISAA WebUI Modul (isaa.ui v0.1.0) initialisiert.
[95misaa[0m: ISAA module started. fallback
Opened : isaa

[KOpened 31 modules in 12.88s

------------------ Version ------------------

[1m[36m[3mRE[0m[0m[0m[3mSimple[0mToolBox:  0.1.21  

              DoNext               :  0.1.21  
         CodeVerification          :  0.0.1   
              welcome              :  0.1.21  
            MinimalHtml            :  0.0.2   
          ProcessManager           :  0.0.1   
            TestWidget             :  0.0.1   
               talk                :  0.0.1   
            WhatsAppTb             : unknown  
            TruthSeeker            : unknown  
           SocketManager           :  0.1.9   
                POA                :  0.1.0   
            FileWidget             :  1.0.0   
           EventManager            :  0.0.3   
         SchedulerManager          :  0.0.2   
         WebSocketManager          :  0.0.3   
                DB                 :  0.0.3   
              FastApi              :  0.2.2   
       CloudM.email_services       :  0.1.0   
        CloudM.AuthManager         :  0.1.21  
              CloudM               :  0.0.4   
       CloudM.UserInstances        :  0.0.2   
         CloudM.UI.widget          :  0.0.1   
     CloudM.UserAccountManager     :  0.0.1   
       CloudM.UserDashboard        :  0.1.1   
       CloudM.AdminDashboard       :  0.1.1   
          WidgetsProvider          :  0.0.1   
    WidgetsProvider.BoardWidget    :  0.0.1   
           cli_functions           :  0.0.1   
           isaa.chainUi            :  0.1.0   
              isaa.ui              :  0.1.0   
               isaa                :  0.2.0   



[K
Building State data:   0%|          | 0/6 [00:00<?, ?chunk/s]
                                                             

Building State data:   0%|          | 0/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  17%|#6        | 1/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  33%|###3      | 2/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  50%|#####     | 3/6 [00:00<00:00, 3000.93chunk/s]working on utils files
working on api files
working on app files
working on mods files
ADDING DoNext
Overriding function get_widget from FileWidget
Overriding function upload from FileWidget
Overriding function download from FileWidget
Overriding function files from FileWidget
Overriding function Version from MinimalHtml
Overriding function add_group from MinimalHtml
Function On Exit result: saved 0 jobs in C:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2\.data\main-DESKTOP-CI57V1L/jobs.compact
Overriding function add_collection_to_group from MinimalHtml
[95misaa[0m: Memory saving process initiated
[1m[3m- end -[0m[0m

