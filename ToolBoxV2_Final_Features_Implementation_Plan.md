# ToolBoxV2 - Final Features Implementation Plan

This document outlines the steps to complete the remaining core features of ToolBoxV2.

---

## 1. Mod Manager Refactoring (High Priority)

**Status:** ✅ **Completed**

**Goal:** Modernize the Mod Manager's backend and frontend, replacing outdated methods with secure, integrated ToolBoxV2 APIs and a modern HTMX/tbjs user interface.

**Summary of Completed Work:**
- [x] **Backend Refactoring:** Replaced old `/installer/` routes with new, secure API endpoints (`/api/CloudM/upload_mod`, `/api/CloudM/download_mod`, `/api/CloudM/getModVersion`).
- [x] **Frontend Migration:** Replaced the NiceGUI interface with a new `mod_manager.html` file utilizing HTMX and `tbjs` for a more integrated user experience.
- [x] **API Integration:** Updated all client-side calls in `ModManager.py` to use the new, secure API endpoints.

---

## 2. Helper Overhaul: CLI Account Management (High Priority)

**Status:** ✅ **Completed**

**Goal:** Expose the powerful, but currently hidden, passwordless account management functions from `CloudM.AuthManager` through a suite of clear and transparent CLI commands.

**Analysis:** The system uses a robust, key-based authentication system, but lacks user-friendly command-line tools for administration. Creating these tools is essential for managing users without relying on direct database manipulation or a web UI. The initial setup helper (`setup_helper.py`) only handles dev-environment dependencies and needs to be augmented with a user-focused initialization flow.

**Summary of Completed Work:**
- [x] **1. Implement `init` Command for First-Time Setup:** Created a new `init_system` command in `toolboxv2/mods/helper.py` to guide users through creating the first administrative user.
- [x] **2. Create User Management Commands:** Implemented `create-user`, `delete-user`, and `list-users` commands in the `helper` module, which wrap the corresponding functions in `CloudM.AuthManager`.
- [x] **3. Create Device & Access Management Commands:** Implemented `create-invitation` and `send-magic-link` commands in the `helper` module.
- [x] **4. Update Documentation:** Created a new `docs/account_management.md` file and linked to it from `docs/management.md`.

---

## 3. P2P Secure Remote Function Access (High Priority)

**Status:** 🟡 **In Progress**

**Goal:** Extend the existing Rust-based P2P system (`tcm`) to allow secure, end-to-end encrypted remote procedure calls (RPC) to whitelisted ToolBoxV2 functions.

**Analysis:** The current `tcm` application provides a solid foundation for P2P connections with a relay server and TCP hole punching. However, it only proxies raw TCP data. The next logical step is to build a secure RPC layer on top of this connection to enable true remote function execution, transforming it from a simple pipe into an intelligent, secure application-level gateway.

**Sub-Tasks:**

- [x] **1. Design the P2P RPC Protocol:**
    -   Define a clear, JSON-based message structure for remote procedure calls. This structure must include:
        -   `call_id`: A unique identifier for each request to match responses.
        -   `module`: The name of the ToolBoxV2 module (e.g., `MyMod`).
        -   `function`: The name of the function to be executed (e.g., `my_function`).
        -   `args`: A list of positional arguments.
        -   `kwargs`: A dictionary of keyword arguments.
    -   Define the corresponding response structure, including `call_id`, `result`, and `error` fields. use the ApiResult Type from toolboxv2/utils/system/types.py

- [x] **2. Enhance the `tcm` Peer (Rust):**
    -   Modify the `proxy_data` function in `tcm/src/main.rs`.
    -   Instead of just forwarding raw bytes, it will now read the JSON-RPC messages from the local service, encrypt them, and send them to the remote peer.
    -   It will also decrypt incoming messages from the remote peer and forward them to the local service.

- [x] **3. Create a Python P2P RPC Server:**
    -   Create a new Python module, `P2PRPCServer`, that runs on the **provider** peer.
    -   This server will listen for incoming connections from the local `tcm` instance.
    -   Upon receiving a decrypted RPC call, it will:
        -   **Security:** Validate that the requested module and function are on a centrally managed whitelist of allowed remote functions.re
        -   Call the requested ToolBoxV2 function using `app.a_run_any()`.
        -   Serialize the result (or error) and send it back to the `tcm` instance for encryption and transmission.

- [x] **4. Create a Python P2P RPC Client:**
    -   Create a new Python module, `P2PRPCClient`, that runs on the **consumer** peer.
    -   This client will provide a simple API for developers to make remote calls (e.g., `p2p_client.call('MyMod', 'my_function', arg1, kwarg2='value')`).
    -   It will connect to the local `tcm` instance, send the serialized RPC request, and wait for the corresponding response.
    -   **Security:** Implemented `TB_R_KEY` for authentication and identification, and integrated one-time key exchange for secure communication.

- [x] **5. Integrate into `tcm_p2p_cli.py`:**
    -   Update the `start-peer` command to automatically launch the `P2PRPCServer` when a peer is started in "provider" mode (using the `--forward` argument).
    -   Ensure that the `forward_to_address` in the `config.toml` points to the address where the new `P2PRPCServer` is listening.

---

## 4. `--test` Command Enhancement (Medium Priority)

**Status:** ⬜ **To-Do**

**Goal:** Extend the `--test` command to automatically discover and run `npm test` for frontend packages.

**Analysis:** This is a quality-of-life improvement that will streamline the development and testing workflow by unifying backend and frontend testing into a single command.

**Sub-Tasks:**

- [x] **1. Locate Core Test Runner:** Identify the primary function or script in the `toolboxv2` core that is responsible for executing tests.
- [x] **2. Find `package.json`:** Confirm the location and script name (`test`) within the relevant `package.json` file (likely in a `tbjs` or frontend-related directory).
- [x] **3. Modify Test Runner:** Update the test runner logic to:
    -   Scan for the presence of a `package.json` file in the project root or relevant subdirectories.
    -   If found, execute the `npm test` command using Python's `subprocess` module.
- [x] **4. Capture and Display Output:** Ensure the `stdout` and `stderr` from the `npm test` command are captured and cleanly displayed alongside the results of the Python tests.
