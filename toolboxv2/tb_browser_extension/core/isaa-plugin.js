// ISAA Plugin for ToolBox Chrome Extension
// Intelligent System Agent Architecture Integration

class TBISAAPlugin {
    constructor() {
        this.isInitialized = false;
        this.agents = new Map();
        this.chatSessions = new Map();
        this.currentSession = null;
        this.settings = {
            serverUrl: 'http://localhost:8080',
            apiEndpoint: '/api/isaa',
            enableChat: true,
            enableFormAutomation: true,
            enablePageScraping: true,
            autoMode: false,
            debugMode: true
        };

        // Agent configurations
        this.agentConfigs = {
            'web-assistant': {
                name: 'web-assistant',
                systemMessage: 'You are a web assistant that helps users interact with web pages. You can fill forms, navigate pages, and extract information.',
                capabilities: ['form-filling', 'navigation', 'scraping', 'chat']
            },
            'form-filler': {
                name: 'form-filler',
                systemMessage: 'You are specialized in filling web forms accurately and efficiently. You understand form fields and can input appropriate data.',
                capabilities: ['form-filling', 'validation']
            },
            'page-navigator': {
                name: 'page-navigator',
                systemMessage: 'You help users navigate web pages by clicking links, buttons, and interacting with page elements.',
                capabilities: ['navigation', 'interaction']
            },
            'data-scraper': {
                name: 'data-scraper',
                systemMessage: 'You extract and structure information from web pages, providing clean, organized data.',
                capabilities: ['scraping', 'extraction', 'analysis']
            }
        };

        this.init();
    }

    async init() {
        try {
            await this.loadSettings();
            await this.initializeAgents();
            this.setupEventListeners();
            this.isInitialized = true;

            TBUtils.info('ISAAPlugin', 'ISAA Plugin initialized successfully');
        } catch (error) {
            TBUtils.handleError('ISAAPlugin', error);
        }
    }

    async loadSettings() {
        try {
            const stored = await TBUtils.getStorage([
                'isaa_server_url',
                'isaa_enable_chat',
                'isaa_enable_form_automation',
                'isaa_enable_page_scraping',
                'isaa_auto_mode',
                'isaa_debug_mode'
            ]);

            this.settings = {
                serverUrl: stored.isaa_server_url || this.settings.serverUrl,
                enableChat: stored.isaa_enable_chat !== false,
                enableFormAutomation: stored.isaa_enable_form_automation !== false,
                enablePageScraping: stored.isaa_enable_page_scraping !== false,
                autoMode: stored.isaa_auto_mode || false,
                debugMode: stored.isaa_debug_mode || false
            };
        } catch (error) {
            TBUtils.warn('ISAAPlugin', 'Failed to load settings, using defaults');
        }
    }

    async initializeAgents() {
        // Initialize local ISAA agents (simulated for browser environment)
        for (const [agentId, config] of Object.entries(this.agentConfigs)) {
            this.agents.set(agentId, {
                id: agentId,
                config: config,
                status: 'ready',
                lastUsed: null,
                conversationHistory: []
            });
        }

        TBUtils.info('ISAAPlugin', `Initialized ${this.agents.size} ISAA agents`);
    }

    setupEventListeners() {
        // Listen for form detection
        this.observeFormChanges();

        // Listen for page navigation
        this.observePageChanges();

        // Listen for user interactions
        this.setupInteractionListeners();
    }

    // ==================== CHAT FUNCTIONALITY ====================

    async startChatSession(sessionId = null) {
        if (!sessionId) {
            sessionId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }

        const session = {
            id: sessionId,
            startTime: Date.now(),
            messages: [],
            context: {
                url: window.location.href,
                title: document.title,
                pageContent: this.extractPageContext()
            },
            activeAgent: 'web-assistant'
        };

        this.chatSessions.set(sessionId, session);
        this.currentSession = sessionId;

        TBUtils.info('ISAAPlugin', `Started chat session: ${sessionId}`);
        return sessionId;
    }

    async sendChatMessage(message, sessionId = null) {
        if (!sessionId) {
            sessionId = this.currentSession || await this.startChatSession();
        }

        const session = this.chatSessions.get(sessionId);
        if (!session) {
            throw new Error(`Chat session ${sessionId} not found`);
        }

        // Add user message
        session.messages.push({
            role: 'user',
            content: message,
            timestamp: Date.now()
        });

        // Process with ISAA agent
        const response = await this.processWithAgent(message, session);

        // Add assistant response
        session.messages.push({
            role: 'assistant',
            content: response.content,
            timestamp: Date.now(),
            actions: response.actions || []
        });

        // Execute any actions
        if (response.actions && response.actions.length > 0) {
            await this.executeActions(response.actions);
        }

        return {
            sessionId: sessionId,
            response: response.content,
            actions: response.actions || []
        };
    }

    async processWithAgent(message, session) {
        const agent = this.agents.get(session.activeAgent);
        if (!agent) {
            throw new Error(`Agent ${session.activeAgent} not found`);
        }

        // Use ToolBox Python ISAA API for real processing
        const response = await this.callISAAAPI(message, session, agent);

        // Update agent usage
        agent.lastUsed = Date.now();
        agent.conversationHistory.push({
            input: message,
            output: response.content,
            timestamp: Date.now()
        });

        return response;
    }

    async callISAAAPI(message, session, agent) {
        try {
            // Analyze intent first to determine the best approach
            const intent = this.analyzeIntent(message, session.context);

            let response;

            if (intent.type === 'form-filling' || intent.type === 'navigation' || intent.type === 'scraping') {
                // Use mini_task_completion for complex tasks
                response = await this.callMiniTaskCompletion(message, intent, session);
            } else {
                // Use format_class for structured responses
                response = await this.callFormatClass(message, intent, session);
            }

            return response;
        } catch (error) {
            TBUtils.handleError('ISAAPlugin', error);
            // Fallback to local processing
            return await this.simulateISAAProcessing(message, session, agent);
        }
    }

    async callMiniTaskCompletion(message, intent, session) {
        const url = `${this.settings.serverUrl}/api/isaa/mini_task_completion`;

        const payload = {
            mini_task: message,
            user_task: `Web interaction on ${session.context.url}`,
            mode: intent.type,
            agent_name: session.activeAgent || 'web-assistant',
            task_from: 'browser_extension',
            context: {
                page_url: session.context.url,
                page_title: session.context.title,
                page_content: session.context.pageContent,
                intent: intent,
                conversation_history: session.messages.slice(-3)
            }
        };

        if (this.settings.debugMode) {
            TBUtils.info('ISAAPlugin', 'Calling mini_task_completion:', url, payload);
        }

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API call failed: ${response.status} - ${errorText}`);
            }

            const result = await response.json();

            if (this.settings.debugMode) {
                TBUtils.info('ISAAPlugin', 'mini_task_completion response:', result);
            }

            return {
                content: result.response || result.content || result.result || 'Task completed',
                actions: this.parseActionsFromResponse(result, intent)
            };
        } catch (error) {
            TBUtils.handleError('ISAAPlugin', 'mini_task_completion failed:', error);
            throw error;
        }
    }

    async callFormatClass(message, intent, session) {
        const url = `${this.settings.serverUrl}/api/isaa/format_class`;

        // Define response schema based on intent
        const schema = this.getResponseSchema(intent.type);

        const payload = {
            format_schema: schema,
            task: `${message}\n\nContext: User is on ${session.context.url} - ${session.context.title}`,
            agent_name: session.activeAgent || 'web-assistant',
            auto_context: true,
            context: {
                page_url: session.context.url,
                page_title: session.context.title,
                intent: intent
            }
        };

        if (this.settings.debugMode) {
            TBUtils.info('ISAAPlugin', 'Calling format_class:', url, payload);
        }

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API call failed: ${response.status} - ${errorText}`);
            }

            const result = await response.json();

            if (this.settings.debugMode) {
                TBUtils.info('ISAAPlugin', 'format_class response:', result);
            }

            return {
                content: result.response || this.formatStructuredResponse(result),
                actions: this.parseActionsFromStructured(result, intent)
            };
        } catch (error) {
            TBUtils.handleError('ISAAPlugin', 'format_class failed:', error);
            throw error;
        }
    }

    getResponseSchema(intentType) {
        const schemas = {
            'chat': {
                type: 'object',
                properties: {
                    response: { type: 'string', description: 'Helpful response to user' },
                    suggestions: { type: 'array', items: { type: 'string' }, description: 'Suggested actions' },
                    confidence: { type: 'number', description: 'Confidence in response' }
                },
                required: ['response']
            },
            'form-filling': {
                type: 'object',
                properties: {
                    response: { type: 'string', description: 'Response about form filling' },
                    form_data: { type: 'object', description: 'Data to fill in forms' },
                    actions: { type: 'array', items: { type: 'string' }, description: 'Actions to perform' }
                },
                required: ['response']
            },
            'navigation': {
                type: 'object',
                properties: {
                    response: { type: 'string', description: 'Response about navigation' },
                    target_elements: { type: 'array', items: { type: 'string' }, description: 'Elements to click' },
                    actions: { type: 'array', items: { type: 'string' }, description: 'Navigation actions' }
                },
                required: ['response']
            },
            'scraping': {
                type: 'object',
                properties: {
                    response: { type: 'string', description: 'Response about data extraction' },
                    extracted_data: { type: 'object', description: 'Extracted information' },
                    data_types: { type: 'array', items: { type: 'string' }, description: 'Types of data found' }
                },
                required: ['response']
            }
        };

        return schemas[intentType] || schemas['chat'];
    }

    parseActionsFromResponse(result, intent) {
        const actions = [];

        if (result.actions) {
            result.actions.forEach(action => {
                actions.push({
                    type: this.mapActionType(action, intent.type),
                    description: action,
                    data: result.context || {}
                });
            });
        }

        return actions;
    }

    parseActionsFromStructured(result, intent) {
        const actions = [];

        if (result.actions) {
            result.actions.forEach(action => {
                actions.push({
                    type: this.mapActionType(action, intent.type),
                    description: action,
                    data: result
                });
            });
        }

        // Generate actions based on intent and structured data
        if (intent.type === 'form-filling' && result.form_data) {
            actions.push({
                type: 'fill-forms',
                data: result.form_data,
                description: 'Fill detected forms with provided data'
            });
        }

        if (intent.type === 'navigation' && result.target_elements) {
            result.target_elements.forEach(element => {
                actions.push({
                    type: 'click-element',
                    target: element,
                    description: `Click on ${element}`
                });
            });
        }

        if (intent.type === 'scraping' && result.data_types) {
            actions.push({
                type: 'extract-data',
                data: result.extracted_data,
                types: result.data_types,
                description: 'Extract specified data from page'
            });
        }

        return actions;
    }

    mapActionType(action, intentType) {
        const actionMap = {
            'form-filling': {
                'fill': 'fill-field',
                'submit': 'submit-form',
                'clear': 'clear-field'
            },
            'navigation': {
                'click': 'click-element',
                'navigate': 'navigate-to',
                'scroll': 'scroll-to'
            },
            'scraping': {
                'extract': 'extract-data',
                'scrape': 'scrape-content',
                'analyze': 'analyze-content'
            }
        };

        const typeMap = actionMap[intentType] || {};

        for (const [key, value] of Object.entries(typeMap)) {
            if (action.toLowerCase().includes(key)) {
                return value;
            }
        }

        return 'generic-action';
    }

    formatStructuredResponse(result) {
        if (result.response) {
            return result.response;
        }

        let formatted = '';

        if (result.form_data) {
            formatted += 'Form data to fill:\n';
            for (const [key, value] of Object.entries(result.form_data)) {
                formatted += `- ${key}: ${value}\n`;
            }
        }

        if (result.extracted_data) {
            formatted += 'Extracted data:\n';
            formatted += JSON.stringify(result.extracted_data, null, 2);
        }

        if (result.suggestions) {
            formatted += '\nSuggestions:\n';
            result.suggestions.forEach((suggestion, index) => {
                formatted += `${index + 1}. ${suggestion}\n`;
            });
        }

        return formatted || 'Task completed successfully';
    }

    async simulateISAAProcessing(message, session, agent) {
        // Local ISAA-like processing
        const context = {
            pageUrl: session.context.url,
            pageTitle: session.context.title,
            pageContent: session.context.pageContent,
            conversationHistory: session.messages.slice(-5), // Last 5 messages
            agentCapabilities: agent.config.capabilities
        };

        // Analyze message intent
        const intent = this.analyzeIntent(message, context);

        // Generate response based on intent
        const response = await this.generateResponse(intent, message, context);

        return response;
    }

    analyzeIntent(message, context) {
        const messageLower = message.toLowerCase();

        // Form-related intents
        if (messageLower.includes('fill') || messageLower.includes('form') || messageLower.includes('input')) {
            return {
                type: 'form-filling',
                confidence: 0.9,
                entities: this.extractFormEntities(message)
            };
        }

        // Navigation intents
        if (messageLower.includes('click') || messageLower.includes('navigate') || messageLower.includes('go to')) {
            return {
                type: 'navigation',
                confidence: 0.85,
                entities: this.extractNavigationEntities(message)
            };
        }

        // Scraping intents
        if (messageLower.includes('extract') || messageLower.includes('get') || messageLower.includes('find')) {
            return {
                type: 'scraping',
                confidence: 0.8,
                entities: this.extractScrapingEntities(message)
            };
        }

        // General chat
        return {
            type: 'chat',
            confidence: 0.7,
            entities: {}
        };
    }

    async generateResponse(intent, message, context) {
        switch (intent.type) {
            case 'form-filling':
                return await this.handleFormFillingIntent(intent, message, context);
            case 'navigation':
                return await this.handleNavigationIntent(intent, message, context);
            case 'scraping':
                return await this.handleScrapingIntent(intent, message, context);
            default:
                return await this.handleChatIntent(intent, message, context);
        }
    }

    // ==================== FORM AUTOMATION ====================

    async handleFormFillingIntent(intent, message, context) {
        const forms = this.detectForms();

        if (forms.length === 0) {
            return {
                content: "I don't see any forms on this page that I can fill. Could you point me to a specific form?",
                actions: []
            };
        }

        // Analyze what data to fill
        const fillData = this.extractFillData(message, intent.entities);

        // Generate fill actions
        const actions = this.generateFormFillActions(forms, fillData);

        return {
            content: `I found ${forms.length} form(s) on this page. I'll fill them with the information you provided.`,
            actions: actions
        };
    }

    detectForms() {
        const forms = [];
        const formElements = document.querySelectorAll('form');

        formElements.forEach((form, index) => {
            const fields = [];
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                if (input.type !== 'hidden' && input.type !== 'submit') {
                    fields.push({
                        element: input,
                        type: input.type || input.tagName.toLowerCase(),
                        name: input.name || input.id || `field_${fields.length}`,
                        label: this.getFieldLabel(input),
                        required: input.required,
                        placeholder: input.placeholder
                    });
                }
            });

            forms.push({
                element: form,
                index: index,
                fields: fields,
                action: form.action,
                method: form.method
            });
        });

        return forms;
    }

    getFieldLabel(input) {
        // Try to find associated label
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (label) return label.textContent.trim();
        }

        // Try parent label
        const parentLabel = input.closest('label');
        if (parentLabel) return parentLabel.textContent.trim();

        // Try previous sibling
        let prev = input.previousElementSibling;
        while (prev) {
            if (prev.tagName === 'LABEL') {
                return prev.textContent.trim();
            }
            if (prev.textContent && prev.textContent.trim().length < 50) {
                return prev.textContent.trim();
            }
            prev = prev.previousElementSibling;
        }

        return input.placeholder || input.name || 'Unknown field';
    }

    extractFillData(message, entities) {
        const data = {};

        // Extract common patterns
        const patterns = {
            email: /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
            phone: /(\+?[\d\s\-\(\)]{10,})/g,
            name: /name[:\s]+([a-zA-Z\s]+)/gi,
            address: /address[:\s]+([a-zA-Z0-9\s,.-]+)/gi
        };

        for (const [type, pattern] of Object.entries(patterns)) {
            const matches = message.match(pattern);
            if (matches) {
                data[type] = matches[0];
            }
        }

        return data;
    }

    generateFormFillActions(forms, fillData) {
        const actions = [];

        forms.forEach(form => {
            form.fields.forEach(field => {
                const value = this.matchFieldToData(field, fillData);
                if (value) {
                    actions.push({
                        type: 'fill-field',
                        target: field.element,
                        value: value,
                        fieldName: field.name,
                        fieldLabel: field.label
                    });
                }
            });
        });

        return actions;
    }

    matchFieldToData(field, fillData) {
        const fieldName = field.name.toLowerCase();
        const fieldLabel = field.label.toLowerCase();

        // Email fields
        if ((fieldName.includes('email') || fieldLabel.includes('email')) && fillData.email) {
            return fillData.email;
        }

        // Phone fields
        if ((fieldName.includes('phone') || fieldLabel.includes('phone')) && fillData.phone) {
            return fillData.phone;
        }

        // Name fields
        if ((fieldName.includes('name') || fieldLabel.includes('name')) && fillData.name) {
            return fillData.name;
        }

        // Address fields
        if ((fieldName.includes('address') || fieldLabel.includes('address')) && fillData.address) {
            return fillData.address;
        }

        return null;
    }

    // ==================== NAVIGATION FUNCTIONALITY ====================

    async handleNavigationIntent(intent, message, context) {
        const navigationTargets = this.findNavigationTargets(message);

        if (navigationTargets.length === 0) {
            return {
                content: "I couldn't find any clickable elements matching your request. Could you be more specific?",
                actions: []
            };
        }

        const actions = navigationTargets.map(target => ({
            type: 'click-element',
            target: target.element,
            description: target.description
        }));

        return {
            content: `I found ${navigationTargets.length} element(s) to click: ${navigationTargets.map(t => t.description).join(', ')}`,
            actions: actions
        };
    }

    findNavigationTargets(message) {
        const targets = [];
        const messageLower = message.toLowerCase();

        // Find clickable elements
        const clickableElements = document.querySelectorAll('a, button, [onclick], [role="button"], input[type="submit"], input[type="button"]');

        clickableElements.forEach(element => {
            const text = element.textContent.trim().toLowerCase();
            const href = element.href || '';
            const title = element.title || '';

            // Simple text matching
            if (text && (messageLower.includes(text) || text.includes(this.extractKeywords(messageLower)))) {
                targets.push({
                    element: element,
                    description: text || href || title || 'Clickable element',
                    confidence: 0.8
                });
            }
        });

        return targets.slice(0, 5); // Limit to top 5 matches
    }

    extractKeywords(text) {
        // Extract meaningful keywords from user message
        const stopWords = ['click', 'on', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'at', 'to', 'for', 'of', 'with', 'by'];
        const words = text.split(/\s+/).filter(word =>
            word.length > 2 && !stopWords.includes(word)
        );
        return words.join('|');
    }

    // ==================== SCRAPING FUNCTIONALITY ====================

    async handleScrapingIntent(intent, message, context) {
        const scrapingTargets = this.identifyScrapingTargets(message);
        const extractedData = this.extractData(scrapingTargets);

        return {
            content: `I extracted the following information from the page:\n\n${this.formatExtractedData(extractedData)}`,
            actions: [{
                type: 'data-extracted',
                data: extractedData
            }]
        };
    }

    identifyScrapingTargets(message) {
        const messageLower = message.toLowerCase();
        const targets = [];

        // Common data extraction patterns
        if (messageLower.includes('title') || messageLower.includes('heading')) {
            targets.push({
                type: 'headings',
                elements: document.querySelectorAll('h1, h2, h3, h4, h5, h6')
            });
        }

        if (messageLower.includes('link') || messageLower.includes('url')) {
            targets.push({
                type: 'links',
                elements: document.querySelectorAll('a[href]')
            });
        }

        if (messageLower.includes('image') || messageLower.includes('picture')) {
            targets.push({
                type: 'images',
                elements: document.querySelectorAll('img')
            });
        }

        if (messageLower.includes('text') || messageLower.includes('content')) {
            targets.push({
                type: 'text',
                elements: document.querySelectorAll('p, div, span')
            });
        }

        if (messageLower.includes('table') || messageLower.includes('data')) {
            targets.push({
                type: 'tables',
                elements: document.querySelectorAll('table')
            });
        }

        return targets;
    }

    extractData(targets) {
        const data = {};

        targets.forEach(target => {
            const items = [];

            target.elements.forEach((element, index) => {
                if (index >= 20) return; // Limit to prevent overwhelming data

                switch (target.type) {
                    case 'headings':
                        items.push({
                            level: element.tagName,
                            text: element.textContent.trim()
                        });
                        break;
                    case 'links':
                        items.push({
                            text: element.textContent.trim(),
                            url: element.href
                        });
                        break;
                    case 'images':
                        items.push({
                            alt: element.alt,
                            src: element.src,
                            title: element.title
                        });
                        break;
                    case 'text':
                        const text = element.textContent.trim();
                        if (text.length > 20 && text.length < 200) {
                            items.push({ text: text });
                        }
                        break;
                    case 'tables':
                        items.push(this.extractTableData(element));
                        break;
                }
            });

            if (items.length > 0) {
                data[target.type] = items;
            }
        });

        return data;
    }

    extractTableData(table) {
        const data = {
            headers: [],
            rows: []
        };

        // Extract headers
        const headerCells = table.querySelectorAll('th');
        headerCells.forEach(cell => {
            data.headers.push(cell.textContent.trim());
        });

        // Extract rows
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                const rowData = [];
                cells.forEach(cell => {
                    rowData.push(cell.textContent.trim());
                });
                data.rows.push(rowData);
            }
        });

        return data;
    }

    formatExtractedData(data) {
        let formatted = '';

        for (const [type, items] of Object.entries(data)) {
            formatted += `**${type.toUpperCase()}:**\n`;

            items.slice(0, 10).forEach((item, index) => {
                switch (type) {
                    case 'headings':
                        formatted += `${index + 1}. [${item.level}] ${item.text}\n`;
                        break;
                    case 'links':
                        formatted += `${index + 1}. ${item.text} (${item.url})\n`;
                        break;
                    case 'images':
                        formatted += `${index + 1}. ${item.alt || 'Image'} - ${item.src}\n`;
                        break;
                    case 'text':
                        formatted += `${index + 1}. ${item.text}\n`;
                        break;
                    case 'tables':
                        formatted += `${index + 1}. Table with ${item.headers.length} columns, ${item.rows.length} rows\n`;
                        break;
                }
            });

            formatted += '\n';
        }

        return formatted || 'No relevant data found on this page.';
    }

    // ==================== GENERAL CHAT FUNCTIONALITY ====================

    async handleChatIntent(intent, message, context) {
        // Generate contextual response based on page content
        const pageInfo = this.analyzeCurrentPage();

        const responses = [
            `I'm here to help you with this page. I can see you're on "${pageInfo.title}". What would you like me to do?`,
            `I can help you fill forms, navigate the page, or extract information. What do you need assistance with?`,
            `I'm your web assistant. I can interact with forms, click elements, or gather data from this page. How can I help?`,
            `I notice this page has ${pageInfo.formCount} form(s) and ${pageInfo.linkCount} link(s). What would you like me to help you with?`
        ];

        return {
            content: responses[Math.floor(Math.random() * responses.length)],
            actions: []
        };
    }

    analyzeCurrentPage() {
        return {
            title: document.title,
            url: window.location.href,
            formCount: document.querySelectorAll('form').length,
            linkCount: document.querySelectorAll('a').length,
            hasLogin: this.hasLoginForm(),
            hasSearch: this.hasSearchForm()
        };
    }

    hasLoginForm() {
        const forms = document.querySelectorAll('form');
        for (const form of forms) {
            const inputs = form.querySelectorAll('input');
            let hasPassword = false;
            let hasEmail = false;

            for (const input of inputs) {
                if (input.type === 'password') hasPassword = true;
                if (input.type === 'email' || input.name.includes('email')) hasEmail = true;
            }

            if (hasPassword && hasEmail) return true;
        }
        return false;
    }

    hasSearchForm() {
        const searchInputs = document.querySelectorAll('input[type="search"], input[name*="search"], input[placeholder*="search"]');
        return searchInputs.length > 0;
    }

    // ==================== ACTION EXECUTION ====================

    async executeActions(actions) {
        const results = [];

        for (const action of actions) {
            try {
                const result = await this.executeAction(action);
                results.push(result);

                // Add delay between actions to prevent overwhelming the page
                await this.delay(500);
            } catch (error) {
                TBUtils.handleError('ISAAPlugin', error);
                results.push({
                    action: action.type,
                    success: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    async executeAction(action) {
        switch (action.type) {
            case 'fill-field':
                return await this.fillField(action);
            case 'click-element':
                return await this.clickElement(action);
            case 'data-extracted':
                return await this.handleDataExtracted(action);
            default:
                throw new Error(`Unknown action type: ${action.type}`);
        }
    }

    async fillField(action) {
        const element = action.target;
        const value = action.value;

        if (!element || !element.isConnected) {
            throw new Error('Target element not found or disconnected');
        }

        // Focus the element
        element.focus();

        // Clear existing value
        element.value = '';

        // Simulate typing
        await this.simulateTyping(element, value);

        // Trigger change events
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));

        return {
            action: 'fill-field',
            success: true,
            fieldName: action.fieldName,
            value: value
        };
    }

    async simulateTyping(element, text) {
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            element.value += char;

            // Dispatch keydown, keypress, keyup events
            element.dispatchEvent(new KeyboardEvent('keydown', { key: char, bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keypress', { key: char, bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { key: char, bubbles: true }));

            // Small delay to simulate human typing
            await this.delay(50 + Math.random() * 50);
        }
    }

    async clickElement(action) {
        const element = action.target;

        if (!element || !element.isConnected) {
            throw new Error('Target element not found or disconnected');
        }

        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Wait for scroll to complete
        await this.delay(300);

        // Highlight element briefly
        this.highlightElement(element, 1000);

        // Click the element
        element.click();

        return {
            action: 'click-element',
            success: true,
            description: action.description
        };
    }

    async handleDataExtracted(action) {
        // Store extracted data for potential use
        const sessionId = this.currentSession;
        if (sessionId) {
            const session = this.chatSessions.get(sessionId);
            if (session) {
                session.extractedData = action.data;
            }
        }

        return {
            action: 'data-extracted',
            success: true,
            dataTypes: Object.keys(action.data),
            itemCount: Object.values(action.data).reduce((sum, items) => sum + items.length, 0)
        };
    }

    highlightElement(element, duration = 2000) {
        const originalStyle = element.style.cssText;

        element.style.cssText += `
            outline: 3px solid #6c8ee8 !important;
            outline-offset: 2px !important;
            background: rgba(108, 142, 232, 0.1) !important;
            transition: all 0.3s ease !important;
        `;

        setTimeout(() => {
            element.style.cssText = originalStyle;
        }, duration);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ==================== UTILITY METHODS ====================

    extractPageContext() {
        return {
            title: document.title,
            url: window.location.href,
            headings: Array.from(document.querySelectorAll('h1, h2, h3')).map(h => h.textContent.trim()).slice(0, 5),
            forms: this.detectForms().length,
            links: document.querySelectorAll('a').length,
            images: document.querySelectorAll('img').length
        };
    }

    extractFormEntities(message) {
        const entities = {};

        // Extract email addresses
        const emailMatch = message.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        if (emailMatch) entities.email = emailMatch[1];

        // Extract phone numbers
        const phoneMatch = message.match(/(\+?[\d\s\-\(\)]{10,})/);
        if (phoneMatch) entities.phone = phoneMatch[1];

        // Extract names (simple pattern)
        const nameMatch = message.match(/name[:\s]+([a-zA-Z\s]+)/i);
        if (nameMatch) entities.name = nameMatch[1].trim();

        return entities;
    }

    extractNavigationEntities(message) {
        const entities = {};

        // Extract button/link text to click
        const clickMatch = message.match(/click[:\s]+["']?([^"']+)["']?/i);
        if (clickMatch) entities.target = clickMatch[1].trim();

        return entities;
    }

    extractScrapingEntities(message) {
        const entities = {};

        // Extract what type of data to scrape
        if (message.includes('title')) entities.type = 'titles';
        if (message.includes('link')) entities.type = 'links';
        if (message.includes('image')) entities.type = 'images';
        if (message.includes('text')) entities.type = 'text';
        if (message.includes('table')) entities.type = 'tables';

        return entities;
    }

    // ==================== PAGE OBSERVATION ====================

    observeFormChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const addedNodes = Array.from(mutation.addedNodes);
                    const hasNewForms = addedNodes.some(node =>
                        node.nodeType === Node.ELEMENT_NODE &&
                        (node.tagName === 'FORM' || node.querySelector('form'))
                    );

                    if (hasNewForms && this.settings.autoMode) {
                        this.handleNewFormsDetected();
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    observePageChanges() {
        // Observe URL changes
        let currentUrl = window.location.href;

        const checkUrlChange = () => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                this.handlePageNavigation();
            }
        };

        // Check for URL changes periodically
        setInterval(checkUrlChange, 1000);

        // Listen for popstate events
        window.addEventListener('popstate', () => {
            this.handlePageNavigation();
        });
    }

    setupInteractionListeners() {
        // Listen for form submissions
        document.addEventListener('submit', (event) => {
            if (this.settings.debugMode) {
                TBUtils.info('ISAAPlugin', 'Form submitted:', event.target);
            }
        });

        // Listen for clicks on important elements
        document.addEventListener('click', (event) => {
            const target = event.target;
            if (target.tagName === 'BUTTON' || target.tagName === 'A' || target.type === 'submit') {
                if (this.settings.debugMode) {
                    TBUtils.info('ISAAPlugin', 'Important element clicked:', target);
                }
            }
        });
    }

    handleNewFormsDetected() {
        if (this.currentSession) {
            // Notify current chat session about new forms
            TBUtils.info('ISAAPlugin', 'New forms detected on page');
        }
    }

    handlePageNavigation() {
        // Update context for all active sessions
        this.chatSessions.forEach((session) => {
            session.context = {
                ...session.context,
                url: window.location.href,
                title: document.title,
                pageContent: this.extractPageContext()
            };
        });

        if (this.settings.debugMode) {
            TBUtils.info('ISAAPlugin', 'Page navigation detected:', window.location.href);
        }
    }

    // ==================== PUBLIC API ====================

    async chat(message, sessionId = null) {
        return await this.sendChatMessage(message, sessionId);
    }

    async fillForm(data, formIndex = 0) {
        const forms = this.detectForms();
        if (forms.length === 0) {
            throw new Error('No forms found on page');
        }

        const form = forms[formIndex];
        const actions = this.generateFormFillActions([form], data);

        return await this.executeActions(actions);
    }

    async extractPageData(types = ['headings', 'links', 'text']) {
        const targets = types.map(type => ({
            type: type,
            elements: this.getElementsForType(type)
        }));

        return this.extractData(targets);
    }

    getElementsForType(type) {
        switch (type) {
            case 'headings': return document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            case 'links': return document.querySelectorAll('a[href]');
            case 'images': return document.querySelectorAll('img');
            case 'text': return document.querySelectorAll('p, div, span');
            case 'tables': return document.querySelectorAll('table');
            default: return document.querySelectorAll('*');
        }
    }

    getStatus() {
        return {
            initialized: this.isInitialized,
            activeAgents: this.agents.size,
            activeSessions: this.chatSessions.size,
            currentSession: this.currentSession,
            settings: this.settings
        };
    }
}

// Initialize ISAA Plugin
window.TBISAAPlugin = new TBISAAPlugin();
