Quality Check Report - 2025-05-24-183531
Commit: fbcd5dfaaa25c2240e2207aa89567defa678af2c
========================================

[Ruff] Exit Code: 1
[Ruff] Output:
test.py:10:1: I001 [*] Import block is un-sorted or un-formatted
   |
 8 |   """
 9 |
10 | / import argparse
11 | | import json
12 | | import pathlib
13 | | import subprocess
14 | | import sys
15 | | import tempfile
16 | | from typing import List, Dict
17 | |
18 | | import pandas as pd
   | |___________________^ I001
19 |   def safe_get_git_commits(repo: pathlib.Path, max_commits: int = 50) -> pd.DataFrame:
20 |       try:
   |
   = help: Organize imports

test.py:10:8: F401 [*] `argparse` imported but unused
   |
 8 | """
 9 |
10 | import argparse
   |        ^^^^^^^^ F401
11 | import json
12 | import pathlib
   |
   = help: Remove unused import: `argparse`

test.py:14:8: F401 [*] `sys` imported but unused
   |
12 | import pathlib
13 | import subprocess
14 | import sys
   |        ^^^ F401
15 | import tempfile
16 | from typing import List, Dict
   |
   = help: Remove unused import: `sys`

test.py:15:8: F401 [*] `tempfile` imported but unused
   |
13 | import subprocess
14 | import sys
15 | import tempfile
   |        ^^^^^^^^ F401
16 | from typing import List, Dict
   |
   = help: Remove unused import: `tempfile`

test.py:16:1: UP035 `typing.List` is deprecated, use `list` instead
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
17 |
18 | import pandas as pd
   |

test.py:16:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
17 |
18 | import pandas as pd
   |

test.py:16:20: F401 [*] `typing.List` imported but unused
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   |                    ^^^^ F401
17 |
18 | import pandas as pd
   |
   = help: Remove unused import

test.py:16:26: F401 [*] `typing.Dict` imported but unused
   |
14 | import sys
15 | import tempfile
16 | from typing import List, Dict
   |                          ^^^^ F401
17 |
18 | import pandas as pd
   |
   = help: Remove unused import

toolboxv2\__gui__.py:10:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
 8 |     import customtkinter as ctk
 9 | except ImportError:
10 |     os.system("pip install customtkinter")
   |     ^^^^^^^^^ S605
11 |     import customtkinter as ctk
   |

toolboxv2\__init__.py:4:18: F401 `yaml.safe_load` imported but unused
  |
2 | import os
3 |
4 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
5 |
6 | try:
  |
  = help: Remove unused import: `yaml.safe_load`

toolboxv2\__init__.py:116:12: F401 `toolboxv2.mods` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
114 | try:
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
    |            ^^^^^^^^^^^^^^ F401
117 |     from toolboxv2.mods import *
118 | except ImportError as e:
    |
    = help: Remove unused import: `toolboxv2.mods`

toolboxv2\__init__.py:117:5: F403 `from toolboxv2.mods import *` used; unable to detect undefined names
    |
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
117 |     from toolboxv2.mods import *
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
118 | except ImportError as e:
119 |     MODS_ERROR = e
    |

toolboxv2\__init__.py:129:22: F401 `platform.node` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                      ^^^^ F401
130 |
131 | __init_cwd__ = init_cwd = Path.cwd()
    |
    = help: Remove unused import

toolboxv2\__init__.py:129:28: F401 `platform.system` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                            ^^^^^^ F401
130 |
131 | __init_cwd__ = init_cwd = Path.cwd()
    |
    = help: Remove unused import

toolboxv2\__init__.py:150:5: F405 `mods` may be undefined, or defined from star imports
    |
148 |     "get_logger",
149 |     "flows_dict",
150 |     "mods",
    |     ^^^^^^ F405
151 |     "get_app",
152 |     "TBEF",
    |

toolboxv2\__main__.py:30:12: F401 `hmr` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
29 | try:
30 |     import hmr
   |            ^^^ F401
31 |
32 |     HOT_RELOADER = True
   |
   = help: Remove unused import: `hmr`

toolboxv2\__main__.py:104:5: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
102 |     def profile_execute_all_functions(*args):
103 |         return print(args)
104 |     raise ValueError("Failed to import function for profiling")
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
105 |
106 | try:
    |

toolboxv2\__main__.py:868:17: S605 Starting a process with a shell, possible injection detected
    |
866 |             print(f"Exit app {app_pid}")
867 |             if system() == "Windows":
868 |                 os.system(f"taskkill /pid {app_pid} /F")
    |                 ^^^^^^^^^ S605
869 |             else:
870 |                 os.system(f"kill -9 {app_pid}")
    |

toolboxv2\__main__.py:870:17: S605 Starting a process with a shell, possible injection detected
    |
868 |                 os.system(f"taskkill /pid {app_pid} /F")
869 |             else:
870 |                 os.system(f"kill -9 {app_pid}")
    |                 ^^^^^^^^^ S605
871 |
872 |     if args.command and not args.background_application:
    |

toolboxv2\__main__.py:894:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
893 | def install_ipython():
894 |     os.system('pip install ipython prompt_toolkit')
    |     ^^^^^^^^^ S605
    |

toolboxv2\__main__.py:1025:5: B018 Found useless expression. Either assign it to a variable or remove it.
     |
1024 | """)
1025 |     ()
     |     ^^ B018
1026 |     return c
     |

toolboxv2\apps\demo.py:15:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
14 | # Beispielinhalt der Streamlit-App
15 | st.title(f"{os.system('dir')}")
   |             ^^^^^^^^^ S605
16 | st.title("Meine Streamlit-App mit benutzerdefiniertem Styling")
17 | st.write("Dieser Text sollte gemäß dem benutzerdefinierten CSS gestylt sein.")
   |

toolboxv2\flows\core0.py:8:9: F401 `toolboxv2.mods.EventManager.module.EventID` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
 6 | try:
 7 |     from toolboxv2.mods.EventManager.module import (
 8 |         EventID,
   |         ^^^^^^^ F401
 9 |         EventManagerClass,
10 |         Scope,
   |
   = help: Remove unused import: `toolboxv2.mods.EventManager.module.EventID`

toolboxv2\flows\core0.py:82:5: S605 Starting a process with a shell, possible injection detected
   |
81 |     # os.system(f"toolboxv2 --test --debug")
82 |     os.system(f"tb -bgr -p 42869 -n core0 -l -m {NAME}")
   |     ^^^^^^^^^ S605
   |

toolboxv2\flows\docker.py:21:9: S605 Starting a process with a shell, possible injection detected
   |
19 |     app.print(f"Running command : {comm}")
20 |     try:
21 |         os.system(comm)
   |         ^^^^^^^^^ S605
22 |     except KeyboardInterrupt:
23 |         app.print("Exit")
   |

toolboxv2\flows\minicli.py:40:5: S605 Starting a process with a shell, possible injection detected
   |
38 |     _ = ""
39 |     _ = "powershell -Command " if pw else "bash -c "
40 |     os.system(_ + buff)
   |     ^^^^^^^^^ S605
   |

toolboxv2\flows\minicli.py:48:18: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
47 |     try:
48 |         result = eval(buff, app.globals['root'], app.locals['user'])
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
49 |         if result is not None:
50 |             print(f"+ #{app.locals['user']['counts']}>", result)
   |

toolboxv2\flows\minicli.py:55:13: S102 Use of `exec` detected
   |
53 |     except SyntaxError:
54 |         try:
55 |             exec(buff, app.globals['root'], app.locals['user'])
   |             ^^^^ S102
56 |             print(f"* #{app.locals['user']['counts']}> Statement executed")
57 |         except Exception as e:
   |

toolboxv2\flows\minicli.py:264:36: B023 Function definition does not bind loop variable `cpu_usage`
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                    ^^^^^^^^^ B023
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

toolboxv2\flows\minicli.py:264:57: B023 Function definition does not bind loop variable `memory_usage`
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                                         ^^^^^^^^^^^^ B023
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

toolboxv2\flows\minicli.py:264:79: B023 Function definition does not bind loop variable `disk_usage`
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                                                               ^^^^^^^^^^ B023
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

toolboxv2\flows\vad_talk.py:138:50: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
136 |                     if self.input_queue.qsize() < self.config.MAX_QUEUE_SIZE:
137 |                         self.input_queue.put(data)
138 |                         if self.config.DEBUG and random.random() < 0.01:  # Log occasionally in debug mode
    |                                                  ^^^^^^^^^^^^^^^ S311
139 |                             self.logger.debug(
140 |                                 f"Audio chunk queued: {len(data)} bytes, queue size: {self.input_queue.qsize()}")
    |

toolboxv2\flows\vad_talk.py:146:68: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
144 |                             self.logger.debug(f"Queue full ({self.input_queue.qsize()}), skipping audio frame")
145 |                         time.sleep(0.01)
146 |                 elif not self.mute_input and self.config.DEBUG and random.random() < 0.01:
    |                                                                    ^^^^^^^^^^^^^^^ S311
147 |                     self.logger.debug("Audio input (TTS active)")
    |

toolboxv2\flows\vad_talk.py:208:54: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
206 |                         if not self.mute_input and self.input_queue.qsize() < self.config.MAX_QUEUE_SIZE:
207 |                             self.input_queue.put(data)
208 |                             if self.config.DEBUG and random.random() < 0.01:
    |                                                      ^^^^^^^^^^^^^^^ S311
209 |                                 self.logger.debug(f"Websocket audio chunk received: {len(data)} bytes")
210 |             except Exception as e:
    |

toolboxv2\flows\vad_talk.py:330:42: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
328 |                 # Frame size is not 20ms or 40ms, use energy-based detection
329 |                 is_speech = avg_energy > self.energy_threshold
330 |                 if self.config.DEBUG and random.random() < 0.01:
    |                                          ^^^^^^^^^^^^^^^ S311
331 |                     self.logger.debug(
332 |                         f"Using energy-based detection for frame size {frame_size}: energy={avg_energy}, is_speech={is_speech}")
    |

toolboxv2\flows\vad_talk.py:439:50: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
438 |                     elif silence_duration > 0.1:  # Short pause detection
439 |                         if self.config.DEBUG and random.random() < 0.05:
    |                                                  ^^^^^^^^^^^^^^^ S311
440 |                             self.logger.debug(f"Thinking pause detected: {silence_duration:.2f}s")
441 |                         return "thinking", None
    |

toolboxv2\flows\vad_talk.py:558:55: B023 Function definition does not bind loop variable `audio_data`
    |
556 |                         try:
557 |                             # Transcribe the audio
558 |                             result = self._transcribe(audio_data)
    |                                                       ^^^^^^^^^^ B023
559 |                             with self.processing_lock:
    |

toolboxv2\flows\vad_talk.py:562:36: B023 Function definition does not bind loop variable `segment_id`
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
    |                          ^^^^^^^^^^ B023
563 | …                         self.transcription_segments[segment_id] = result
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

toolboxv2\flows\vad_talk.py:562:62: B023 Function definition does not bind loop variable `segment_id`
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
    |                                                    ^^^^^^^^^^ B023
563 | …                         self.transcription_segments[segment_id] = result
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

toolboxv2\flows\vad_talk.py:563:65: B023 Function definition does not bind loop variable `segment_id`
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
563 | …                         self.transcription_segments[segment_id] = result
    |                                                       ^^^^^^^^^^ B023
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

toolboxv2\flows\vad_talk.py:564:87: B023 Function definition does not bind loop variable `segment_id`
    |
562 |                                 if segment_id != "final" and segment_id != "legacy":
563 |                                     self.transcription_segments[segment_id] = result
564 |                                     self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |                                                                                       ^^^^^^^^^^ B023
565 |
566 |                                 # For final segments, try to concatenate previous segments if available
    |

toolboxv2\flows\vad_talk.py:567:36: B023 Function definition does not bind loop variable `is_final`
    |
566 | …                     # For final segments, try to concatenate previous segments if available
567 | …                     if is_final and self.transcription_segments:
    |                          ^^^^^^^^ B023
568 | …                         combined_result = self._concatenate_transcriptions(result)
569 | …                         self.logger.info(
    |

toolboxv2\flows\vad_talk.py:577:36: B023 Function definition does not bind loop variable `is_final`
    |
576 | …                     # Store for reference
577 | …                     if is_final:
    |                          ^^^^^^^^ B023
578 | …                         self.last_transcription = result
    |

toolboxv2\flows\vad_talk.py:581:36: B023 Function definition does not bind loop variable `callback`
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
    |                                    ^^^^^^^^ B023
582 |                                     self.results_queue.put((result, is_final, callback))
583 |                         finally:
    |

toolboxv2\flows\vad_talk.py:582:69: B023 Function definition does not bind loop variable `is_final`
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
582 |                                     self.results_queue.put((result, is_final, callback))
    |                                                                     ^^^^^^^^ B023
583 |                         finally:
584 |                             self.task_queue.task_done()
    |

toolboxv2\flows\vad_talk.py:582:79: B023 Function definition does not bind loop variable `callback`
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
582 |                                     self.results_queue.put((result, is_final, callback))
    |                                                                               ^^^^^^^^ B023
583 |                         finally:
584 |                             self.task_queue.task_done()
    |

toolboxv2\flows\vad_talk.py:792:24: SIM115 Use a context manager for opening files
    |
790 |                 return ""
791 |             # Create a temporary WAV file
792 |             temp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^ SIM115
793 |             wav_path = temp_wav.name
794 |             temp_wav.close()  # Close the file handle immediately
    |

toolboxv2\flows\vad_talk.py:1374:28: B023 Function definition does not bind loop variable `callback_start`
     |
1373 |                         # Trigger any provided callback before starting
1374 |                         if callback_start:
     |                            ^^^^^^^^^^^^^^ B023
1375 |                             callback_start()
     |

toolboxv2\flows\vad_talk.py:1375:29: B023 Function definition does not bind loop variable `callback_start`
     |
1373 |                         # Trigger any provided callback before starting
1374 |                         if callback_start:
1375 |                             callback_start()
     |                             ^^^^^^^^^^^^^^ B023
1376 |
1377 |                         self.logger.debug(f"Starting TTS for text: {text[:50]}{'...' if len(text) > 50 else ''}")
     |

toolboxv2\flows\vad_talk.py:1390:32: B023 Function definition does not bind loop variable `callback_end`
     |
1388 | …                     self.is_speaking = False
1389 | …                     #self.current_text = None
1390 | …                     if callback_end:
     |                          ^^^^^^^^^^^^ B023
1391 | …                         callback_end(text)
1392 | …                     #self.tts_queue.task_done()
     |

toolboxv2\flows\vad_talk.py:1391:33: B023 Function definition does not bind loop variable `callback_end`
     |
1389 | …                     #self.current_text = None
1390 | …                     if callback_end:
1391 | …                         callback_end(text)
     |                           ^^^^^^^^^^^^ B023
1392 | …                     #self.tts_queue.task_done()
1393 | …                     pass
     |

toolboxv2\mods\CloudM\AuthManager.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import base64
 3 | | import datetime
 4 | | import json
 5 | | import os
 6 | | import time
 7 | | import uuid
 8 | | from dataclasses import asdict
 9 | | from urllib.parse import quote
10 | |
11 | | import jwt
12 | | import webauthn
13 | | from pydantic import BaseModel, field_validator
14 | | from webauthn.helpers.exceptions import (
15 | |     InvalidAuthenticationResponse,
16 | |     InvalidRegistrationResponse,
17 | | )
18 | | from webauthn.helpers.structs import AuthenticationCredential, RegistrationCredential
19 | |
20 | | from toolboxv2 import TBEF, App, Result, ToolBox_over, get_app, get_logger
21 | | from toolboxv2.mods.DB.types import DatabaseModes
22 | | from toolboxv2.utils.security.cryp import Code
23 | | from toolboxv2.utils.system.types import ApiResult, ToolBoxInterfaces
24 | | from .email_services import send_magic_link_email
25 | |
26 | | from .types import User, UserCreator
   | |____________________________________^ I001
27 |
28 |   version = "0.0.2"
   |
   = help: Organize imports

toolboxv2\mods\CloudM\AuthManager.py:194:38: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
193 |     if isinstance(user_data, bytes):
194 |         return Result.ok(data=User(**eval(user_data.decode())))
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^ S307
195 |     if isinstance(user_data, str):
196 |         return Result.ok(data=User(**eval(user_data)))
    |

toolboxv2\mods\CloudM\AuthManager.py:196:38: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
194 |         return Result.ok(data=User(**eval(user_data.decode())))
195 |     if isinstance(user_data, str):
196 |         return Result.ok(data=User(**eval(user_data)))
    |                                      ^^^^^^^^^^^^^^^ S307
197 |     if isinstance(user_data, dict):
198 |         return Result.ok(data=User(**user_data))
    |

toolboxv2\mods\CloudM\AuthManager.py:209:38: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
207 |             user_data[0] = user_data[0].decode()
208 |
209 |         return Result.ok(data=User(**eval(user_data[0])))
    |                                      ^^^^^^^^^^^^^^^^^^ S307
210 |     else:
211 |         return Result.default_internal_error(info="get_user_by_name failed no User data found", exec_code=2351)
    |

toolboxv2\mods\CloudM\ModManager.py:80:9: S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
   |
78 |         print_func(f"{url} -> {directory}/{filename}")
79 |         os.makedirs(directory, exist_ok=True)
80 |         urllib.request.urlretrieve(url, f"{directory}/{filename}")
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S310
81 |     return f"{directory}/{filename}"
   |

toolboxv2\mods\CloudM\ModManager.py:89:9: S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
   |
87 |         requirements_filename = f"{module_name}-requirements.txt"
88 |         print_func(f"Download requirements {requirements_filename}")
89 |         urllib.request.urlretrieve(requirements_url, requirements_filename)
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S310
90 |
91 |         print_func("Install requirements")
   |

toolboxv2\mods\CloudM\ModManager.py:258:13: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
256 |               temp_dir = Path(temp_dir)
257 |
258 | /             with Spinner(f"Extracting {zip_path.name}"):
259 | |                 # Entpacke ZIP-Datei
260 | |                 with zipfile.ZipFile(zip_path, 'r') as zip_ref:
    | |_______________________________________________________________^ SIM117
261 |                       zip_ref.extractall(temp_dir)
    |
    = help: Combine `with` statements

toolboxv2\mods\CloudM\ModManager.py:462:11: F811 Redefinition of unused `update_all_mods` from line 423
    |
461 | @export(mod_name=Name, name="build_all", test=False)
462 | async def update_all_mods(app, base="mods", upload=True):
    |           ^^^^^^^^^^^^^^^ F811
463 |     if app is None:
464 |         app = get_app(f"{Name}.update_all")
    |
    = help: Remove definition: `update_all_mods`

toolboxv2\mods\CloudM\UI\UserAccountManager.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/CloudM/UI/user_account_manager.py
 2 |
 3 | / import uuid
 4 | | from dataclasses import asdict
 5 | |
 6 | |
 7 | | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user  # Assuming AuthManager functions are accessible
 9 | | from ..types import User  # From toolboxv2/mods/CloudM/types.py
   | |________________________^ I001
10 |
11 |   Name = 'CloudM.UI.UserAccountManager'
   |
   = help: Organize imports

toolboxv2\mods\CloudM\UI\UserAccountManager.py:3:8: F401 [*] `uuid` imported but unused
  |
1 | # toolboxv2/mods/CloudM/UI/user_account_manager.py
2 |
3 | import uuid
  |        ^^^^ F401
4 | from dataclasses import asdict
  |
  = help: Remove unused import: `uuid`

toolboxv2\mods\CloudM\UI\UserAccountManager.py:7:34: F401 [*] `toolboxv2.Result` imported but unused
  |
7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
  |                                  ^^^^^^ F401
8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_save_user  # Assuming AuthManager functions are accessible
9 | from ..types import User  # From toolboxv2/mods/CloudM/types.py
  |
  = help: Remove unused import: `toolboxv2.Result`

toolboxv2\mods\CloudM\UI\UserAccountManager.py:88:16: F541 [*] f-string without any placeholders
   |
86 |         target_id_suffix = target_id_suffix.split("-")[-1]
87 |     if not user:
88 |         return f"<div class='text-red-500'>Error: User not authenticated or found.</div>"
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
89 |
90 |     if setting_key == "experimental_features":
   |
   = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\UI\__init__.py:1:21: F401 `.widget.get_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .widget import get_widget
  |                     ^^^^^^^^^^ F401
  |
  = help: Use an explicit re-export: `get_widget as get_widget`

toolboxv2\mods\CloudM\UI\widget.py:3:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # toolboxv2/mods/CloudM/UI/widget.py
 2 |
 3 | / import uuid
 4 | | from dataclasses import asdict
 5 | | from datetime import datetime
 6 | |
 7 | | from toolboxv2 import TBEF, App, Result, get_app, RequestData
 8 | | from toolboxv2.mods.CloudM.AuthManager import db_helper_delete_user, db_helper_save_user
 9 | | from toolboxv2.mods.SocketManager import get_local_ip
10 | |
11 | | from ..types import User
12 | | # Import the Name constant from user_account_manager to use in TBEF calls
13 | | from .UserAccountManager import Name as UAM_ModuleName
   | |______________________________________________________^ I001
14 |
15 |   Name = 'CloudM.UI.widget'
   |
   = help: Organize imports

toolboxv2\mods\CloudM\UI\widget.py:8:47: F401 [*] `toolboxv2.mods.CloudM.AuthManager.db_helper_delete_user` imported but unused
  |
7 | from toolboxv2 import TBEF, App, Result, get_app, RequestData
8 | from toolboxv2.mods.CloudM.AuthManager import db_helper_delete_user, db_helper_save_user
  |                                               ^^^^^^^^^^^^^^^^^^^^^ F401
9 | from toolboxv2.mods.SocketManager import get_local_ip
  |
  = help: Remove unused import: `toolboxv2.mods.CloudM.AuthManager.db_helper_delete_user`

toolboxv2\mods\CloudM\UI\widget.py:36:25: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
34 |             else:
35 |                 user_str = str(user_bytes)  # Fallback if not bytes
36 |             user_dict = eval(user_str)
   |                         ^^^^^^^^^^^^^^ S307
37 |             # Ensure essential keys for the template exist
38 |             user_dict.setdefault('name', '--')
   |

toolboxv2\mods\CloudM\UI\widget.py:187:72: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
186 | # This is the main entry point for widget loading, needs to await async calls
187 | async def load_widget(app, display_name="Cud be ur name", WidgetID=str(uuid.uuid4())[:4]):
    |                                                                        ^^^^^^^^^^^^ B008
188 |     user_obj = User()  # Default user
189 |     if display_name != "Cud be ur name":
    |

toolboxv2\mods\CloudM\UserInstances.py:145:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
143 |       if instance['SiID'] in UserInstances().live_user_instances:
144 |           instance_live = UserInstances().live_user_instances.get(instance['SiID'], {})
145 | /         if 'live' in instance_live:
146 | |             if instance_live['live'] and instance_live['save']['mods']:
    | |_______________________________________________________________________^ SIM102
147 |                   logger.info(Style.BLUEBG2("Instance returned from live"))
148 |                   return instance_live
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\CloudM\__init__.py:1:21: F401 `.extras.login` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .extras import login
  |                     ^^^^^ F401
2 | from .module import Tools
3 | from .types import User
  |
  = help: Use an explicit re-export: `login as login`

toolboxv2\mods\CloudM\__init__.py:3:20: F401 `.types.User` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .extras import login
2 | from .module import Tools
3 | from .types import User
  |                    ^^^^ F401
4 | from .UI.widget import get_widget
5 | from .UserInstances import UserInstances
  |
  = help: Use an explicit re-export: `User as User`

toolboxv2\mods\CloudM\__init__.py:4:24: F401 `.UI.widget.get_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
2 | from .module import Tools
3 | from .types import User
4 | from .UI.widget import get_widget
  |                        ^^^^^^^^^^ F401
5 | from .UserInstances import UserInstances
  |
  = help: Use an explicit re-export: `get_widget as get_widget`

toolboxv2\mods\CloudM\__init__.py:5:28: F401 `.UserInstances.UserInstances` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
3 | from .types import User
4 | from .UI.widget import get_widget
5 | from .UserInstances import UserInstances
  |                            ^^^^^^^^^^^^^ F401
6 |
7 | tools = Tools
  |
  = help: Use an explicit re-export: `UserInstances as UserInstances`

toolboxv2\mods\CloudM\email_services.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import smtplib
 2 | | from email.mime.multipart import MIMEMultipart
 3 | | from email.mime.text import MIMEText
 4 | | from jinja2 import Environment, BaseLoader
 5 | | import os
 6 | | import uuid
 7 | | import datetime
 8 | | from urllib.parse import quote  # For URL encoding parameters
   | |______________________________^ I001
 9 |
10 |   # Assuming Code is available from your toolboxv2 installation
   |
   = help: Organize imports

toolboxv2\mods\CloudM\email_services.py:25:1: I001 [*] Import block is un-sorted or un-formatted
   |
23 |       print("Warning: toolboxv2.utils.security.cryp.Code not found, using placeholder.")
24 |
25 | / from toolboxv2 import App, Result, get_app, get_logger, MainTool  # MainTool not used directly here
26 | | from toolboxv2.utils.system.types import ApiResult, \
27 | |     ToolBoxInterfaces, ToolBoxError  # ToolBoxError, ToolBoxInterfaces not used directly
   | |___________________________________^ I001
28 |
29 |   # --- Configuration ---
   |
   = help: Organize imports

toolboxv2\mods\CloudM\email_services.py:117:13: S701 By default, jinja2 sets `autoescape` to `False`. Consider using `autoescape=True` or the `select_autoescape` function to mitigate XSS vulnerabilities.
    |
116 | # Jinja2 Environment for inline templates
117 | jinja_env = Environment(loader=BaseLoader())
    |             ^^^^^^^^^^^ S701
118 | base_template_jinja = jinja_env.from_string(BASE_HTML_TEMPLATE)
    |

toolboxv2\mods\CloudM\email_services.py:351:11: F541 [*] f-string without any placeholders
    |
349 |     # or if GMAIL_EMAIL/PASSWORD are not set.
350 |
351 |     print(f"To test, ensure GMAIL_EMAIL, GMAIL_PASSWORD, and SENDER_EMAIL are set.")
    |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F541
352 |     print(f"Using SMTP Server: {SMTP_SERVER}:{SMTP_PORT}")
353 |     print(f"App Name: {APP_NAME}, Base URL: {APP_BASE_URL}")
    |
    = help: Remove extraneous `f` prefix

toolboxv2\mods\CloudM\extras.py:136:9: S605 Starting a process with a shell, possible injection detected
    |
134 |         webbrowser.open(url, new=0, autoraise=True)
135 |     except Exception as e:
136 |         os.system(f"start {url}")
    |         ^^^^^^^^^ S605
137 |         self.logger.error(Style.YELLOW(str(e)))
138 |         self.print(Style.YELLOW(str(e)))
    |

toolboxv2\mods\CloudM\extras.py:145:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
143 | @no_test
144 | def init_git(_):
145 |     os.system("git init")
    |     ^^^^^^^^^ S605
146 |
147 |     os.system(" ".join(
    |

toolboxv2\mods\CloudM\extras.py:147:5: S605 Starting a process with a shell, possible injection detected
    |
145 |     os.system("git init")
146 |
147 |     os.system(" ".join(
    |     ^^^^^^^^^ S605
148 |         ['git', 'remote', 'add', 'origin', 'https://github.com/MarkinHaus/ToolBoxV2.git']))
    |

toolboxv2\mods\CloudM\extras.py:152:5: S605 Starting a process with a shell, possible injection detected
    |
150 |     # Stash any changes
151 |     print("Stashing changes...")
152 |     os.system(" ".join(['git', 'stash']))
    |     ^^^^^^^^^ S605
153 |
154 |     # Pull the latest changes
    |

toolboxv2\mods\CloudM\extras.py:156:5: S605 Starting a process with a shell, possible injection detected
    |
154 |     # Pull the latest changes
155 |     print("Pulling the latest changes...")
156 |     os.system(" ".join(['git', 'pull', 'origin', 'master']))
    |     ^^^^^^^^^ S605
157 |
158 |     # Apply stashed changes
    |

toolboxv2\mods\CloudM\extras.py:160:5: S605 Starting a process with a shell, possible injection detected
    |
158 |     # Apply stashed changes
159 |     print("Applying stashed changes...")
160 |     os.system(" ".join(['git', 'stash', 'pop']))
    |     ^^^^^^^^^ S605
    |

toolboxv2\mods\CloudM\extras.py:200:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
198 | def update_core_pip(self):
199 |     self.print("Init Update.. via pip")
200 |     os.system("pip install --upgrade ToolBoxV2")
    |     ^^^^^^^^^ S605
    |

toolboxv2\mods\CloudM\extras.py:206:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
204 |     self.print("Init Update..")
205 |     if backup:
206 |         os.system("git fetch --all")
    |         ^^^^^^^^^ S605
207 |         d = f"git branch backup-master-{self.st_router.id}-{self.version}-{name}"
208 |         os.system(d)
    |

toolboxv2\mods\CloudM\extras.py:208:9: S605 Starting a process with a shell, possible injection detected
    |
206 |         os.system("git fetch --all")
207 |         d = f"git branch backup-master-{self.st_router.id}-{self.version}-{name}"
208 |         os.system(d)
    |         ^^^^^^^^^ S605
209 |         os.system("git reset --hard origin/master")
210 |     out = os.system("git pull")
    |

toolboxv2\mods\CloudM\extras.py:209:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
207 |         d = f"git branch backup-master-{self.st_router.id}-{self.version}-{name}"
208 |         os.system(d)
209 |         os.system("git reset --hard origin/master")
    |         ^^^^^^^^^ S605
210 |     out = os.system("git pull")
211 |     self.st_router.remove_all_modules()
    |

toolboxv2\mods\CloudM\extras.py:210:11: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
208 |         os.system(d)
209 |         os.system("git reset --hard origin/master")
210 |     out = os.system("git pull")
    |           ^^^^^^^^^ S605
211 |     self.st_router.remove_all_modules()
212 |     try:
    |

toolboxv2\mods\CloudM\extras.py:232:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
230 |         )
231 |         if 'n' not in input("do git commands ? [Y/n]").lower():
232 |             os.system("git stash")
    |             ^^^^^^^^^ S605
233 |             os.system("git pull")
234 |             os.system("git stash pop")
    |

toolboxv2\mods\CloudM\extras.py:233:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
231 |         if 'n' not in input("do git commands ? [Y/n]").lower():
232 |             os.system("git stash")
233 |             os.system("git pull")
    |             ^^^^^^^^^ S605
234 |             os.system("git stash pop")
    |

toolboxv2\mods\CloudM\extras.py:234:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
232 |             os.system("git stash")
233 |             os.system("git pull")
234 |             os.system("git stash pop")
    |             ^^^^^^^^^ S605
235 |
236 |     if out == -1:
    |

toolboxv2\mods\CloudM\extras.py:237:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
236 |     if out == -1:
237 |         os.system("git fetch --all")
    |         ^^^^^^^^^ S605
238 |         os.system("git reset --hard origin/master")
    |

toolboxv2\mods\CloudM\extras.py:238:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
236 |     if out == -1:
237 |         os.system("git fetch --all")
238 |         os.system("git reset --hard origin/master")
    |         ^^^^^^^^^ S605
239 |
240 |     if "-u main" in com and len(com.split("-u main")) > 5:
    |

toolboxv2\mods\CloudM\extras.py:243:9: S605 Starting a process with a shell, possible injection detected
    |
241 |         c = com.replace('-u main ', '')
242 |         print("Restarting... with : " + c)
243 |         os.system(c)
    |         ^^^^^^^^^ S605
244 |     exit(0)
    |

toolboxv2\mods\CloudM\extras.py:303:5: SIM102 Use a single `if` statement instead of nested `if` statements
    |
301 |           return "Pleas connect first to a redis instance"
302 |
303 | /     if not do_root:
304 | |         if 'y' not in input(Style.RED("Ar u sure : the deb will be cleared type y :")):
    | |_______________________________________________________________________________________^ SIM102
305 |               return
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\CloudM\mini.py:46:22: S602 `subprocess` call with `shell=True` identified, security issue
   |
45 |             # Add encoding handling for Windows
46 |             result = subprocess.run(
   |                      ^^^^^^^^^^^^^^ S602
47 |                 command,
48 |                 capture_output=True,
   |

toolboxv2\mods\CloudM\mini.py:75:26: S602 `subprocess` call with `shell=True` identified, security issue
   |
73 |             # Try alternate encoding if cp850 fails
74 |             try:
75 |                 result = subprocess.run(
   |                          ^^^^^^^^^^^^^^ S602
76 |                     command,
77 |                     capture_output=True,
   |

toolboxv2\mods\CloudM\mini.py:100:22: S602 `subprocess` call with `shell=True` identified, security issue
    |
 98 |             command = f'ps -p {pids_str} -o pid='
 99 |
100 |             result = subprocess.run(
    |                      ^^^^^^^^^^^^^^ S602
101 |                 command,
102 |                 capture_output=True,
    |

toolboxv2\mods\DB\__init__.py:1:23: F401 `toolboxv2.get_logger` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from toolboxv2 import get_logger
  |                       ^^^^^^^^^^ F401
2 |
3 | from .tb_adapter import Tools
  |
  = help: Use an explicit re-export: `get_logger as get_logger`

toolboxv2\mods\DB\__init__.py:4:34: F401 `.tb_adapter.Tools` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
3 | from .tb_adapter import Tools
4 | from .tb_adapter import Tools as DB
  |                                  ^^ F401
5 |
6 | Name = "DB"
  |
  = help: Use an explicit re-export: `Tools as Tools`

toolboxv2\mods\DB\local_instance.py:27:29: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
25 |         if os.path.exists(os.path.join(location, 'MiniDictDB.json')):
26 |             try:
27 |                 self.data = eval(Code.decrypt_symmetric(load_from_json(os.path.join(location, 'MiniDictDB.json')).get('data'), key))
   |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
28 |             except Exception as er:
29 |                 print(f"Data is corrupted error : {er}")
   |

toolboxv2\mods\DB\tb_adapter.py:90:25: UP007 Use `X | Y` for type annotations
   |
88 |         self.encoding = 'utf-8'
89 |
90 |         self.data_base: Optional[MiniRedis , MiniDictDB , DB , None] = None
   |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
91 |         self.mode = DatabaseModes.crate(
92 |             os.getenv("DB_MODE_KEY", "LC") if 'test' not in get_app("DB_MODE_KEY").id else os.getenv("DB_MODE_KEY_TEST",
   |
   = help: Convert to `X | Y`

toolboxv2\mods\EventManager\__init__.py:1:27: F401 `.module.Tools` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .module import Name, Tools, version
  |                           ^^^^^ F401
2 |
3 | Name = Name
  |
  = help: Use an explicit re-export: `Tools as Tools`

toolboxv2\mods\EventManager\module.py:375:23: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
373 |             if id_ != "new_con" and 'id' in data:
374 |                 id_data = data.get('id')
375 |                 id_ = eval(id_)
    |                       ^^^^^^^^^ S307
376 |                 c_host, c_pot = id_
377 |                 print(f"Registering: new client {id_data} : {c_host, c_pot}")
    |

toolboxv2\mods\EventManager\module.py:389:19: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
388 |         if isinstance(id_, str):
389 |             id_ = eval(id_)
    |                   ^^^^^^^^^ S307
390 |
391 |         c_name = self.routes.get(id_)
    |

toolboxv2\mods\EventManager\module.py:480:44: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
478 |             await self.add_server_route(self.source_id, ('0.0.0.0', os.getenv("TOOLBOXV2_REMOTE_PORT", 6587)))
479 |
480 |     async def connect_to_remote(self, host=os.getenv("TOOLBOXV2_REMOTE_IP"),
    |                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
481 |                                 port=os.getenv("TOOLBOXV2_REMOTE_PORT", 6587)):
482 |         await self.add_client_route("S0", (host, port))
    |

toolboxv2\mods\EventManager\module.py:481:38: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
480 |     async def connect_to_remote(self, host=os.getenv("TOOLBOXV2_REMOTE_IP"),
481 |                                 port=os.getenv("TOOLBOXV2_REMOTE_PORT", 6587)):
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
482 |         await self.add_client_route("S0", (host, port))
    |

toolboxv2\mods\EventManager\module.py:595:20: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
594 |         if event.source_types.name is SourceTypes.S.name:
595 |             return eval(event.source, __locals={'app': get_app(str(event_id)), 'event': event, 'eventManagerC': self})
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
596 |
597 |     async def routing_function_router(self, event_id: EventID):
    |

toolboxv2\mods\FastApi\__init__.py:1:31: F401 `.manager.Tools` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .manager import VERSION, Tools
  |                               ^^^^^ F401
2 |
3 | Name = "FastApi"
  |
  = help: Use an explicit re-export: `Tools as Tools`

toolboxv2\mods\FastApi\fast_api_install.py:378:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
377 |         except Exception as e:
378 |             raise HTTPException(status_code=500, detail=f"There was an error uploading the file: {str(e)}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
379 |
380 |         finally:
    |

toolboxv2\mods\FastApi\fast_api_main.py:704:11: F811 Redefinition of unused `index` from line 689
    |
703 | @app.get("/tauri")
704 | async def index():
    |           ^^^^^ F811
705 |     return serve_app_func("/web/assets/widgetControllerLogin.html")
    |
    = help: Remove definition: `index`

toolboxv2\mods\FastApi\fast_api_main.py:709:11: F811 Redefinition of unused `index` from line 704
    |
708 | @app.get("/favicon.ico")
709 | async def index():
    |           ^^^^^ F811
710 |     return serve_app_func('/web/favicon.ico')
711 |     # return "Willkommen bei Simple V0 powered by ToolBoxV2-0.0.3"
    |
    = help: Remove definition: `index`

toolboxv2\mods\FastApi\fast_api_main.py:805:11: F811 Redefinition of unused `login_page` from line 800
    |
804 | @app.get("/web/logout")
805 | async def login_page(access_allowed: bool = Depends(lambda: check_access_level(0))):
    |           ^^^^^^^^^^ F811
806 |     return serve_app_func('web/assets/logout.html')
    |
    = help: Remove definition: `login_page`

toolboxv2\mods\FastApi\fast_api_main.py:862:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
860 |               except httpx.RequestError as e:
861 |                   # More specific error handling for network-related issues
862 | /                 raise HTTPException(
863 | |                     status_code=500,
864 | |                     detail=f"Request failed: {str(e)}"
865 | |                 )
    | |_________________^ B904
866 |               except httpx.HTTPStatusError as e:
867 |                   # Handle HTTP error status codes
    |

toolboxv2\mods\FastApi\fast_api_main.py:868:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
866 |               except httpx.HTTPStatusError as e:
867 |                   # Handle HTTP error status codes
868 | /                 raise HTTPException(
869 | |                     status_code=e.response.status_code,
870 | |                     detail=f"HTTP error: {str(e)}"
871 | |                 )
    | |_________________^ B904
872 |
873 |       except Exception as e:
    |

toolboxv2\mods\FastApi\fast_api_main.py:875:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
873 |       except Exception as e:
874 |           # Catch-all error handling with more detailed logging
875 | /         raise HTTPException(
876 | |             status_code=500,
877 | |             detail=f"Unexpected error in webhook forwarding: {str(e)}"
878 | |         )
    | |_________^ B904
879 |
880 |   @app.api_route("/whatsappHook/{port}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
    |

toolboxv2\mods\FastApi\fast_api_main.py:885:11: F811 Redefinition of unused `startup_event` from line 788
    |
884 | @app.on_event("startup")
885 | async def startup_event():
    |           ^^^^^^^^^^^^^ F811
886 |     print('Server started :', __name__, datetime.now())
    |
    = help: Remove definition: `startup_event`

toolboxv2\mods\FastApi\fast_api_main.py:1055:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     |
1054 |             except fastapi.exceptions.FastAPIError as e:
1055 |                 raise SyntaxError(f"fuction '{function_name}' prove the signature error {e}")
     |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
1056 |         if add:
1057 |             app.include_router(router)
     |

toolboxv2\mods\FastApi\fast_lit.py:109:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
107 |                 headers['Authorization'] = f'Bearer {session_token}'
108 |             except jwt.InvalidTokenError:
109 |                 raise ValueError("Invalid session token")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
110 |
111 |         async with httpx.AsyncClient() as client:
    |

toolboxv2\mods\FastApi\fast_nice.py:44:30: SIM115 Use a context manager for opening files
   |
42 |         self.endpoints: list[UIEndpoint] = []
43 |
44 |         self.helper_contex = open("./dist/helper.html", encoding="utf-8").read()
   |                              ^^^^ SIM115
45 |
46 |         self.app.add_middleware(BaseHTTPMiddleware, dispatch=self.middleware_dispatch)
   |

toolboxv2\mods\FastApi\fast_nice.py:140:21: S102 Use of `exec` detected
    |
138 |                     setup_code = code_editor.value
139 |                     setup_namespace = {}
140 |                     exec(setup_code, {'ui': ui}, setup_namespace)
    |                     ^^^^ S102
141 |                     setup_func = setup_namespace['setup_gui']
    |

toolboxv2\mods\FastApi\fast_nice.py:348:13: SIM102 Use a single `if` statement instead of nested `if` statements
    |
346 |                   await self.handle_ws_message(session_id, gui_id, data)
347 |           except WebSocketDisconnect:
348 | /             if session_id in self.ws_connections:
349 | |                 if gui_id in self.ws_connections[session_id]:
    | |_____________________________________________________________^ SIM102
350 |                       del self.ws_connections[session_id][gui_id]
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\FastApi\manager.py:237:13: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
235 |         if not os.path.exists(node_modules_path):
236 |             self.logger.info("Node modules folder not found. Installing dependencies in '%s'", node_modules_path)
237 |             os.system("npm install --prefix ./web ./web")
    |             ^^^^^^^^^ S605
238 |
239 |         # Build the uvicorn command.
    |

toolboxv2\mods\FastApi\manager.py:340:21: S605 Starting a process with a shell, possible injection detected
    |
338 |             try:
339 |                 if system() == "Windows":
340 |                     os.system(f"taskkill /pid {api_pid} /F")
    |                     ^^^^^^^^^ S605
341 |                 else:
342 |                     os.kill(api_pid, signal.SIGKILL)
    |

toolboxv2\mods\FileWidget.py:73:13: S112 `try`-`except`-`continue` detected, consider logging the exception
   |
71 |                   elif disposition.get('name') == 'totalChunks':
72 |                       total_chunks = int(content.strip(b'\r\n'))
73 | /             except:
74 | |                 continue
   | |________________________^ S112
75 |
76 |           return ChunkInfo(
   |

toolboxv2\mods\FileWidget.py:660:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
658 |         folder_list = []
659 |         for blob_id in blob_ids:
660 |             blob_data = pickle.loads(storage.read_blob(blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
661 |             folder_list.extend(flatten_dict(blob_data, blob_id, '/').keys())
    |

toolboxv2\mods\MinimalHtml.py:117:17: B007 Loop control variable `i` not used within loop body
    |
115 |             template = string.Template(template_content)
116 |             html_element = '<h1> invalid Template </h1>'
117 |             for i in range(len(template_content)):
    |                 ^ B007
118 |                 try:
119 |                     html_element = template.substitute(**element['kwargs'])
    |
    = help: Rename unused `i` to `_i`

toolboxv2\mods\POA\__init__.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | from .module import MOD_NAME, VERSION
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
2 | version = VERSION
3 | Name = MOD_NAME
  |
  = help: Organize imports

toolboxv2\mods\POA\module.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / import json
2 | | import time
3 | | import uuid  # Für eindeutige IDs
4 | | from typing import Dict, Optional, List, Any
5 | |
6 | | from toolboxv2 import get_app, App, RequestData, Result  # Annahme: RequestData und Result sind korrekt importiert
  | |_______________________________________________________^ I001
7 |
8 |   # Moduldefinition
  |
  = help: Organize imports

toolboxv2\mods\POA\module.py:4:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
2 | import time
3 | import uuid  # Für eindeutige IDs
4 | from typing import Dict, Optional, List, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
5 |
6 | from toolboxv2 import get_app, App, RequestData, Result  # Annahme: RequestData und Result sind korrekt importiert
  |

toolboxv2\mods\POA\module.py:4:1: UP035 `typing.List` is deprecated, use `list` instead
  |
2 | import time
3 | import uuid  # Für eindeutige IDs
4 | from typing import Dict, Optional, List, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
5 |
6 | from toolboxv2 import get_app, App, RequestData, Result  # Annahme: RequestData und Result sind korrekt importiert
  |

toolboxv2\mods\POA\module.py:17:35: UP007 [*] Use `X | Y` for type annotations
   |
15 | # In einer echten Anwendung würde man den Benutzernamen dynamisch aus der Session/RequestData holen
16 | # Für SPP1 verwenden wir einen festen Benutzernamen oder einen, der per Query-Parameter kommt.
17 | def get_current_username(request: Optional[RequestData] = None) -> str:
   |                                   ^^^^^^^^^^^^^^^^^^^^^ UP007
18 |     if request:
19 |         return request.session.user_name
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:23:75: UP006 [*] Use `list` instead of `List` for type annotation
   |
23 | async def get_user_data(app: App, username: str, data_key_prefix: str) -> List[Dict[str, Any]]:
   |                                                                           ^^^^ UP006
24 |     db = app.get_mod("DB")  # Standard DB-Instanz
25 |     # db.edit_cli("RR") # Sicherstellen, dass der Read-Replica-Modus aktiv ist, falls nötig
   |
   = help: Replace with `list`

toolboxv2\mods\POA\module.py:23:80: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
23 | async def get_user_data(app: App, username: str, data_key_prefix: str) -> List[Dict[str, Any]]:
   |                                                                                ^^^^ UP006
24 |     db = app.get_mod("DB")  # Standard DB-Instanz
25 |     # db.edit_cli("RR") # Sicherstellen, dass der Read-Replica-Modus aktiv ist, falls nötig
   |
   = help: Replace with `dict`

toolboxv2\mods\POA\module.py:46:79: UP006 [*] Use `list` instead of `List` for type annotation
   |
46 | async def save_user_data(app: App, username: str, data_key_prefix: str, data: List[Dict[str, Any]]):
   |                                                                               ^^^^ UP006
47 |     db = app.get_mod("DB")
48 |     # db.edit_cli("RR") # oder einen spezifischen Schreibmodus, falls konfiguriert
   |
   = help: Replace with `list`

toolboxv2\mods\POA\module.py:46:84: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
46 | async def save_user_data(app: App, username: str, data_key_prefix: str, data: List[Dict[str, Any]]):
   |                                                                                    ^^^^ UP006
47 |     db = app.get_mod("DB")
48 |     # db.edit_cli("RR") # oder einen spezifischen Schreibmodus, falls konfiguriert
   |
   = help: Replace with `dict`

toolboxv2\mods\POA\module.py:60:40: UP007 [*] Use `X | Y` for type annotations
   |
58 | # --- API Endpunkte für Aufgaben ---
59 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
60 | async def get_tasks(app: App, request: Optional[RequestData] = None):
   |                                        ^^^^^^^^^^^^^^^^^^^^^ UP007
61 |     username = get_current_username(request)
62 |     tasks = await get_user_data(app, username, "poa_tasks")
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:67:45: UP007 [*] Use `X | Y` for type annotations
   |
66 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
67 | async def add_task(app: App, request, data: Optional[Dict[str, str]] = None, **kwargs):
   |                                             ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
68 |     username = get_current_username(request)
69 |     if not isinstance(data, dict):
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:67:54: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
66 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
67 | async def add_task(app: App, request, data: Optional[Dict[str, str]] = None, **kwargs):
   |                                                      ^^^^ UP006
68 |     username = get_current_username(request)
69 |     if not isinstance(data, dict):
   |
   = help: Replace with `dict`

toolboxv2\mods\POA\module.py:90:32: UP007 [*] Use `X | Y` for type annotations
   |
88 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['PUT'])
89 | async def toggle_task(app: App,
90 |                       request: Optional[RequestData] = None, **kwargs):  # Pfad könnte /api/POA/toggle_task?task_id=... sein
   |                                ^^^^^^^^^^^^^^^^^^^^^ UP007
91 |     username = get_current_username(request)
92 |     task_id = request.query_params.get("task_id") if request and request.query_params else None
   |
   = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:114:32: UP007 [*] Use `X | Y` for type annotations
    |
112 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['DELETE'])
113 | async def delete_task(app: App,
114 |                       request: Optional[RequestData] = None, **kwargs):  # Pfad könnte /api/POA/delete_task?task_id=... sein
    |                                ^^^^^^^^^^^^^^^^^^^^^ UP007
115 |     username = get_current_username(request)
116 |     task_id = request.query_params.get("task_id") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:133:40: UP007 [*] Use `X | Y` for type annotations
    |
131 | # --- API Endpunkte für Notizen (ähnlich wie Aufgaben) ---
132 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['GET'])
133 | async def get_notes(app: App, request: Optional[RequestData] = None, **kwargs):
    |                                        ^^^^^^^^^^^^^^^^^^^^^ UP007
134 |     username = get_current_username(request)
135 |     notes = await get_user_data(app, username, "poa_notes")
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:140:39: UP007 [*] Use `X | Y` for type annotations
    |
139 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['POST'])
140 | async def add_note(app: App, request: Optional[RequestData] = None,data=None, **kwargs):
    |                                       ^^^^^^^^^^^^^^^^^^^^^ UP007
141 |     username = get_current_username(request)
142 |     if data and not request.body:
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:167:42: UP007 [*] Use `X | Y` for type annotations
    |
166 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['PUT'])
167 | async def update_note(app: App, request: Optional[RequestData] = None,data=None, **kwargs):
    |                                          ^^^^^^^^^^^^^^^^^^^^^ UP007
168 |     username = get_current_username(request)
169 |     note_id = request.query_params.get("note_id") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:195:42: UP007 [*] Use `X | Y` for type annotations
    |
194 | @export(mod_name=MOD_NAME, api=True, version=VERSION, request_as_kwarg=True, api_methods=['DELETE'])
195 | async def delete_note(app: App, request: Optional[RequestData] = None, **kwargs):
    |                                          ^^^^^^^^^^^^^^^^^^^^^ UP007
196 |     username = get_current_username(request)
197 |     note_id = request.query_params.get("note_id") if request and request.query_params else None
    |
    = help: Convert to `X | Y`

toolboxv2\mods\POA\module.py:214:43: UP007 [*] Use `X | Y` for type annotations
    |
212 | # --- Hauptseite ---
213 | @export(mod_name=MOD_NAME, api=True, version=VERSION, name="main", api_methods=['GET'])  # Zugriff über /api/POA/
214 | async def get_poa_page(app: App, request: Optional[RequestData] = None):
    |                                           ^^^^^^^^^^^^^^^^^^^^^ UP007
215 |     # Diese Funktion liefert das Haupt-HTML für die POA-Anwendung
216 |     # Das HTML wird im nächsten Abschnitt definiert.
    |
    = help: Convert to `X | Y`

toolboxv2\mods\ProcessManager.py:20:23: S602 `subprocess` call with `shell=True` identified, security issue
   |
18 |         self.processes = []
19 |         for command in commands:
20 |             process = subprocess.Popen(command, shell=True)
   |                       ^^^^^^^^^^^^^^^^ S602
21 |             self.processes.append(process)
   |

toolboxv2\mods\SchedulerManager.py:174:17: S102 Use of `exec` detected
    |
172 |             with open(func) as file:
173 |                 func_code = file.read()
174 |                 exec(func_code)
    |                 ^^^^ S102
175 |                 func = locals()[object_name]
176 |         elif isinstance(func, str) and func.endswith('.dill') and safety_mode == 'open':
    |

toolboxv2\mods\SchedulerManager.py:185:17: S102 Use of `exec` detected
    |
183 |             local_vars = {'app': get_app(from_=Name + f".pasing.{object_name}")}
184 |             try:
185 |                 exec(func.strip(), {}, local_vars)
    |                 ^^^^ S102
186 |             except Exception as e:
187 |                 return Result.default_internal_error(f"Function parsing failed withe {e}")
    |

toolboxv2\mods\SocketManager.py:105:20: S113 Probable use of `requests` call without timeout
    |
103 | def get_public_ip():
104 |     try:
105 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^ S113
106 |         ip_address = response.json()['ip']
107 |         return ip_address
    |

toolboxv2\mods\SocketManager.py:251:20: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
249 |         self.logger.debug(f"Starting:{name} client on port {port} with host {host}")
250 |         sock = socket.socket(socket_type, socket.SOCK_STREAM)
251 |         time.sleep(random.choice(range(1, 100)) // 100)
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
252 |         if unix_file:
253 |             connection_error = sock.connect_ex(host)
    |

toolboxv2\mods\SocketManager.py:278:20: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
276 |         self.logger.debug(f"Starting:{name} client on port {port} with host {host}")
277 |
278 |         time.sleep(random.choice(range(1, 100)) // 100)
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
279 |         try:
280 |             if unix_file:
    |

toolboxv2\mods\SocketManager.py:438:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
436 |               f" max:{self.sockets[name]['max_connections']} connect on :{endpoint}")
437 |           self.sockets[name]["client_sockets_dict"][endpoint[0] + str(endpoint[1])] = client_socket
438 | /         if self.sockets[name]['max_connections'] != -1:
439 | |             if self.sockets[name]["connections"] >= self.sockets[name]['max_connections']:
    | |__________________________________________________________________________________________^ SIM102
440 |                   self.sockets[name]["running_dict"]["server_receiver"].set()
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\SocketManager.py:587:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
585 |                 except Exception as e:
586 |                     return Result.custom_error(data=str(e), data_info="Connection down and closed")
587 |         except ConnectionResetError and ConnectionAbortedError as e:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
588 |             self.print(f"Closing Receiver {name}:{identifier} {str(e)}")
589 |             self.sockets[name]["running_dict"]["receive"][identifier].set()
    |

toolboxv2\mods\SocketManager.py:612:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
610 |             self.logger.info(f"{name} -- received JSON -- {msg['identifier']}")
611 |             return msg
612 |         except json.JSONDecodeError and UnicodeDecodeError as e:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
613 |             self.logger.error(f"JSON decode error: {e}")
    |

toolboxv2\mods\TestWidget\__init__.py:1:25: F401 `.wiget_test.get_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .wiget_test import get_widget
  |                         ^^^^^^^^^^ F401
2 |
3 | Name = 'TestWidget'
  |
  = help: Use an explicit re-export: `get_widget as get_widget`

toolboxv2\mods\TestWidget\wiget_test.py:16:72: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
16 | def load_widget(app, display_name="Cud be ur name", cv=0, WidgetID=str(uuid.uuid4())[:4]):
   |                                                                        ^^^^^^^^^^^^ B008
17 |     # vars : $providerurl $WidgetID $root $$username
18 |     app.run_any(TBEF.MINIMALHTML.ADD_GROUP, command=Name)
   |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:23:28: F811 Redefinition of unused `TextSplitter` from line 16
   |
21 | from .research_processor import ResearchProcessor
22 | from .sources.arxiv_source import Paper, search_papers
23 | from .text_splitter import TextSplitter
   |                            ^^^^^^^^^^^^ F811
   |
   = help: Remove definition: `TextSplitter`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:26:7: F811 Redefinition of unused `RobustPDFDownloader` from line 18
   |
26 | class RobustPDFDownloader:
   |       ^^^^^^^^^^^^^^^^^^^ F811
27 |     def __init__(self, max_retries=5, backoff_factor=0.3,
28 |                  download_dir='downloads',
   |
   = help: Remove definition: `RobustPDFDownloader`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:61:24: F821 Undefined name `HTTPAdapter`
   |
59 |             backoff_factor=backoff_factor
60 |         )
61 |         self.adapter = HTTPAdapter(max_retries=self.retry_strategy)
   |                        ^^^^^^^^^^^ F821
62 |
63 |         # Create session with retry mechanism
   |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:64:24: F821 Undefined name `requests`
   |
63 |         # Create session with retry mechanism
64 |         self.session = requests.Session()
   |                        ^^^^^^^^ F821
65 |         self.session.mount("https://", self.adapter)
66 |         self.session.mount("http://", self.adapter)
   |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:99:16: F821 Undefined name `requests`
    |
 97 |             return file_path
 98 |
 99 |         except requests.exceptions.RequestException as e:
    |                ^^^^^^^^ F821
100 |             self.logger.error(f"Download failed for {url}: {e}")
101 |             raise
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:116:26: F821 Undefined name `PyPDF2`
    |
114 |             page_texts = []
115 |             with open(pdf_path, 'rb') as file:
116 |                 reader = PyPDF2.PdfReader(file)
    |                          ^^^^^^ F821
117 |                 for page_num, page in enumerate(reader.pages, 1):
118 |                     text = page.extract_text()
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:143:26: F821 Undefined name `PyPDF2`
    |
141 |         try:
142 |             with open(pdf_path, 'rb') as file:
143 |                 reader = PyPDF2.PdfReader(file)
    |                          ^^^^^^ F821
144 |
145 |                 for page_num, page in enumerate(reader.pages, 1):
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:149:35: F821 Undefined name `Image`
    |
147 |                         for img_index, image in enumerate(page.images):
148 |                             img_data = image.data
149 |                             img = Image.open(io.BytesIO(img_data))
    |                                   ^^^^^ F821
150 |
151 |                             img_filename = f'page_{page_num}_img_{img_index}.png'
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:149:46: F821 Undefined name `io`
    |
147 |                         for img_index, image in enumerate(page.images):
148 |                             img_data = image.data
149 |                             img = Image.open(io.BytesIO(img_data))
    |                                              ^^ F821
150 |
151 |                             img_filename = f'page_{page_num}_img_{img_index}.png'
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:178:7: F811 Redefinition of unused `Paper` from line 22
    |
176 |     relevance_score: float = 0.0
177 |
178 | class Paper(BaseModel):
    |       ^^^^^ F811
179 |     title: str
180 |     summary: str
    |
    = help: Remove definition: `Paper`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:190:5: F811 Redefinition of unused `search_papers` from line 22
    |
188 |     key_sections: list[str] = Field(default_factory=list)
189 |
190 | def search_papers(query: str, max_results=10) -> list[Paper]:
    |     ^^^^^^^^^^^^^ F811
191 |     search = arxiv.Search(
192 |         query=query,
    |
    = help: Remove definition: `search_papers`

toolboxv2\mods\TruthSeeker\arXivCrawler.py:191:14: F821 Undefined name `arxiv`
    |
190 | def search_papers(query: str, max_results=10) -> list[Paper]:
191 |     search = arxiv.Search(
    |              ^^^^^ F821
192 |         query=query,
193 |         max_results=max_results,
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:194:17: F821 Undefined name `arxiv`
    |
192 |         query=query,
193 |         max_results=max_results,
194 |         sort_by=arxiv.SortCriterion.Relevance
    |                 ^^^^^ F821
195 |     )
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:198:19: F821 Undefined name `arxiv`
    |
197 |     results = []
198 |     for result in arxiv.Client().results(search):
    |                   ^^^^^ F821
199 |         paper = Paper(
200 |             title=result.title,
    |

toolboxv2\mods\TruthSeeker\arXivCrawler.py:453:10: F821 Undefined name `Spinner`
    |
451 | async def main(query: str = "Beste strategien in bretspielen sitler von katar"):
452 |     """Main execution function"""
453 |     with Spinner("Init Isaa"):
    |          ^^^^^^^ F821
454 |         tools = get_app("ArXivPDFProcessor", name=None).get_mod("isaa")
455 |         tools.init_isaa(build=True)
    |

toolboxv2\mods\TruthSeeker\module.py:17:11: SIM115 Use a context manager for opening files
   |
16 | dot = os.path.dirname(os.path.abspath(__file__))
17 | content = open(os.path.join(dot,"template.html"), encoding="utf-8").read()
   |           ^^^^ SIM115
18 | abut_content = open(os.path.join(dot,"abut.html"), encoding="utf-8").read()
   |

toolboxv2\mods\TruthSeeker\module.py:18:16: SIM115 Use a context manager for opening files
   |
16 | dot = os.path.dirname(os.path.abspath(__file__))
17 | content = open(os.path.join(dot,"template.html"), encoding="utf-8").read()
18 | abut_content = open(os.path.join(dot,"abut.html"), encoding="utf-8").read()
   |                ^^^^ SIM115
19 |
20 | code_templates = {
   |

toolboxv2\mods\TruthSeeker\module.py:227:5: SIM102 Use a single `if` statement instead of nested `if` statements
    |
225 |       if abut:
226 |           return HTMLResponse(content=abut_content)
227 | /     if hasattr(request, 'row'):
228 | |         if sid := request.row.query_params.get('session_id'):
    | |_____________________________________________________________^ SIM102
229 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
230 |       if hasattr(request, 'query_params'):
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\TruthSeeker\module.py:230:5: SIM102 Use a single `if` statement instead of nested `if` statements
    |
228 |           if sid := request.row.query_params.get('session_id'):
229 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
230 | /     if hasattr(request, 'query_params'):
231 | |         if sid := request.query_params.get('session_id'):
    | |_________________________________________________________^ SIM102
232 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
233 |       return RedirectResponse(url="/gui/open-Seeker")
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\TruthSeeker\one.py:131:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
129 |             import torchaudio
130 |         except ImportError:
131 |             raise ValueError("Couldn't load audio install torchaudio'")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
132 |         # Handle different audio input types
133 |         if isinstance(audio_data, str):
    |

toolboxv2\mods\TruthSeeker\one.py:306:21: S112 `try`-`except`-`continue` detected, consider logging the exception
    |
304 |                           if chunk_embedding:
305 |                               chunk_embeddings.append(chunk_embedding)
306 | /                     except Exception:
307 | |                         continue
    | |________________________________^ S112
308 |                       try:
309 |                           input_data = InputData(chunk[len(chunk)//2:], modality)
    |

toolboxv2\mods\TruthSeeker\one.py:315:29: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
313 |                     except Exception:
314 |                         if len(chunk_embeddings) == 0 and chunks.index(chunk) == len(chunks)-1:
315 |                             raise e
    |                             ^^^^^^^ B904
    |

toolboxv2\mods\TruthSeeker\tests.py:124:9: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
123 |           # Mock PDF download and processing
124 | /         with patch.object(RobustPDFDownloader, 'download_pdf') as mock_download:
125 | |             with patch.object(RobustPDFDownloader, 'extract_text_from_pdf') as mock_extract:
    | |____________________________________________________________________________________________^ SIM117
126 |                   mock_download.return_value = "test.pdf"
127 |                   mock_extract.return_value = [{'page_number': 1, 'text': 'test content'}]
    |
    = help: Combine `with` statements

toolboxv2\mods\TruthSeeker\tests.py:171:9: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
169 |       def test_process_empty_results(self):
170 |           # Test processing with no results
171 | /         with patch.object(ArXivPDFProcessor, 'generate_queries') as mock_generate:
172 | |             with patch.object(ArXivPDFProcessor, 'search_and_process_papers') as mock_search:
    | |_____________________________________________________________________________________________^ SIM117
173 |                   mock_generate.return_value = ["test query"]
174 |                   mock_search.return_value = []
    |
    = help: Combine `with` statements

toolboxv2\mods\TruthSeeker\tests.py:194:9: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
192 |           mock_search.return_value.results.return_value = [mock_result]
193 |
194 | /         with patch.object(RobustPDFDownloader, 'download_pdf') as mock_download:
195 | |             with patch.object(RobustPDFDownloader, 'extract_text_from_pdf') as mock_extract:
    | |____________________________________________________________________________________________^ SIM117
196 |                   with patch.object(self.mock_tools, 'format_class') as mock_format_class:
197 |                       mock_download.return_value = "test.pdf"
    |
    = help: Combine `with` statements

toolboxv2\mods\WebSocketManager.py:317:49: S105 Possible hardcoded password assigned to: "token"
    |
315 |                         return '{"res": "No User Instance Found"}'
316 |
317 |                     if data['data']['token'] == "**SelfAuth**":
    |                                                 ^^^^^^^^^^^^^^ S105
318 |                         data['data']['token'] = user_instance['token']
    |

toolboxv2\mods\WebSocketManager.py:392:49: S105 Possible hardcoded password assigned to: "token"
    |
390 |                         return '{"res": "No User Instance Found pleas log in"}'
391 |
392 |                     if data['data']['token'] == "**SelfAuth**":
    |                                                 ^^^^^^^^^^^^^^ S105
393 |                         data['data']['token'] = user_instance['token']
    |

toolboxv2\mods\WebSocketManager.py:432:21: SIM102 Use a single `if` statement instead of nested `if` statements
    |
430 |                           res = "Mod Error " + str(e)
431 |
432 | /                     if type(res) == str:
433 | |                         if (res.startswith('{') or res.startswith('[')) or res.startswith('"[') or res.startswith('"{') \
434 | |                             or res.startswith('\"[') or res.startswith('\"{') or res.startswith(
435 | |                             'b"[') or res.startswith('b"{'): \
    | |____________________________________________________________^ SIM102
436 |                               res = eval(res)
437 |                       if not isinstance(res, dict):
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\WebSocketManager.py:436:35: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
434 |                             or res.startswith('\"[') or res.startswith('\"{') or res.startswith(
435 |                             'b"[') or res.startswith('b"{'): \
436 |                             res = eval(res)
    |                                   ^^^^^^^^^ S307
437 |                     if not isinstance(res, dict):
438 |                         res = {"res": res, data['name']: True}
    |

toolboxv2\mods\WhatsAppTb\client.py:145:22: F821 Undefined name `Agent`
    |
143 |     whc: WhClient
144 |     isaa: 'Tools'
145 |     agent: Optional['Agent'] = None
    |                      ^^^^^ F821
146 |     credentials: Credentials | None = None
147 |     state: AssistantState = AssistantState.OFFLINE
    |

toolboxv2\mods\WhatsAppTb\client.py:1407:21: SIM115 Use a context manager for opening files
     |
1405 |                 filename = f"file_{file_type}_{datetime.now().isoformat()}_{content.get('sha256', '')}"
1406 |                 blob_id = self.blob_docs_system.save_document(
1407 |                     open(media_data, 'rb').read(),
     |                     ^^^^ SIM115
1408 |                     filename=filename,
1409 |                     file_type=file_type
     |

toolboxv2\mods\WhatsAppTb\server.py:54:9: S110 `try`-`except`-`pass` detected, consider logging the exception
   |
52 |                   signal.signal(signal.SIGINT, self.signal_handler)
53 |                   signal.signal(signal.SIGTERM, self.signal_handler)
54 | /         except Exception:
55 | |             pass
   | |________________^ S110
56 |
57 |       def offline(self, instance_id):
   |

toolboxv2\mods\WhatsAppTb\server.py:211:22: F821 Undefined name `ui`
    |
209 | …     """Interactive instance control card"""
210 | …     config = self.instances[instance_id]
211 | …     with ui.card().classes('w-full p-4 mb-4 bg-gray-50 dark:bg-gray-800').style("background-color: var(--background-color) !importa…
    |            ^^ F821
212 | …         # Header Section
213 | …         with ui.row().classes('w-full justify-between items-center'):
    |

toolboxv2\mods\WhatsAppTb\server.py:213:26: F821 Undefined name `ui`
    |
211 | …     with ui.card().classes('w-full p-4 mb-4 bg-gray-50 dark:bg-gray-800').style("background-color: var(--background-color) !importa…
212 | …         # Header Section
213 | …         with ui.row().classes('w-full justify-between items-center'):
    |                ^^ F821
214 | …             ui.label(f'📱 {instance_id}').classes('text-xl font-bold')
    |

toolboxv2\mods\WhatsAppTb\server.py:214:25: F821 Undefined name `ui`
    |
212 |                     # Header Section
213 |                     with ui.row().classes('w-full justify-between items-center'):
214 |                         ui.label(f'📱 {instance_id}').classes('text-xl font-bold')
    |                         ^^ F821
215 |
216 |                         # Status Indicator
    |

toolboxv2\mods\WhatsAppTb\server.py:217:25: F821 Undefined name `ui`
    |
216 |                         # Status Indicator
217 |                         ui.label().bind_text_from(
    |                         ^^ F821
218 |                             self.threads, instance_id,
219 |                             lambda x: 'Running' if x and x.is_alive() else 'Stopped'
    |

toolboxv2\mods\WhatsAppTb\server.py:223:26: F821 Undefined name `ui`
    |
222 |                     # Configuration Display
223 |                     with ui.grid(columns=2).classes('w-full mt-4 gap-2'):
    |                          ^^ F821
224 |
225 |                         ui.label('port:').classes('font-bold')
    |

toolboxv2\mods\WhatsAppTb\server.py:225:25: F821 Undefined name `ui`
    |
223 |                     with ui.grid(columns=2).classes('w-full mt-4 gap-2'):
224 |
225 |                         ui.label('port:').classes('font-bold')
    |                         ^^ F821
226 |                         ui.label(config['port'])
    |

toolboxv2\mods\WhatsAppTb\server.py:226:25: F821 Undefined name `ui`
    |
225 |                         ui.label('port:').classes('font-bold')
226 |                         ui.label(config['port'])
    |                         ^^ F821
227 |
228 |                         ui.label('Last Activity:').classes('font-bold')
    |

toolboxv2\mods\WhatsAppTb\server.py:228:25: F821 Undefined name `ui`
    |
226 |                         ui.label(config['port'])
227 |
228 |                         ui.label('Last Activity:').classes('font-bold')
    |                         ^^ F821
229 |                         ui.label().bind_text_from(
230 |                             self.last_messages, instance_id,
    |

toolboxv2\mods\WhatsAppTb\server.py:229:25: F821 Undefined name `ui`
    |
228 |                         ui.label('Last Activity:').classes('font-bold')
229 |                         ui.label().bind_text_from(
    |                         ^^ F821
230 |                             self.last_messages, instance_id,
231 |                             lambda x: x.strftime("%Y-%m-%d %H:%M:%S") if x else 'Never'
    |

toolboxv2\mods\WhatsAppTb\server.py:235:26: F821 Undefined name `ui`
    |
234 |                     # Action Controls
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
    |                          ^^ F821
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
237 |                             ui.tooltip('Configure')
    |

toolboxv2\mods\WhatsAppTb\server.py:236:30: F821 Undefined name `ui`
    |
234 |                     # Action Controls
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
    |                              ^^ F821
237 |                             ui.tooltip('Configure')
    |

toolboxv2\mods\WhatsAppTb\server.py:237:29: F821 Undefined name `ui`
    |
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
237 |                             ui.tooltip('Configure')
    |                             ^^ F821
238 |
239 |                         with ui.button(icon='refresh', color='orange',
    |

toolboxv2\mods\WhatsAppTb\server.py:239:30: F821 Undefined name `ui`
    |
237 |                             ui.tooltip('Configure')
238 |
239 |                         with ui.button(icon='refresh', color='orange',
    |                              ^^ F821
240 |                                        on_click=lambda: self.restart_instance(instance_id)):
241 |                             ui.tooltip('Restart')
    |

toolboxv2\mods\WhatsAppTb\server.py:241:29: F821 Undefined name `ui`
    |
239 |                         with ui.button(icon='refresh', color='orange',
240 |                                        on_click=lambda: self.restart_instance(instance_id)):
241 |                             ui.tooltip('Restart')
    |                             ^^ F821
242 |
243 |                         with ui.button(icon='stop', color='red',
    |

toolboxv2\mods\WhatsAppTb\server.py:243:30: F821 Undefined name `ui`
    |
241 |                             ui.tooltip('Restart')
242 |
243 |                         with ui.button(icon='stop', color='red',
    |                              ^^ F821
244 |                                        on_click=lambda: self.stop_instance(instance_id)):
245 |                             ui.tooltip('Stop')
    |

toolboxv2\mods\WhatsAppTb\server.py:245:29: F821 Undefined name `ui`
    |
243 |                         with ui.button(icon='stop', color='red',
244 |                                        on_click=lambda: self.stop_instance(instance_id)):
245 |                             ui.tooltip('Stop')
    |                             ^^ F821
246 |
247 |                     # Edit Configuration Dialog
    |

toolboxv2\mods\WhatsAppTb\server.py:248:26: F821 Undefined name `ui`
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
    |                          ^^ F821
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

toolboxv2\mods\WhatsAppTb\server.py:248:54: F821 Undefined name `ui`
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
    |                                                      ^^ F821
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

toolboxv2\mods\WhatsAppTb\server.py:249:35: F821 Undefined name `ui`
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
    |                                   ^^ F821
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

toolboxv2\mods\WhatsAppTb\server.py:250:38: F821 Undefined name `ui`
    |
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |                                      ^^ F821
251 |
252 |                         with ui.row().classes('w-full justify-end'):
    |

toolboxv2\mods\WhatsAppTb\server.py:252:30: F821 Undefined name `ui`
    |
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
251 |
252 |                         with ui.row().classes('w-full justify-end'):
    |                              ^^ F821
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
254 |                             ui.button('Save', color='primary', on_click=lambda: (
    |

toolboxv2\mods\WhatsAppTb\server.py:253:29: F821 Undefined name `ui`
    |
252 |                         with ui.row().classes('w-full justify-end'):
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
    |                             ^^ F821
254 |                             ui.button('Save', color='primary', on_click=lambda: (
255 |                                 self.update_instance_config(
    |

toolboxv2\mods\WhatsAppTb\server.py:254:29: F821 Undefined name `ui`
    |
252 |                         with ui.row().classes('w-full justify-end'):
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
254 |                             ui.button('Save', color='primary', on_click=lambda: (
    |                             ^^ F821
255 |                                 self.update_instance_config(
256 |                                     instance_id,
    |

toolboxv2\mods\WhatsAppTb\server.py:264:18: F821 Undefined name `ui`
    |
263 |             # Main UI Layout
264 |             with ui.column().classes('w-full max-w-4xl mx-auto p-4'):
    |                  ^^ F821
265 |                 ui.label('WhatsApp Instance Manager').classes('text-2xl font-bold mb-6')
    |

toolboxv2\mods\WhatsAppTb\server.py:265:17: F821 Undefined name `ui`
    |
263 |             # Main UI Layout
264 |             with ui.column().classes('w-full max-w-4xl mx-auto p-4'):
265 |                 ui.label('WhatsApp Instance Manager').classes('text-2xl font-bold mb-6')
    |                 ^^ F821
266 |
267 |                 # Add Instance Section
    |

toolboxv2\mods\WhatsAppTb\server.py:268:17: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
    |
267 |                   # Add Instance Section
268 | /                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 | |                     with ui.card().classes('w-full p-4 mt-2'):
    | |______________________________________________________________^ SIM117
270 |                           instance_id = ui.input('Instance ID').classes('w-full')
271 |                           token = ui.input('API Token').classes('w-full')
    |
    = help: Combine `with` statements

toolboxv2\mods\WhatsAppTb\server.py:268:22: F821 Undefined name `ui`
    |
267 |                 # Add Instance Section
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
    |                      ^^ F821
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:269:26: F821 Undefined name `ui`
    |
267 |                 # Add Instance Section
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 |                     with ui.card().classes('w-full p-4 mt-2'):
    |                          ^^ F821
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:270:39: F821 Undefined name `ui`
    |
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
    |                                       ^^ F821
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:271:33: F821 Undefined name `ui`
    |
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
    |                                 ^^ F821
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:272:37: F821 Undefined name `ui`
    |
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
    |                                     ^^ F821
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |

toolboxv2\mods\WhatsAppTb\server.py:273:40: F821 Undefined name `ui`
    |
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |                                        ^^ F821
274 |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
    |

toolboxv2\mods\WhatsAppTb\server.py:275:30: F821 Undefined name `ui`
    |
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
274 |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
    |                              ^^ F821
276 |                             ui.button('Clear', on_click=lambda: (
277 |                                 instance_id.set_value(''),
    |

toolboxv2\mods\WhatsAppTb\server.py:276:29: F821 Undefined name `ui`
    |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
276 |                             ui.button('Clear', on_click=lambda: (
    |                             ^^ F821
277 |                                 instance_id.set_value(''),
278 |                                 token.set_value(''),
    |

toolboxv2\mods\WhatsAppTb\server.py:282:29: F821 Undefined name `ui`
    |
280 | …                         phone_number.set_value('')
281 | …                     ))
282 | …                     ui.button('Create', color='positive', on_click=lambda: (
    |                       ^^ F821
283 | …                         self.add_update_instance(
284 | …                             instance_id.value,
    |

toolboxv2\mods\WhatsAppTb\server.py:293:39: F821 Undefined name `ui`
    |
292 |                 # Instances Display
293 |                 instances_container = ui.column().classes('w-full')
    |                                       ^^ F821
294 |                 with instances_container:
295 |                     for instance_id in self.instances:
    |

toolboxv2\mods\WidgetsProvider\__init__.py:1:42: F401 `.board_widget.BoardWidget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .board_widget import BoardWidget as Tools
  |                                          ^^^^^ F401
2 | from .module import open_widget
3 | from .StorageUtil import get_names
  |
  = help: Use an explicit re-export: `BoardWidget as BoardWidget`

toolboxv2\mods\WidgetsProvider\__init__.py:2:21: F401 `.module.open_widget` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .board_widget import BoardWidget as Tools
2 | from .module import open_widget
  |                     ^^^^^^^^^^^ F401
3 | from .StorageUtil import get_names
  |
  = help: Use an explicit re-export: `open_widget as open_widget`

toolboxv2\mods\WidgetsProvider\__init__.py:3:26: F401 `.StorageUtil.get_names` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | from .board_widget import BoardWidget as Tools
2 | from .module import open_widget
3 | from .StorageUtil import get_names
  |                          ^^^^^^^^^ F401
4 |
5 | Name = 'WidgetsProvider'
  |
  = help: Use an explicit re-export: `get_names as get_names`

toolboxv2\mods\__init__.py:1:8: F401 `toolboxv2` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | import toolboxv2
  |        ^^^^^^^^^ F401
2 | from toolboxv2.utils.toolbox import get_app
  |
  = help: Use an explicit re-export: `toolboxv2 as toolboxv2`

toolboxv2\mods\__init__.py:2:37: F401 `toolboxv2.utils.toolbox.get_app` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
  |
1 | import toolboxv2
2 | from toolboxv2.utils.toolbox import get_app
  |                                     ^^^^^^^ F401
3 |
4 | BROWSER = 'firefox'  # 'chrome'
  |
  = help: Use an explicit re-export: `get_app as get_app`

toolboxv2\mods\cli_functions.py:16:8: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
14 |     READCHAR = True
15 |     READCHAR_error = None
16 | except ImportError and ModuleNotFoundError:
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
17 |     READCHAR = False
   |

toolboxv2\mods\cli_functions.py:35:8: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
33 |     PROMPT_TOOLKIT = True
34 |     PROMPT_TOOLKIT_error = None
35 | except ImportError and ModuleNotFoundError:
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
36 |     PROMPT_TOOLKIT = False
   |

toolboxv2\mods\cli_functions.py:200:9: SIM113 Use `enumerate()` for index variable `i` in `for` loop
    |
198 |             else:
199 |                 as_list[i] = replacements[key]
200 |         i += 1
    |         ^^^^^^ SIM113
201 |     if not inlist:
202 |         return text
    |

toolboxv2\mods\cli_functions.py:384:28: B008 Do not perform function call `node` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
382 |                password=False,
383 |                bindings=None,
384 |                message=f"~{node()}@>", fh=None) -> CallingObject:
    |                            ^^^^^^ B008
385 |     if app is None:
386 |         app = get_app(from_="cliF.user_input")
    |

toolboxv2\mods\cli_functions.py:422:13: S605 Starting a process with a shell, possible injection detected
    |
420 |                 return
421 |             fh.append_string(buff)
422 |             os.system(buff)
    |             ^^^^^^^^^ S605
423 |
424 |         run_in_terminal(run_in_console)
    |

toolboxv2\mods\cli_functions.py:428:9: F811 Redefinition of unused `run_in_shell` from line 414
    |
427 |     @bindings.add('c-up')
428 |     def run_in_shell(event):
    |         ^^^^^^^^^^^^ F811
429 |         buff = event.st_router.current_buffer.text
    |
    = help: Remove definition: `run_in_shell`

toolboxv2\mods\cli_functions.py:440:26: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
439 |             try:
440 |                 result = eval(buff, app.globals['root'], app.locals['user'])
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
441 |                 if result is not None:
442 |                     print(f"+ {buff}\n#{app.locals['user']['counts']}>", result)
    |

toolboxv2\mods\cli_functions.py:446:17: S102 Use of `exec` detected
    |
444 |                     print(f"- {buff}\n#{app.locals['user']['counts']}>")
445 |             except SyntaxError:
446 |                 exec(buff, app.globals['root'], app.locals['user'])
    |                 ^^^^ S102
447 |                 print(f"* {buff}\n#{app.locals['user']['counts']}> Statement executed")
448 |             except Exception as e:
    |

toolboxv2\mods\cli_functions.py:469:21: S605 Starting a process with a shell, possible injection detected
    |
467 |                     print("Avalabel functions:", completer_dict[user_input_buffer_info[0]])
468 |                 else:
469 |                     os.system(f"{user_input_buffer_info[0]} --help")
    |                     ^^^^^^^^^ S605
470 |             if len(user_input_buffer_info) > 1:
471 |                 if user_input_buffer_info[0] in completer_dict:
    |

toolboxv2\mods\cli_functions.py:489:9: SIM115 Use a context manager for opening files
    |
488 |     if not os.path.exists(f'{app.data_dir}/{app.args_sto.modi}-cli.txt'):
489 |         open(f'{app.data_dir}/{app.args_sto.modi}-cli.txt', "a")
    |         ^^^^ SIM115
490 |
491 |     session = PromptSession(message=message,
    |

toolboxv2\mods\isaa\CodingAgent\coder.py:246:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
245 |     except FileNotFoundError as e:
246 |         raise FileNotFoundError(f"pytest not found. Please install pytest and pytest-json-report : {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
247 |     except Exception as e:
248 |         raise Exception(f"Error running tests: {str(e)}")
    |

toolboxv2\mods\isaa\CodingAgent\coder.py:248:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
246 |         raise FileNotFoundError(f"pytest not found. Please install pytest and pytest-json-report : {str(e)}")
247 |     except Exception as e:
248 |         raise Exception(f"Error running tests: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

toolboxv2\mods\isaa\CodingAgent\live.py:795:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
793 |                 subprocess.run([sys.executable, "-m", "venv", str(self._venv_path)], check=True)
794 |             except subprocess.CalledProcessError as e:
795 |                 raise RuntimeError(f"Failed to create virtual environment: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
796 |
797 |     def _virtual_open(self, filepath, mode='r', *args, **kwargs):
    |

toolboxv2\mods\isaa\CodingAgent\live.py:806:21: SIM115 Use a context manager for opening files
    |
805 |         # Use actual filesystem but track in virtual fs
806 |         real_file = open(abs_path, mode, *args, **kwargs)
    |                     ^^^^ SIM115
807 |
808 |         if 'r' in mode:
    |

toolboxv2\mods\isaa\CodingAgent\live.py:981:33: S102 Use of `exec` detected
    |
979 | …                     try:
980 | …                         # Execute wrapped code and await it
981 | …                         exec(exec_code, self.user_ns)
    |                           ^^^^ S102
982 | …                         result = self.user_ns['__wrapper']()
983 | …                         if asyncio.iscoroutine(result):
    |

toolboxv2\mods\isaa\CodingAgent\live.py:989:29: S102 Use of `exec` detected
    |
987 |                         elif is_async:
988 |                             # Execute async code
989 |                             exec(exec_code, self.user_ns)
    |                             ^^^^ S102
990 |                             if eval_code:
991 |                                 result = eval(eval_code, self.user_ns)
    |

toolboxv2\mods\isaa\CodingAgent\live.py:991:42: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
989 | …                     exec(exec_code, self.user_ns)
990 | …                     if eval_code:
991 | …                         result = eval(eval_code, self.user_ns)
    |                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
992 | …                         if asyncio.iscoroutine(result):
993 | …                             result = await result
    |

toolboxv2\mods\isaa\CodingAgent\live.py:996:29: S102 Use of `exec` detected
    |
994 |                         else:
995 |                             # Execute sync code
996 |                             exec(exec_code, self.user_ns)
    |                             ^^^^ S102
997 |                             if eval_code:
998 |                                 result = eval(eval_code, self.user_ns)
    |

toolboxv2\mods\isaa\CodingAgent\live.py:998:42: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
 996 |                             exec(exec_code, self.user_ns)
 997 |                             if eval_code:
 998 |                                 result = eval(eval_code, self.user_ns)
     |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
 999 |
1000 |                         if result is not None:
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1042:21: B012 `return` inside `finally` blocks cause exceptions to be silenced
     |
1040 |                         self.vfs.delete_file(temp_file)
1041 |
1042 |                     return result
     |                     ^^^^^^^^^^^^^ B012
1043 |
1044 |         except Exception as e:
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1129:21: S102 Use of `exec` detected
     |
1127 |                     # Create a new function object from the code
1128 |                     method_locals = {}
1129 |                     exec(
     |                     ^^^^ S102
1130 |                         f"def _temp_func{signature(getattr(base_obj.__class__, attr_name, None))}: {method_ast.body[0].value.s}",
1131 |                         globals(), method_locals)
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1139:50: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1137 |                 else:
1138 |                     # For simple attributes
1139 |                     setattr(base_obj, attr_name, eval(code, self.user_ns))
     |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^ S307
1140 |                     result_message.append(f"Updated attribute '{clean_object_name}' in memory")
1141 |             else:
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1145:33: B005 Using `.strip()` with multi-character strings is misleading
     |
1143 |                 if code.startswith('"""') and code.endswith('"""'):
1144 |                     # Handle function definitions
1145 |                     func_code = code.strip('"""')
     |                                 ^^^^^^^^^^^^^^^^^ B005
1146 |                     func_ast = ast.parse(func_code).body[0]
1147 |                     func_name = func_ast.name
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1151:21: S102 Use of `exec` detected
     |
1149 |                     # Create a new function object from the code
1150 |                     func_locals = {}
1151 |                     exec(f"{func_code}", globals(), func_locals)
     |                     ^^^^ S102
1152 |                     self.user_ns[clean_object_name] = func_locals[func_name]
1153 |                     result_message.append(f"Updated function '{clean_object_name}' in memory")
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1156:55: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1154 |                 else:
1155 |                     # Simple variable assignment
1156 |                     self.user_ns[clean_object_name] = eval(code, self.user_ns)
     |                                                       ^^^^^^^^^^^^^^^^^^^^^^^^ S307
1157 |                     result_message.append(f"Updated variable '{clean_object_name}' in memory")
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1179:39: B005 Using `.strip()` with multi-character strings is misleading
     |
1178 |                     if code.startswith('"""') and code.endswith('"""'):
1179 |                         method_code = code.strip('"""')
     |                                       ^^^^^^^^^^^^^^^^^ B005
1180 |
1181 |                         # Use ast to parse the file and find the method to replace
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1205:37: B005 Using `.strip()` with multi-character strings is misleading
     |
1203 |                     if code.startswith('"""') and code.endswith('"""'):
1204 |                         # Handle function updates
1205 |                         func_code = code.strip('"""')
     |                                     ^^^^^^^^^^^^^^^^^ B005
1206 |                         func_pattern = fr"def {clean_object_name}.*?:(.*?)(?=\n\w|\Z)"
1207 |                         func_match = re.search(func_pattern, original_content, re.DOTALL)
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1281:32: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1279 |         if session_file.exists():
1280 |             with open(session_file, 'rb') as f:
1281 |                 session_data = pickle.load(f)
     |                                ^^^^^^^^^^^^^^ S301
1282 |                 # self.user_ns.update(session_data['user_ns'])
1283 |                 self.output_history.update(session_data['output_history'])
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1707:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     |
1705 |             if self.browser:
1706 |                 await self.browser.close()
1707 |             raise Exception(f"Failed to initialize browser: {str(e)}")
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
1708 |
1709 |     async def create_agent(self, task: str, initial_actions=None):
     |

toolboxv2\mods\isaa\CodingAgent\live.py:1746:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     |
1744 |             return page
1745 |         except Exception as e:
1746 |             raise Exception(f"Failed to navigate to {url}: {str(e)}")
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
1747 |
1748 |     async def get_tabs(self):
     |

toolboxv2\mods\isaa\CodingAgent\live.py:2409:17: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
2407 |                                   args.append(f"{name}={param.default}")
2408 |                           desc_parts.append(f"**Init Args:** `{', '.join(args)}`")
2409 | /                 except:
2410 | |                     pass
     | |________________________^ S110
2411 |
2412 |                   # Instance state
     |

toolboxv2\mods\isaa\CodingAgent\runner.py:125:27: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
123 |         print(result_list)
124 |         try:
125 |             result_list = eval(result)
    |                           ^^^^^^^^^^^^ S307
126 |         except ValueError:
127 |             return "Expected format cond not parse agent interpretation"
    |

toolboxv2\mods\isaa\SearchAgentCluster\entry.py:62:17: SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
   |
60 |           async with self.semaphore:
61 |               try:
62 | /                 async with aiohttp.ClientSession() as session:
63 | |                     async with session.get(url) as response:
   | |____________________________________________________________^ SIM117
64 |                           content = await response.text()
65 |                           self.cache.content[url] = content
   |
   = help: Combine `with` statements

toolboxv2\mods\isaa\SearchAgentCluster\search_tool.py:91:36: B008 Do not perform function call `WebScraperConfig` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
89 |     def __init__(
90 |         self,
91 |         config: WebScraperConfig = WebScraperConfig(),
   |                                    ^^^^^^^^^^^^^^^^^^ B008
92 |         llm: str | BaseChatModel | None = None,
93 |         chrome_path: str | None = None,
   |

toolboxv2\mods\isaa\base\AgentUtils.py:77:16: S113 Probable use of `requests` call without timeout
   |
76 | def get_ip():
77 |     response = requests.get('https://api64.ipify.org?format=json').json()
   |                ^^^^^^^^^^^^ S113
78 |     return response["ip"]
   |

toolboxv2\mods\isaa\base\AgentUtils.py:84:16: S113 Probable use of `requests` call without timeout
   |
82 | def get_location():
83 |     ip_address = get_ip()
84 |     response = requests.get(f'https://ipapi.co/{ip_address}/json/').json()
   |                ^^^^^^^^^^^^ S113
85 |     location_data = f"city: {response.get('city')},region: {response.get('region')},country: {response.get('country_name')},"
   |

toolboxv2\mods\isaa\base\AgentUtils.py:141:12: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
139 |         process = get_location()
140 |         info['location'], info['ip'] = process.result()
141 |     except TimeoutError and Exception:
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
142 |         info['location'] = "Berlin Schöneberg"
143 |     initialize_system_infos(info)
    |

toolboxv2\mods\isaa\base\AgentUtils.py:188:32: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
186 |                 data = f.read()
187 |             if data:
188 |                 self.scripts = pickle.loads(data)
    |                                ^^^^^^^^^^^^^^^^^^ S301
189 |         else:
190 |             os.makedirs(self.filename, exist_ok=True)
    |

toolboxv2\mods\isaa\base\AgentUtils.py:252:13: SIM108 Use ternary operator `node_info = node_dict if index == 'question' else node_dict[index]` instead of `if`-`else`-block
    |
251 |               index = list(node_dict.keys())[0]  # Get the node's index.
252 | /             if index == 'question':
253 | |                 node_info = node_dict
254 | |             else:
255 | |                 node_info = node_dict[index]  # Get the node's info.
    | |____________________________________________^ SIM108
256 |               return IsaaQuestionNode(
257 |                   node_info['question'],
    |
    = help: Replace `if`-`else`-block with `node_info = node_dict if index == 'question' else node_dict[index]`

toolboxv2\mods\isaa\base\AgentUtils.py:761:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
759 |                 texts = [text.replace('\\t', '').replace('\t', '')]
760 |             except Exception as e:
761 |                 raise ValueError(f"File processing failed: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
762 |         elif isinstance(data, str):
763 |             texts = [data.replace('\\t', '').replace('\t', '')]
    |

toolboxv2\mods\isaa\base\AgentUtils.py:779:13: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
777 |             import traceback
778 |             print(traceback.format_exc())
779 |             raise RuntimeError(f"Data addition failed: {str(e)}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
780 |
781 |     def get(self, names):
    |

toolboxv2\mods\isaa\base\AgentUtils.py:1070:13: S102 Use of `exec` detected
     |
1068 |     def eval_code(self, code):
1069 |         try:
1070 |             exec(code, self.global_env, self.local_env)
     |             ^^^^ S102
1071 |             result = eval(code, self.global_env, self.local_env)
1072 |             return self.format_output(result)
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1071:22: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1069 |         try:
1070 |             exec(code, self.global_env, self.local_env)
1071 |             result = eval(code, self.global_env, self.local_env)
     |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S307
1072 |             return self.format_output(result)
1073 |         except Exception as e:
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1433:24: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1431 |             # If the parsed value is a string, it might be a JSON string, so we try to parse it again
1432 |             if isinstance(parsed_value, str):
1433 |                 return eval(parsed_value)
     |                        ^^^^^^^^^^^^^^^^^^ S307
1434 |             else:
1435 |                 return parsed_value
     |

toolboxv2\mods\isaa\base\AgentUtils.py:1652:24: S307 Use of possibly insecure function; consider using `ast.literal_eval`
     |
1650 |     json_objects = find_json_objects_in_str(data)
1651 |     if not json_objects and data.startswith('[') and data.endswith(']'):
1652 |         json_objects = eval(data)
     |                        ^^^^^^^^^^ S307
1653 |     if json_objects and len(json_objects) > 0 and isinstance(json_objects[0], dict):
1654 |         result.extend([{**expected_keys, **ob} for ob in json_objects])
     |

toolboxv2\mods\isaa\base\Agent\agent.py:66:45: F401 `google.adk.tools.agent_tool.AgentTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
64 |     )
65 |     from google.adk.tools import google_search as adk_google_search
66 |     from google.adk.tools.agent_tool import AgentTool
   |                                             ^^^^^^^^^ F401
67 |     from google.adk.tools.mcp_tool.mcp_toolset import (
68 |         MCPToolset,
   |
   = help: Remove unused import: `google.adk.tools.agent_tool.AgentTool`

toolboxv2\mods\isaa\base\Agent\agent.py:75:45: F401 `google.genai.types.FunctionCall` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
73 |     # ADK Artifacts (Optional, for advanced use cases)
74 |     # from google.adk.artifacts import ArtifactService, InMemoryArtifactService
75 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                             ^^^^^^^^^^^^ F401
76 |
77 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:75:59: F401 `google.genai.types.FunctionResponse` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
73 |     # ADK Artifacts (Optional, for advanced use cases)
74 |     # from google.adk.artifacts import ArtifactService, InMemoryArtifactService
75 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                                           ^^^^^^^^^^^^^^^^ F401
76 |
77 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:171:12: F401 `mcp.server.stdio` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
169 | # If using ADK's MCPToolset, explicit MCP imports might not be needed here.
170 | try:
171 |     import mcp.server.stdio
    |            ^^^^^^^^^^^^^^^^ F401
172 |     import mcp.types as mcp_types  # For building MCP servers
173 |     from google.adk.tools.mcp_tool.conversion_utils import (
    |
    = help: Remove unused import: `mcp.server.stdio`

toolboxv2\mods\isaa\base\Agent\agent.py:179:37: F401 `mcp.server.lowlevel.NotificationOptions` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
177 |     from mcp.client.sse import sse_client as mcp_sse_client
178 |     from mcp.server.fastmcp import FastMCP
179 |     from mcp.server.lowlevel import NotificationOptions
    |                                     ^^^^^^^^^^^^^^^^^^^ F401
180 |     from mcp.server.lowlevel import Server as MCPServerBase
181 |     from mcp.server.models import InitializationOptions
    |
    = help: Remove unused import: `mcp.server.lowlevel.NotificationOptions`

toolboxv2\mods\isaa\base\Agent\agent.py:181:35: F401 `mcp.server.models.InitializationOptions` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
179 |     from mcp.server.lowlevel import NotificationOptions
180 |     from mcp.server.lowlevel import Server as MCPServerBase
181 |     from mcp.server.models import InitializationOptions
    |                                   ^^^^^^^^^^^^^^^^^^^^^ F401
182 |
183 |     MCP_AVAILABLE = True
    |
    = help: Remove unused import: `mcp.server.models.InitializationOptions`

toolboxv2\mods\isaa\base\Agent\agent.py:213:12: F401 `litellm` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
211 | # LiteLLM
212 | try:
213 |     import litellm
    |            ^^^^^^^ F401
214 |     from litellm import BudgetManager, acompletion, completion_cost, token_counter
215 |     from litellm.exceptions import (
    |
    = help: Remove unused import: `litellm`

toolboxv2\mods\isaa\base\Agent\agent.py:242:41: F401 `opentelemetry.sdk.trace.TracerProvider` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
240 | try:
241 |     from opentelemetry import trace
242 |     from opentelemetry.sdk.trace import TracerProvider
    |                                         ^^^^^^^^^^^^^^ F401
243 |     from opentelemetry.sdk.trace.export import (  # Example exporter
244 |         BatchSpanProcessor,
    |
    = help: Remove unused import: `opentelemetry.sdk.trace.TracerProvider`

toolboxv2\mods\isaa\base\Agent\agent.py:244:9: F401 `opentelemetry.sdk.trace.export.BatchSpanProcessor` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
242 |     from opentelemetry.sdk.trace import TracerProvider
243 |     from opentelemetry.sdk.trace.export import (  # Example exporter
244 |         BatchSpanProcessor,
    |         ^^^^^^^^^^^^^^^^^^ F401
245 |         ConsoleSpanExporter,
246 |     )
    |
    = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:245:9: F401 `opentelemetry.sdk.trace.export.ConsoleSpanExporter` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
243 |     from opentelemetry.sdk.trace.export import (  # Example exporter
244 |         BatchSpanProcessor,
245 |         ConsoleSpanExporter,
    |         ^^^^^^^^^^^^^^^^^^^ F401
246 |     )
247 |     # Add other exporters (OTLP, Jaeger, etc.) as needed
    |
    = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\agent.py:432:21: S102 Use of `exec` detected
    |
430 |             try:
431 |                 with contextlib.redirect_stdout(stdout_capture), contextlib.redirect_stderr(stderr_capture):
432 |                     exec(code_execution_input.code, globals(), local_vars) # <<< UNSAFE!
    |                     ^^^^ S102
433 |                 stdout = stdout_capture.getvalue().strip()
434 |                 stderr = stderr_capture.getvalue().strip()
    |

toolboxv2\mods\isaa\base\Agent\agent.py:629:37: B023 Function definition does not bind loop variable `adk_tool`
    |
627 |                          # This simple version calls the tool's underlying function if possible.
628 |                          # WARNING: This bypasses ADK's standard tool execution flow.
629 |                          if hasattr(adk_tool, 'func') and callable(adk_tool.func):
    |                                     ^^^^^^^^ B023
630 |                              # This assumes the function doesn't need ToolContext
631 |                              result = await adk_tool.func(**kwargs)
    |

toolboxv2\mods\isaa\base\Agent\agent.py:629:68: B023 Function definition does not bind loop variable `adk_tool`
    |
627 |                          # This simple version calls the tool's underlying function if possible.
628 |                          # WARNING: This bypasses ADK's standard tool execution flow.
629 |                          if hasattr(adk_tool, 'func') and callable(adk_tool.func):
    |                                                                    ^^^^^^^^ B023
630 |                              # This assumes the function doesn't need ToolContext
631 |                              result = await adk_tool.func(**kwargs)
    |

toolboxv2\mods\isaa\base\Agent\agent.py:631:45: B023 Function definition does not bind loop variable `adk_tool`
    |
629 |                          if hasattr(adk_tool, 'func') and callable(adk_tool.func):
630 |                              # This assumes the function doesn't need ToolContext
631 |                              result = await adk_tool.func(**kwargs)
    |                                             ^^^^^^^^ B023
632 |                              # Convert result to MCP content (e.g., TextContent)
633 |                              if isinstance(result, str):
    |

toolboxv2\mods\isaa\base\Agent\agent.py:1162:13: SIM102 Use a single `if` statement instead of nested `if` statements
     |
1160 |                        return ProcessingStrategy.ADK_RUN, "Input mentions specific ADK tool or requests tool use."
1161 |               # General ADK case: If ADK is primary mode and input isn't trivial
1162 | /             if len(user_input.split()) > 5: # Simple heuristic for non-trivial input
1163 | |                 # If ADK tools exist, assume ADK might be needed for planning
1164 | |                 if has_adk_tools:
     | |_________________________________^ SIM102
1165 |                       return ProcessingStrategy.ADK_RUN, "Complex input and ADK tools available, using ADK planning."
1166 |                   # If only basic LLM agent, still might use ADK runner for session mgmt? Check config.
     |
     = help: Combine `if` statements using `and`

toolboxv2\mods\isaa\base\Agent\agent.py:1495:33: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
1493 | …                  if err_content:
1494 | …                      error_message = err_content.get('text') if isinstance(err_content, dict) else getattr(err_content, 'text', 'U…
1495 | …             except: pass # Ignore parsing errors here
     |               ^^^^^^^^^^^^ S110
1496 | …         return f"Error: A2A task failed on {target_url}: {error_message or final_text}"
1497 | …     elif current_state == TaskState.CANCELLED:
     |

toolboxv2\mods\isaa\base\Agent\builder.py:26:6: I001 Import block is un-sorted or un-formatted
   |
25 | # Framework Imports & Availability Checks (mirrored from agent.py)
26 | try: from google.adk.agents import LlmAgent; ADK_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
27 | except ImportError: LlmAgent = object; ADK_AVAILABLE_BLD = False # Need LlmAgent for isinstance check
28 | try: from google.adk.tools import BaseTool, FunctionTool, AgentTool; from google.adk.tools.mcp_tool import MCPToolset, StdioServerPara…
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:28:6: I001 Import block is un-sorted or un-formatted
   |
26 | try: from google.adk.agents import LlmAgent; ADK_AVAILABLE_BLD = True
27 | except ImportError: LlmAgent = object; ADK_AVAILABLE_BLD = False # Need LlmAgent for isinstance check
28 | try: from google.adk.tools import BaseTool, FunctionTool, AgentTool; from google.adk.tools.mcp_tool import MCPToolset, StdioServerParameters, SseServerParams; from google.adk.runners import Runner, InMemoryRunner, AsyncWebRunner; from google.adk.sessions import SessionService, InMemorySessionService; from google.adk.code_executors import BaseCodeExecutor as ADKBaseCodeExecutor; from google.adk.planners import BasePlanner; from google.adk.examples import Example
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
29 | except ImportError: BaseTool = object; FunctionTool = object; AgentTool = object; MCPToolset = object; Runner = object; InMemoryRunner = object; AsyncWebRunner = object; SessionService = object; InMemorySessionService = object; ADKBaseCodeExecutor = object; BasePlanner = object; Example = object; StdioServerParameters = object; SseServerParams = object
30 | try: from python_a2a.server import A2AServer; from python_a2a.models import AgentCard; A2A_AVAILABLE_BLD = True
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:30:6: I001 Import block is un-sorted or un-formatted
   |
28 | try: from google.adk.tools import BaseTool, FunctionTool, AgentTool; from google.adk.tools.mcp_tool import MCPToolset, StdioServerPara…
29 | except ImportError: BaseTool = object; FunctionTool = object; AgentTool = object; MCPToolset = object; Runner = object; InMemoryRunner…
30 | try: from python_a2a.server import A2AServer; from python_a2a.models import AgentCard; A2A_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
31 | except ImportError: A2AServer = object; AgentCard = object; A2A_AVAILABLE_BLD = False
32 | try: from mcp.server.fastmcp import FastMCP; MCP_AVAILABLE_BLD = True
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:32:6: I001 Import block is un-sorted or un-formatted
   |
30 | try: from python_a2a.server import A2AServer; from python_a2a.models import AgentCard; A2A_AVAILABLE_BLD = True
31 | except ImportError: A2AServer = object; AgentCard = object; A2A_AVAILABLE_BLD = False
32 | try: from mcp.server.fastmcp import FastMCP; MCP_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
33 | except ImportError: FastMCP = object; MCP_AVAILABLE_BLD = False
34 | try: from litellm import BudgetManager; LITELLM_AVAILABLE_BLD = True
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:34:6: I001 Import block is un-sorted or un-formatted
   |
32 | try: from mcp.server.fastmcp import FastMCP; MCP_AVAILABLE_BLD = True
33 | except ImportError: FastMCP = object; MCP_AVAILABLE_BLD = False
34 | try: from litellm import BudgetManager; LITELLM_AVAILABLE_BLD = True
   |      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ I001
35 | except ImportError: BudgetManager = object; LITELLM_AVAILABLE_BLD = False
   |
   = help: Organize imports

toolboxv2\mods\isaa\base\Agent\builder.py:40:35: F401 `google.adk.agents.BaseAgent` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
38 | # Google ADK
39 | try:
40 |     from google.adk.agents import BaseAgent, LlmAgent
   |                                   ^^^^^^^^^ F401
41 |     from google.adk.agents.callback_context import CallbackContext
42 |     from google.adk.agents.invocation_context import InvocationContext
   |
   = help: Remove unused import: `google.adk.agents.BaseAgent`

toolboxv2\mods\isaa\base\Agent\builder.py:41:52: F401 `google.adk.agents.callback_context.CallbackContext` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
39 | try:
40 |     from google.adk.agents import BaseAgent, LlmAgent
41 |     from google.adk.agents.callback_context import CallbackContext
   |                                                    ^^^^^^^^^^^^^^^ F401
42 |     from google.adk.agents.invocation_context import InvocationContext
43 |     from google.adk.code_executors import BaseCodeExecutor
   |
   = help: Remove unused import: `google.adk.agents.callback_context.CallbackContext`

toolboxv2\mods\isaa\base\Agent\builder.py:42:54: F401 `google.adk.agents.invocation_context.InvocationContext` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
40 |     from google.adk.agents import BaseAgent, LlmAgent
41 |     from google.adk.agents.callback_context import CallbackContext
42 |     from google.adk.agents.invocation_context import InvocationContext
   |                                                      ^^^^^^^^^^^^^^^^^ F401
43 |     from google.adk.code_executors import BaseCodeExecutor
44 |     from google.adk.code_executors.code_execution_utils import (
   |
   = help: Remove unused import: `google.adk.agents.invocation_context.InvocationContext`

toolboxv2\mods\isaa\base\Agent\builder.py:45:9: F401 `google.adk.code_executors.code_execution_utils.CodeExecutionInput` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
43 |     from google.adk.code_executors import BaseCodeExecutor
44 |     from google.adk.code_executors.code_execution_utils import (
45 |         CodeExecutionInput,
   |         ^^^^^^^^^^^^^^^^^^ F401
46 |         CodeExecutionResult,
47 |     )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:46:9: F401 `google.adk.code_executors.code_execution_utils.CodeExecutionResult` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
44 |     from google.adk.code_executors.code_execution_utils import (
45 |         CodeExecutionInput,
46 |         CodeExecutionResult,
   |         ^^^^^^^^^^^^^^^^^^^ F401
47 |     )
48 |     from google.adk.events import Event
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:48:35: F401 `google.adk.events.Event` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
46 |         CodeExecutionResult,
47 |     )
48 |     from google.adk.events import Event
   |                                   ^^^^^ F401
49 |     from google.adk.examples import Example  # For few-shot
50 |     from google.adk.models import BaseLlm, Gemini
   |
   = help: Remove unused import: `google.adk.events.Event`

toolboxv2\mods\isaa\base\Agent\builder.py:50:35: F401 `google.adk.models.BaseLlm` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
48 |     from google.adk.events import Event
49 |     from google.adk.examples import Example  # For few-shot
50 |     from google.adk.models import BaseLlm, Gemini
   |                                   ^^^^^^^ F401
51 |     from google.adk.models.lite_llm import LiteLlm  # ADK Wrapper for LiteLLM
52 |     from google.adk.planners import BasePlanner
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:50:44: F401 `google.adk.models.Gemini` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
48 |     from google.adk.events import Event
49 |     from google.adk.examples import Example  # For few-shot
50 |     from google.adk.models import BaseLlm, Gemini
   |                                            ^^^^^^ F401
51 |     from google.adk.models.lite_llm import LiteLlm  # ADK Wrapper for LiteLLM
52 |     from google.adk.planners import BasePlanner
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:58:37: F401 `google.adk.sessions.Session` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
56 |         Runner,
57 |     )
58 |     from google.adk.sessions import Session, State
   |                                     ^^^^^^^ F401
59 |     from google.adk.tools import (
60 |         BaseTool,
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:58:46: F401 `google.adk.sessions.State` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
56 |         Runner,
57 |     )
58 |     from google.adk.sessions import Session, State
   |                                              ^^^^^ F401
59 |     from google.adk.tools import (
60 |         BaseTool,
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:62:9: F401 `google.adk.tools.LongRunningFunctionTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
60 |         BaseTool,
61 |         FunctionTool,
62 |         LongRunningFunctionTool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^ F401
63 |         ToolContext,
64 |     )
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:63:9: F401 `google.adk.tools.ToolContext` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
61 |         FunctionTool,
62 |         LongRunningFunctionTool,
63 |         ToolContext,
   |         ^^^^^^^^^^^ F401
64 |     )
65 |     from google.adk.tools import VertexAiSearchTool as AdkVertexAiSearchTool
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:65:56: F401 `google.adk.tools.VertexAiSearchTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
63 |         ToolContext,
64 |     )
65 |     from google.adk.tools import VertexAiSearchTool as AdkVertexAiSearchTool
   |                                                        ^^^^^^^^^^^^^^^^^^^^^ F401
66 |     from google.adk.tools import (
67 |         built_in_code_execution as adk_built_in_code_execution,  # Secure option
   |
   = help: Remove unused import: `google.adk.tools.VertexAiSearchTool`

toolboxv2\mods\isaa\base\Agent\builder.py:69:51: F401 `google.adk.tools.google_search` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
67 |         built_in_code_execution as adk_built_in_code_execution,  # Secure option
68 |     )
69 |     from google.adk.tools import google_search as adk_google_search
   |                                                   ^^^^^^^^^^^^^^^^^ F401
70 |     from google.adk.tools.agent_tool import AgentTool
71 |     from google.adk.tools.mcp_tool.mcp_toolset import (
   |
   = help: Remove unused import: `google.adk.tools.google_search`

toolboxv2\mods\isaa\base\Agent\builder.py:70:45: F401 `google.adk.tools.agent_tool.AgentTool` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
68 |     )
69 |     from google.adk.tools import google_search as adk_google_search
70 |     from google.adk.tools.agent_tool import AgentTool
   |                                             ^^^^^^^^^ F401
71 |     from google.adk.tools.mcp_tool.mcp_toolset import (
72 |         MCPToolset,
   |
   = help: Remove unused import: `google.adk.tools.agent_tool.AgentTool`

toolboxv2\mods\isaa\base\Agent\builder.py:76:36: F401 `google.genai.types.Content` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
74 |         StdioServerParameters,
75 |     )
76 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                    ^^^^^^^ F401
77 |
78 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:76:45: F401 `google.genai.types.FunctionCall` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
74 |         StdioServerParameters,
75 |     )
76 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                             ^^^^^^^^^^^^ F401
77 |
78 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:76:59: F401 `google.genai.types.FunctionResponse` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
74 |         StdioServerParameters,
75 |     )
76 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                                           ^^^^^^^^^^^^^^^^ F401
77 |
78 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:76:77: F401 `google.genai.types.Part` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
74 |         StdioServerParameters,
75 |     )
76 |     from google.genai.types import Content, FunctionCall, FunctionResponse, Part
   |                                                                             ^^^^ F401
77 |
78 |     ADK_AVAILABLE = True
   |
   = help: Remove unused import

toolboxv2\mods\isaa\base\Agent\builder.py:129:12: F401 `litellm` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
127 | # LiteLLM
128 | try:
129 |     import litellm
    |            ^^^^^^^ F401
130 |     from litellm import BudgetManager
131 |     from litellm.utils import get_max_tokens
    |
    = help: Remove unused import: `litellm`

toolboxv2\mods\isaa\base\Agent\executors.py:128:13: S102 Use of `exec` detected
    |
126 |             # Execute the compiled code
127 |             # Note: restrictedpython does not inherently support robust timeouts during exec
128 |             exec(byte_code, exec_globals, local_vars)
    |             ^^^^ S102
129 |
130 |             # Check execution time again
    |

toolboxv2\mods\isaa\base\Agent\executors.py:334:32: F821 Undefined name `AgentConfig`
    |
333 | # --- Factory function ---
334 | def get_code_executor(config: 'AgentConfig') -> RestrictedPythonExecutor | DockerCodeExecutor | BaseCodeExecutor | None:
    |                                ^^^^^^^^^^^ F821
335 |     """Creates a code executor instance based on configuration."""
336 |     executor_type = config.code_executor_type
    |

toolboxv2\mods\isaa\base\Agents.py:688:17: B021 f-string used as docstring. Python will interpret this as a joined string, rather than a docstring.
    |
686 | …with_functions is None:
687 | … class WithFunctions(BaseModel):
688 | …     f"""Deside if u need to call one function to perform this task {[f.name for f in self.functions] if self.functions is not None else ''}"""
    |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B021
689 | …     use_function: bool = field(default=False)
    |

toolboxv2\mods\isaa\base\Agents.py:1976:24: S113 Probable use of `requests` call without timeout
     |
1975 |         def encode_image_url(image_url, img_type):
1976 |             response = requests.get(image_url)
     |                        ^^^^^^^^^^^^ S113
1977 |             file_data = response.content
1978 |             return {
     |

toolboxv2\mods\isaa\base\KnowledgeBase.py:260:17: SIM115 Use a context manager for opening files
    |
258 |         print(f"Graph saved to {output_file} Open in browser to view.", len(nx_graph))
259 |         if get_output:
260 |             c = open(output_file, encoding="utf-8").read()
    |                 ^^^^ SIM115
261 |             os.remove(output_file)
262 |             return c
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:614:89: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
612 | class KnowledgeBase:
613 |     def __init__(self, embedding_dim: int = 768, similarity_threshold: float = 0.61, batch_size: int = 64,
614 |                  n_clusters: int = 4, deduplication_threshold: float = 0.85, model_name=os.getenv("DEFAULTMODELSUMMERY"),
    |                                                                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
615 |                  embedding_model=os.getenv("DEFAULTMODELEMBEDDING"),
616 |                  vis_class:str | None = "EnhancedVectorStore",
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:615:34: B008 Do not perform function call `os.getenv` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
613 |     def __init__(self, embedding_dim: int = 768, similarity_threshold: float = 0.61, batch_size: int = 64,
614 |                  n_clusters: int = 4, deduplication_threshold: float = 0.85, model_name=os.getenv("DEFAULTMODELSUMMERY"),
615 |                  embedding_model=os.getenv("DEFAULTMODELEMBEDDING"),
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B008
616 |                  vis_class:str | None = "EnhancedVectorStore",
617 |                  vis_kwargs:dict[str, Any] | None=None,
    |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1714:28: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1712 |                 # Load data from disk
1713 |                 with open(path, 'rb') as f:
1714 |                     data = pickle.load(f)
     |                            ^^^^^^^^^^^^^^ S301
1715 |             elif isinstance(path, bytes):
1716 |                 data = pickle.loads(path)
     |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1716:24: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1714 |                     data = pickle.load(f)
1715 |             elif isinstance(path, bytes):
1716 |                 data = pickle.loads(path)
     |                        ^^^^^^^^^^^^^^^^^^ S301
1717 |             else:
1718 |                 raise ValueError("Invalid path type")
     |

toolboxv2\mods\isaa\base\KnowledgeBase.py:1893:11: F811 Redefinition of unused `math` from line 4
     |
1891 | text = "test 123".encode("utf-8", errors="replace").decode("utf-8")
1892 |
1893 | async def math():
     |           ^^^^ F811
1894 |     kb = KnowledgeBase(n_clusters=3, model_name="openrouter/mistralai/mistral-7b-instruct", requests_per_second=10, batch_size=20, c…
     |
     = help: Remove definition: `math`

toolboxv2\mods\isaa\base\VectorStores\__init__.py:19:68: F401 `toolboxv2.mods.isaa.base.VectorStores.qdrant_store.QdrantVectorStore` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
18 | try:
19 |     from toolboxv2.mods.isaa.base.VectorStores.qdrant_store import QdrantVectorStore
   |                                                                    ^^^^^^^^^^^^^^^^^ F401
20 |     QDRANT_AVAILABLE = True
21 | except ImportError:
   |
   = help: Remove unused import: `toolboxv2.mods.isaa.base.VectorStores.qdrant_store.QdrantVectorStore`

toolboxv2\mods\isaa\base\VectorStores\defaults.py:240:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
239 |     def load(self, data: bytes) -> 'NumpyVectorStore':
240 |         loaded = pickle.loads(data)
    |                  ^^^^^^^^^^^^^^^^^^ S301
241 |         self.embeddings = loaded['embeddings']
242 |         self.chunks = loaded['chunks']
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:312:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
310 |         import faiss
311 |
312 |         loaded = pickle.loads(data)
    |                  ^^^^^^^^^^^^^^^^^^ S301
313 |         self.dimension = loaded['dimension']
314 |         self.index = faiss.deserialize_index(loaded['index_bytes'])
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:413:23: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
411 |     def load(self, data: bytes) -> 'RedisVectorStore':
412 |         self.clear()
413 |         loaded_data = pickle.loads(data)
    |                       ^^^^^^^^^^^^^^^^^^ S301
414 |         pipe = self.redis_client.pipeline()
415 |         for hash_data in loaded_data:
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:611:26: SIM115 Use a context manager for opening files
    |
609 |             f.write(b'\0' * initial_size)
610 |
611 |         self.mmap_file = open(mmap_path, 'r+b')
    |                          ^^^^ SIM115
612 |         self.mmap_array = np.memmap(
613 |             self.mmap_file,
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:622:21: SIM115 Use a context manager for opening files
    |
620 |         with self.lock:
621 |             self.index.save_index("index.temp")
622 |             index = open("index.s.temp", 'rb').read()
    |                     ^^^^ SIM115
623 |             os.remove("index.s.temp")
624 |             state = {
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:633:21: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
631 |     def load(self, data: bytes) -> 'EnhancedVectorStore':
632 |         with self.lock:
633 |             state = pickle.loads(data)
    |                     ^^^^^^^^^^^^^^^^^^ S301
634 |             self.chunks = state['chunks']
635 |             self.config = state['config']
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:636:13: SIM115 Use a context manager for opening files
    |
634 |             self.chunks = state['chunks']
635 |             self.config = state['config']
636 |             open("index.l.temp", "wb").write(state['index'])
    |             ^^^^ SIM115
637 |             self.index.load_index("index.l.temp")
638 |             os.remove("index.f.temp")
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:854:17: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
853 |     def load(self, data: bytes) -> 'FastVectorStore':
854 |         state = pickle.loads(data)
    |                 ^^^^^^^^^^^^^^^^^^ S301
855 |         self.current_size = state['current_size']
856 |         self.chunks = state['chunks']
    |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:1051:17: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1050 |     def load(self, data: bytes) -> 'FastVectorStore':
1051 |         state = pickle.loads(data)
     |                 ^^^^^^^^^^^^^^^^^^ S301
1052 |         self.current_size = state['current_size']
1053 |         self.chunks = state['chunks']
     |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:1172:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1171 |     def load(self, data: bytes) -> 'FastVectorStoreO':
1172 |         loaded = pickle.loads(data)
     |                  ^^^^^^^^^^^^^^^^^^ S301
1173 |         self.embeddings = loaded['embeddings'].astype(np.float32)
1174 |         self.chunks = loaded['chunks']
     |

toolboxv2\mods\isaa\base\VectorStores\defaults.py:1428:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
     |
1427 |     def load(self, data: bytes) -> 'FastVectorStoreO':
1428 |         loaded = pickle.loads(data)
     |                  ^^^^^^^^^^^^^^^^^^ S301
1429 |         self.embeddings = loaded['embeddings'].astype(np.float32)
1430 |         self.chunks = loaded['chunks']
     |

toolboxv2\mods\isaa\base\VectorStores\qdrant_store.py:21:9: F401 `qdrant_client.http.models.Range` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
19 |         MatchValue,
20 |         PointStruct,
21 |         Range,
   |         ^^^^^ F401
22 |         VectorParams,
23 |     )
   |
   = help: Remove unused import: `qdrant_client.http.models.Range`

toolboxv2\mods\isaa\base\VectorStores\qdrant_store.py:151:13: B007 Loop control variable `i` not used within loop body
    |
149 |         # Prepare points for Qdrant
150 |         points = []
151 |         for i, (embedding, chunk) in enumerate(zip(embeddings, chunks, strict=False)):
    |             ^ B007
152 |             # Generate a UUID for the point if not already present
153 |             point_id = str(uuid.uuid4())
    |
    = help: Rename unused `i` to `_i`

toolboxv2\mods\isaa\base\VectorStores\qdrant_store.py:276:17: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
274 |             Loaded vector store
275 |         """
276 |         state = pickle.loads(data)
    |                 ^^^^^^^^^^^^^^^^^^ S301
277 |
278 |         # Update configuration
    |

toolboxv2\mods\isaa\extras\filter.py:32:9: S605 Starting a process with a shell, possible injection detected
   |
30 |         from rapidfuzz import fuzz
31 |     except Exception:
32 |         os.system([sys.executable, '-m', 'pip', 'install', 'RapidFuzz'])
   |         ^^^^^^^^^ S605
33 |         from rapidfuzz import fuzz
34 |     try:
   |

toolboxv2\mods\isaa\extras\filter.py:37:9: S605 Starting a process with a shell, possible injection detected
   |
35 |         from sentence_transformers import SentenceTransformer, util
36 |     except Exception:
37 |         os.system([sys.executable, '-m', 'pip', 'install', 'sentence-transformers'])
   |         ^^^^^^^^^ S605
38 |         from sentence_transformers import SentenceTransformer, util
   |

toolboxv2\mods\isaa\extras\filter.py:148:13: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
146 |         return _d.encode("utf-8", errors='replace').decode("utf-8", errors='replace')
147 |     try:
148 |         d = eval(clean(d))
    |             ^^^^^^^^^^^^^^ S307
149 |     except SyntaxError:
150 |         print("Invalid syntax in input data")
    |

toolboxv2\mods\isaa\extras\filter.py:154:17: S307 Use of possibly insecure function; consider using `ast.literal_eval`
    |
152 |     if isinstance(d, str):
153 |         try:
154 |             d = eval(clean(d, ex=True))
    |                 ^^^^^^^^^^^^^^^^^^^^^^^ S307
155 |         except Exception:
156 |             d = after_format_(d1)
    |

toolboxv2\mods\isaa\isaa_modi.py:33:9: F401 `toolboxv2.mods.isaa_audio.get_audio_transcribe` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
31 | try:
32 |     from toolboxv2.mods.isaa_audio import (
33 |         get_audio_transcribe,
   |         ^^^^^^^^^^^^^^^^^^^^ F401
34 |         s30sek_mean,
35 |         speech_stream,
   |
   = help: Remove unused import: `toolboxv2.mods.isaa_audio.get_audio_transcribe`

toolboxv2\mods\isaa\isaa_modi.py:206:15: S602 `subprocess` call with `shell=True` identified, security issue
    |
205 |     command = f"git clone --branch {branch} {repo_url} {destination_folder}"
206 |     process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    |               ^^^^^^^^^^^^^^^^ S602
207 |     stdout, stderr = process.communicate()
    |

toolboxv2\mods\isaa\isaa_modi.py:267:21: SIM101 Multiple `isinstance` calls for expression, merge into a single call
    |
265 |                 errors.append(
266 |                     f"Invalid 'use' type '{use_type}' in a task. It should be 'agent', 'chain', 'tool', or 'function'. {i}")
267 |             if not (isinstance(task["args"], str) or isinstance(task["args"], dict)):
    |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ SIM101
268 |                 errors.append(f"The value of the 'args' key in a task must be a string. in task : {i}")
269 |             if not isinstance(task["return"], str):
    |
    = help: Merge `isinstance` calls

toolboxv2\mods\isaa\isaa_modi.py:919:9: S605 Starting a process with a shell, possible injection detected
    |
917 |         images_url = [images_url]
918 |     for image_url in images_url:
919 |         os.system(f'start {browser} {image_url}')
    |         ^^^^^^^^^ S605
    |

toolboxv2\mods\isaa\isaa_modi.py:957:16: S113 Probable use of `requests` call without timeout
    |
956 | def scrape_text(url):
957 |     response = requests.get(url, headers={
    |                ^^^^^^^^^^^^ S113
958 |         "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Saf…
    |

toolboxv2\mods\isaa\isaa_modi.py:992:16: S113 Probable use of `requests` call without timeout
    |
991 | def scrape_links(url):
992 |     response = requests.get(url, headers={
    |                ^^^^^^^^^^^^ S113
993 |         "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Saf…
    |

toolboxv2\mods\isaa\isaa_modi.py:1142:13: SIM102 Use a single `if` statement instead of nested `if` statements
     |
1140 |                                      exclude=['task_list', 'task_list_done', 'step_between', 'pre_task', 'task_index'])
1141 |               self.print("Initialized from config augment")
1142 | /             if 'tools' in self.config['augment']:
1143 | |                 if self.config['augment']['tools']:
     | |___________________________________________________^ SIM102
1144 |                       return
1145 |           else:
     |
     = help: Combine `if` statements using `and`

toolboxv2\mods\isaa\lnn\binary_test.py:35:31: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
34 | def add_noise(binary_list, noise_level=0.1):
35 |     return [max(0, min(1, b + random.uniform(-noise_level, noise_level))) for b in binary_list]
   |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
   |

toolboxv2\mods\isaa\lnn\text_test_mini.py:69:9: B007 Loop control variable `i` not used within loop body
   |
68 |     text = text[0]
69 |     for i in range(10):
   |         ^ B007
70 |         text += predict_next_token(model, text[-1])
71 |     print(f"New text: {text}")
   |
   = help: Rename unused `i` to `_i`

toolboxv2\mods\isaa\lnn\tf.py:262:13: B007 Loop control variable `step` not used within loop body
    |
260 |     for epoch in range(3):  # try a few epochs
261 |         epoch_loss = 0
262 |         for step, (x_batch, y_batch) in enumerate(train_dataset):
    |             ^^^^ B007
263 |             loss_val = ff_train_step(ff_model, ff_optimizer, x_batch, y_batch, threshold=1.0)
264 |             epoch_loss += loss_val
    |
    = help: Rename unused `step` to `_step`

toolboxv2\mods\isaa\module.py:110:16: S113 Probable use of `requests` call without timeout
    |
109 | def get_ip():
110 |     response = requests.get('https://api64.ipify.org?format=json').json()
    |                ^^^^^^^^^^^^ S113
111 |     return response["ip"]
    |

toolboxv2\mods\isaa\module.py:117:16: S113 Probable use of `requests` call without timeout
    |
115 | def get_location():
116 |     ip_address = get_ip()
117 |     response = requests.get(f'https://ipapi.co/{ip_address}/json/').json()
    |                ^^^^^^^^^^^^ S113
118 |     location_data = f"city: {response.get('city')},region: {response.get('region')},country: {response.get('country_name')},"
    |

toolboxv2\mods\isaa\module.py:128:41: F821 Undefined name `ProjectManager`
    |
127 |         self.run_callback = None
128 |         self.coding_projects: dict[str, ProjectManager] = {}
    |                                         ^^^^^^^^^^^^^^ F821
129 |         self.pipes: dict[str, Pipeline] = {}
130 |         if app is None:
    |

toolboxv2\mods\isaa\module.py:386:13: SIM102 Use a single `if` statement instead of nested `if` statements
    |
384 |               if 'taskstack' in agent_data:
385 |                   del agent_data['taskstack']
386 | /             if 'amd' in agent_data and 'provider' in agent_data['amd']:
387 | |                 if isinstance(agent_data['amd'].get('provider'), Enum):
    | |_______________________________________________________________________^ SIM102
388 |                       agent_data['amd']['provider'] = str(agent_data['amd'].get('provider').name).upper()
389 |               data[agent_name] = agent_data
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\isaa\module.py:438:45: SIM112 Use capitalized environment variable `IFTTTKEY` instead of `IFTTTKey`
    |
436 |         self.config['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY')
437 |         self.config['REPLICATE_API_TOKEN'] = os.getenv('REPLICATE_API_TOKEN')
438 |         self.config['IFTTTKey'] = os.getenv('IFTTTKey')
    |                                             ^^^^^^^^^^ SIM112
439 |         self.config['SERP_API_KEY'] = os.getenv('SERP_API_KEY')
440 |         self.config['PINECONE_API_KEY'] = os.getenv('PINECONE_API_KEY')
    |
    = help: Replace `IFTTTKey` with `IFTTTKEY`

toolboxv2\mods\isaa\module.py:623:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
621 |           agent_builder: AgentBuilder = self.get_agent_builder(name)
622 |
623 | /         if name != "":
624 | |             if os.path.exists(f".data/{get_app('isaa.get_default_agent_builder').id}/Memory/{name}.agent"):
    | |___________________________________________________________________________________________________________^ SIM102
625 |                   agent_builder = agent_builder.load_from_json_file(f".data/{get_app('isaa.get_default_agent_builder').id}/Memory/{name…
626 |                   agent_builder.set_isaa_reference(self)
    |
    = help: Combine `if` statements using `and`

toolboxv2\mods\isaa\module.py:844:25: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
    |
842 |                         agent = agent_builder.build()
843 |                     except Exception:
844 |                         subprocess.Popen("wsl -e ollama serve", shell=True, stdout=subprocess.PIPE,
    |                         ^^^^^^^^^^^^^^^^ S602
845 |                                                    stderr=subprocess.PIPE)
846 |                         time.sleep(5)
    |

toolboxv2\mods\isaa\module.py:1176:21: B021 f-string used as docstring. Python will interpret this as a joined string, rather than a docstring.
     |
1175 |                 class SummarizationSegments(BaseModel):
1176 |                     f"""importance for: {ref if ref != text else 'key details and concrete informations'}"""
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B021
1177 |                     segments: list[Segments] = field(default_factory=list)
     |

toolboxv2\mods\isaa\module.py:1349:19: S602 `subprocess` call with `shell=True` identified, security issue
     |
1348 |         # Execute command
1349 |         process = subprocess.run(
     |                   ^^^^^^^^^^^^^^ S602
1350 |             full_cmd,
1351 |             shell=True,
     |

toolboxv2\mods\isaa\subtools\audo_to_text_assemblyai.py:16:78: B008 Do not perform function call `_api_key_` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
16 | def translate(filename_url, transcript_format=TranscriptFormat.TEXT, api_key=_api_key_()):
   |                                                                              ^^^^^^^^^^^ B008
17 |     if Audio_Avalabel is not True:
18 |         raise ImportError(e)
   |

toolboxv2\mods\isaa\subtools\audo_to_text_assemblyai.py:18:27: F821 Undefined name `e`
   |
16 | def translate(filename_url, transcript_format=TranscriptFormat.TEXT, api_key=_api_key_()):
17 |     if Audio_Avalabel is not True:
18 |         raise ImportError(e)
   |                           ^ F821
19 |     loader = AssemblyAIAudioTranscriptLoader(file_path=filename_url, transcript_format=transcript_format,
20 |                                              api_key=api_key)
   |

toolboxv2\mods\isaa\subtools\open_weather_map.py:12:44: B008 Do not perform function call `_api_key_` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
12 | def get_weather_data(places: list, api_key=_api_key_()):
   |                                            ^^^^^^^^^^^ B008
13 |     if isinstance(places, str):
14 |         places = [places]
   |

toolboxv2\mods\isaa\subtools\web_loder.py:132:5: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
130 |       try:
131 |           return get_data_from_web([url], **kwargs)
132 | /     except:
133 | |         pass
    | |____________^ S110
134 |
135 |       try:
    |

toolboxv2\mods\isaa\subtools\web_loder.py:139:5: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
137 |           if _test_valid(docs1):
138 |               return loader1, docs1
139 | /     except:
140 | |         pass
    | |____________^ S110
141 |
142 |       try:
    |

toolboxv2\mods\isaa\subtools\web_loder.py:146:5: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
144 |           if _test_valid(docs1()):
145 |               return loader1, docs1
146 | /     except:
147 | |         pass
    | |____________^ S110
148 |       try:
149 |           loader1, docs1 = get_text_from_urls_play([url], **kwargs)
    |

toolboxv2\mods\isaa\subtools\web_loder.py:152:5: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
150 |           if _test_valid(docs1()):
151 |               return loader1, docs1
152 | /     except:
153 | |         pass
    | |____________^ S110
154 |
155 |           # Hier müsste eine Logik implementiert werden, um die Ergebnisse zu vergleichen und das beste zu wählen
    |

toolboxv2\mods\isaa\ui\nice.py:1115:11: F811 Redefinition of unused `chat_websocket` from line 1107
     |
1113 | @export(mod_name="isaa", request_as_kwarg=True, level=1, api=True,
1114 |         name="chat_websocket", row=True)
1115 | async def chat_websocket(websocket: WebSocket, spec: str = "main"):
     |           ^^^^^^^^^^^^^^ F811
1116 |     chat_widget = IsaaWebSocketUI(get_app('chat.websocket').get_mod("isaa", spec=spec))
1117 |     await chat_widget.handle_websocket_chat(websocket)
     |
     = help: Remove definition: `chat_websocket`

toolboxv2\mods\talk.py:223:25: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
221 |                               print("NEW Magic-Number:", i)
222 |                               break
223 | /                         except Exception:
224 | |                             pass
    | |________________________________^ S110
225 |               else:
226 |                   text = stt(audio_data)['text']
    |

toolboxv2\mods\welcome.py:51:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
49 | def cls():
50 |     if system() == "Windows":
51 |         os.system("cls")
   |         ^^^^^^^^^ S605
52 |     else:
53 |         os.system("clear")
   |

toolboxv2\mods\welcome.py:53:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
51 |         os.system("cls")
52 |     else:
53 |         os.system("clear")
   |         ^^^^^^^^^ S605
   |

toolboxv2\setup_helper.py:66:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
64 |       print("🔧 Installiere Dev-Tools...")
65 |       d = ["cargo", "node"]
66 | /     if a := input("With docker (N/y)"):
67 | |         if a.lower() == 'y':
   | |____________________________^ SIM102
68 |               d.append("docker")
69 |       for _d in d.copy():
   |
   = help: Combine `if` statements using `and`

toolboxv2\setup_helper.py:152:9: S602 `subprocess` call with `shell=True` identified, security issue
    |
150 |         cwd = _cwd
151 |     try:
152 |         subprocess.run(command, cwd=cwd, shell=True, check=True,
    |         ^^^^^^^^^^^^^^ S602
153 |                        stdout=subprocess.PIPE if silent else None)
154 |         return True
    |

toolboxv2\tests\test_utils\test_daemon\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(daemon_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await DaemonUtil(class_instance=None, host='0.0.0.0', port=6582, t=False,
20 |                              app=None, peer=False, name='daemonApp-server',
   |

toolboxv2\tests\test_utils\test_proxy\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(proxy_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
20 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

toolboxv2\tests\test_utils\test_proxy\test.py:23:14: B017 Do not assert blind exception: `Exception`
   |
21 |                                          unix_socket=False)
22 |
23 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
24 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
25 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:63:22: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
   |
61 |         # Check if the environment exists
62 |         try:
63 |             output = subprocess.check_output("conda env list", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
64 |             self.assertIn(self.test_env_name, output)
65 |         except subprocess.CalledProcessError:
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:81:13: S602 `subprocess` call with `shell=True` identified, security issue
   |
79 |         # Check that the environment no longer exists
80 |         with self.assertRaises(subprocess.CalledProcessError):
81 |             subprocess.check_output(f"conda env list | grep {self.test_env_name}", shell=True, text=True)
   |             ^^^^^^^^^^^^^^^^^^^^^^^ S602
82 |
83 |     def test_add_dependency(self):
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:96:22: S602 `subprocess` call with `shell=True` identified, security issue
   |
94 |         # Verify the dependency was added by checking conda list
95 |         try:
96 |             output = subprocess.check_output(f"conda list -n {self.test_env_name} numpy", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
97 |             self.assertIn("numpy", output)
98 |         except subprocess.CalledProcessError:
   |

toolboxv2\tests\test_utils\test_system\test_conda_runner.py:148:14: B017 Do not assert blind exception: `Exception`
    |
146 |         if not get_app(name="test").local_test:
147 |             return
148 |         with self.assertRaises(Exception):
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
149 |             res = add_dependency(self.test_env_name, "non_existent_package_xyz")
150 |             if res is False:
    |

toolboxv2\tests\web_test\__init__.py:1:1: F403 `from .test_main_page import *` used; unable to detect undefined names
  |
1 | from .test_main_page import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 |
3 | in_valid_session_tests = []
  |

toolboxv2\tests\web_test\__init__.py:4:24: F405 `contact_page_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                        ^^^^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

toolboxv2\tests\web_test\__init__.py:4:51: F405 `installer_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                                                   ^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

toolboxv2\tests\web_util.py:22:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
20 |     )
21 | except ImportError:
22 |     os.system("pip install playwright")
   |     ^^^^^^^^^ S605
23 |     from playwright.async_api import Browser as ABrowser
24 |     from playwright.async_api import BrowserContext as ABrowserContext
   |

toolboxv2\utils\__init__.py:1:8: F401 `os` imported but unused
  |
1 | import os
  |        ^^ F401
2 |
3 | from yaml import safe_load
  |
  = help: Remove unused import: `os`

toolboxv2\utils\__init__.py:3:18: F401 `yaml.safe_load` imported but unused
  |
1 | import os
2 |
3 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
4 |
5 | from .extras.show_and_hide_console import show_console
  |
  = help: Remove unused import: `yaml.safe_load`

toolboxv2\utils\brodcast\server.py:32:5: S110 `try`-`except`-`pass` detected, consider logging the exception
   |
30 |           data = server.recv(1024)
31 |           print(f"data received! {data.decode()}", flush=True)
32 | /     except:
33 | |         pass
   | |____________^ S110
34 |       finally:
35 |           server.close()
   |

toolboxv2\utils\daemon\daemon_util.py:10:1: F403 `from ..system.all_functions_enums import *` used; unable to detect undefined names
   |
 8 | from ..extras.show_and_hide_console import show_console
 9 | from ..extras.Style import Style
10 | from ..system.all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
11 | from ..system.getting_and_closing_app import get_app
12 | from ..system.tb_logger import get_logger
   |

toolboxv2\utils\daemon\daemon_util.py:85:45: F405 `SOCKETMANAGER` may be undefined, or defined from star imports
   |
83 |         if not app.mod_online("SocketManager"):
84 |             await app.load_mod("SocketManager")
85 |         server_result = await app.a_run_any(SOCKETMANAGER.CREATE_SOCKET,
   |                                             ^^^^^^^^^^^^^ F405
86 |                                             get_results=True,
87 |                                             name=self._name,
   |

toolboxv2\utils\daemon\daemon_util.py:216:71: B023 Function definition does not bind loop variable `name`
    |
214 |                         async def _helper_runner():
215 |                             try:
216 |                                 attr_f = getattr(self.class_instance, name)
    |                                                                       ^^^^ B023
217 |
218 |                                 if asyncio.iscoroutinefunction(attr_f):
    |

toolboxv2\utils\daemon\daemon_util.py:219:57: B023 Function definition does not bind loop variable `args`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                               ^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

toolboxv2\utils\daemon\daemon_util.py:219:65: B023 Function definition does not bind loop variable `kwargs`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                                       ^^^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

toolboxv2\utils\daemon\daemon_util.py:221:51: B023 Function definition does not bind loop variable `args`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                   ^^^^ B023
222 |
223 |                                 if res is None:
    |

toolboxv2\utils\daemon\daemon_util.py:221:59: B023 Function definition does not bind loop variable `kwargs`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                           ^^^^^^ B023
222 |
223 |                                 if res is None:
    |

toolboxv2\utils\daemon\daemon_util.py:237:51: B023 Function definition does not bind loop variable `identifier`
    |
235 |                                 get_logger().info(f"sending response {res} {type(res)}")
236 |
237 |                                 await sender(res, identifier)
    |                                                   ^^^^^^^^^^ B023
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |

toolboxv2\utils\daemon\daemon_util.py:239:64: B023 Function definition does not bind loop variable `identifier`
    |
237 |                                 await sender(res, identifier)
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |                                                                ^^^^^^^^^^ B023
240 |
241 |                         await _helper_runner()
    |

toolboxv2\utils\extras\Style.py:45:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
43 | def cls():
44 |     if system() == "Windows":
45 |         os.system("cls")
   |         ^^^^^^^^^ S605
46 |     if system() == "Linux":
47 |         os.system("clear")
   |

toolboxv2\utils\extras\Style.py:47:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
45 |         os.system("cls")
46 |     if system() == "Linux":
47 |         os.system("clear")
   |         ^^^^^^^^^ S605
   |

toolboxv2\utils\extras\Style.py:369:28: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
367 |             if i < len(words) - 1:
368 |                 print(" ", end="", flush=True)
369 |             typing_speed = uniform(min_typing_speed, max_typing_speed)
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
370 |             time.sleep(typing_speed)
371 |             # type faster after each word
    |

toolboxv2\utils\extras\base_widget.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / import asyncio
2 | | import uuid
  | |___________^ I001
  |
  = help: Organize imports

toolboxv2\utils\extras\base_widget.py:84:24: B023 Function definition does not bind loop variable `fuction`
   |
82 |         for fuction in functions:
83 |             def x(r):
84 |                 return fuction(request=r)
   |                        ^^^^^^^ B023
85 |             self.onReload.append(x)
   |

toolboxv2\utils\extras\base_widget.py:152:66: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
150 |         return asset
151 |
152 |     def generate_html(self, app, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                  ^^^^^^^^^^^^ B008
153 |         return app.run_any(MINIMALHTML.GENERATE_HTML,
154 |                            group_name=self.name,
    |

toolboxv2\utils\extras\base_widget.py:157:73: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
155 |                            collection_name=f"{name}-{asset_id}")
156 |
157 |     def load_widget(self, app, request, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                         ^^^^^^^^^^^^ B008
158 |         app.run_any(MINIMALHTML.ADD_GROUP, command=self.name)
159 |         self.reload(request)
    |

toolboxv2\utils\extras\blobs.py:55:13: SIM113 Use `enumerate()` for index variable `current_blob_id` in `for` loop
   |
53 |                 if index_ + 1 > len(blob_ids) and len(all_link[i + splitter:]) > 1:
54 |                     self.add_link(blob_ids[current_blob_id], blob_ids[current_blob_id], link_port)
55 |             current_blob_id += 1
   |             ^^^^^^^^^^^^^^^^^^^^ SIM113
56 |
57 |     def recover_blob(self, blob_ids, check_blobs_ids):
   |

toolboxv2\utils\extras\blobs.py:158:20: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
156 |             return self.create_blob(pickle.dumps({}), blob_id)
157 |         with open(blob_file, 'rb') as f:
158 |             return pickle.load(f)
    |                    ^^^^^^^^^^^^^^ S301
159 |
160 |     def _generate_recovery_bytes(self, blob_id):
    |

toolboxv2\utils\extras\blobs.py:180:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
178 |           self.storage = storage
179 |           self.data = b""
180 | /         if key is not None:
181 | |             if Code.decrypt_symmetric(Code.encrypt_symmetric("test", key), key) != "test":
    | |__________________________________________________________________________________________^ SIM102
182 |                   raise ValueError("Invalid Key")
183 |           self.key = key
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\extras\blobs.py:202:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
200 |     def __enter__(self):
201 |         if 'r' in self.mode:
202 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
203 |             if self.folder in blob_data:
204 |                 blob_folder = blob_data[self.folder]
    |

toolboxv2\utils\extras\blobs.py:220:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
218 |             if self.key is not None:
219 |                 data = Code.encrypt_symmetric(data, self.key)
220 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
221 |             if self.folder not in blob_data:
222 |                 blob_data[self.folder] = {self.datei: data}
    |

toolboxv2\utils\extras\blobs.py:268:16: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
266 |         if self.data == b"":
267 |             return {}
268 |         return pickle.loads(self.data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^ S301
269 |
270 |     def write_pickle(self, data):
    |

toolboxv2\utils\extras\bottleup.py:18:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
16 |     except ImportError:
17 |         print("Bottle is not available auto installation")
18 |         os.system("pip install bottle")
   |         ^^^^^^^^^ S605
19 |         return bottle_up(tb_app, user=user, main_route=main_route, **kwargs)
   |

toolboxv2\utils\extras\bottleup.py:125:112: B023 Function definition does not bind loop variable `tb_func`
    |
123 |                         if request_as_kwarg:
124 |                             def tb_func_(**kw):
125 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read()+tb_func(**kw)
    |                                                                                                                ^^^^^^^ B023
126 |                         else:
127 |                             def tb_func_():
    |

toolboxv2\utils\extras\bottleup.py:128:114: B023 Function definition does not bind loop variable `tb_func`
    |
126 |                         else:
127 |                             def tb_func_():
128 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read() + tb_func()
    |                                                                                                                  ^^^^^^^ B023
129 |                         self.route(f'/{mod_name}', method='GET')(tb_func_)
130 |                         print("adding root:", f'/{mod_name}')
    |

toolboxv2\utils\extras\gist_control.py:20:9: S102 Use of `exec` detected
   |
18 |         # Erstelle ein neues Modul
19 |         module = importlib.util.module_from_spec(self.get_spec(module_name))
20 |         exec(self.module_code, module.__dict__)
   |         ^^^^ S102
21 |         return module
   |

toolboxv2\utils\extras\gist_control.py:35:20: S113 Probable use of `requests` call without timeout
   |
33 |         api_url = f"https://api.github.com/gists/{gist_id}"
34 |
35 |         response = requests.get(api_url)
   |                    ^^^^^^^^^^^^ S113
36 |
37 |         if response.status_code == 200:
   |

toolboxv2\utils\extras\gist_control.py:77:20: S113 Probable use of `requests` call without timeout
   |
75 |         # Update an existing Gist
76 |         url = f"https://api.github.com/gists/{gist_id}"
77 |         response = requests.patch(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^^ S113
78 |     else:
79 |         # Create a new Gist
   |

toolboxv2\utils\extras\gist_control.py:81:20: S113 Probable use of `requests` call without timeout
   |
79 |         # Create a new Gist
80 |         url = "https://api.github.com/gists"
81 |         response = requests.post(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^ S113
82 |
83 |     # Check if the request was successful
   |

toolboxv2\utils\extras\helper_test_functions.py:52:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
50 |     """
51 |     if param_type in [int, float]:
52 |         return random.randint(0, 100)  # Zufällige normale Zahlen
   |                ^^^^^^^^^^^^^^^^^^^^^^ S311
53 |     elif param_type == str:
54 |         return "test" # Zufälliges Wort
   |

toolboxv2\utils\proxy\prox_util.py:149:21: SIM102 Use a single `if` statement instead of nested `if` statements
    |
147 |                               return await app_attr(*args, **kwargs)
148 |                           return app_attr(*args, **kwargs)
149 | /                     if (name == 'run_any' or name == 'a_run_any') and kwargs.get('get_results', False):
150 | |                         if isinstance(args[0], Enum):
    | |_____________________________________________________^ SIM102
151 |                               args = (args[0].__class__.NAME.value, args[0].value), args[1:]
152 |                       self.app.sprint(f"Calling method {name}, {args=}, {kwargs}=")
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\security\cryp.py:76:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
74 |             int: Eine zufällige Zahl.
75 |         """
76 |         return random.randint(2 ** 32 - 1, 2 ** 64 - 1)
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
77 |
78 |     @staticmethod
   |

toolboxv2\utils\security\cryp.py:334:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
332 |               )
333 |               return True
334 | /         except:
335 | |             pass
    | |________________^ S110
336 |           return False
    |

toolboxv2\utils\security\cryp.py:361:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
359 |               )
360 |               return True
361 | /         except:
362 | |             pass
    | |________________^ S110
363 |           return False
    |

toolboxv2\utils\system\__init__.py:1:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
  |
1 | from .all_functions_enums import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 | from .cache import FileCache, MemoryCache
3 | from .file_handler import FileHandler
  |

toolboxv2\utils\system\api.py:151:20: S113 Probable use of `requests` call without timeout
    |
149 |     print(f"Attempting to download executable from {url}...")
150 |     try:
151 |         response = requests.get(url, stream=True)
    |                    ^^^^^^^^^^^^ S113
152 |     except Exception as e:
153 |         print(f"Download error: {e}")
    |

toolboxv2\utils\system\api.py:163:33: S103 `os.chmod` setting a permissive mask `0o755` on file or directory
    |
161 |         # Make the file executable on non-Windows systems
162 |         if platform.system().lower() != "windows":
163 |             os.chmod(file_name, 0o755)
    |                                 ^^^^^ S103
164 |         return file_name
165 |     else:
    |

toolboxv2\utils\system\api.py:350:20: S108 Probable insecure usage of temporary file or directory: "/tmp/dill_package"
    |
348 |     """Package dill and all dependencies into a single .dill archive."""
349 |     try:
350 |         temp_dir = "/tmp/dill_package"
    |                    ^^^^^^^^^^^^^^^^^^^ S108
351 |         os.makedirs(temp_dir, exist_ok=True)
    |

toolboxv2\utils\system\api.py:761:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
759 |                     with open(PERSISTENT_FD_FILE) as f_fd: fd_val = f_fd.read().strip()
760 |                     print(f"  Listening FD (from file, POSIX only): {fd_val}")
761 |                 except Exception: pass
    |                 ^^^^^^^^^^^^^^^^^^^^^^ S110
762 |         else:
763 |             print("Server is STOPPED (or state inconsistent).")
    |

toolboxv2\utils\system\api.py:864:9: SIM105 Use `contextlib.suppress(Exception)` instead of `try`-`except`-`pass`
    |
862 |       if args.watch:
863 |           from toolboxv2 import tb_root_dir
864 | /         try:
865 | |             subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
866 | |         except Exception as e:
867 | |             pass
    | |________________^ SIM105
868 |           return
869 |       api_manager(args.action, args.debug, args.exe, args.version)
    |
    = help: Replace with `contextlib.suppress(Exception)`

toolboxv2\utils\system\api.py:865:13: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
    |
863 |         from toolboxv2 import tb_root_dir
864 |         try:
865 |             subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
    |             ^^^^^^^^^^^^^^ S602
866 |         except Exception as e:
867 |             pass
    |

toolboxv2\utils\system\api.py:866:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
864 |           try:
865 |               subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
866 | /         except Exception as e:
867 | |             pass
    | |________________^ S110
868 |           return
869 |       api_manager(args.action, args.debug, args.exe, args.version)
    |

toolboxv2\utils\system\api.py:866:29: F841 [*] Local variable `e` is assigned to but never used
    |
864 |         try:
865 |             subprocess.run("npm run dev", cwd=tb_root_dir, shell=True)
866 |         except Exception as e:
    |                             ^ F841
867 |             pass
868 |         return
    |
    = help: Remove assignment to unused variable `e`

toolboxv2\utils\system\cache.py:16:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
14 |     def get(self, key):
15 |         try:
16 |             with shelve.open(self.filename) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
17 |                 return db.get(key.replace('\x00', ''))
18 |         except Exception:
   |

toolboxv2\utils\system\cache.py:23:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
21 |     def set(self, key, value):
22 |         try:
23 |             with shelve.open(self.filename, writeback=True) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
24 |                 db[key.replace('\x00', '')] = value
25 |         except Exception:
   |

toolboxv2\utils\system\conda_runner.py:15:19: S602 `subprocess` call with `shell=True` identified, security issue
   |
13 |     if live:
14 |         # Using subprocess.Popen to stream stdout and stderr live
15 |         process = subprocess.Popen(command, shell=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
   |                   ^^^^^^^^^^^^^^^^ S602
16 |         process.communicate()  # Wait for the process to complete
17 |         return process.returncode == 0, None
   |

toolboxv2\utils\system\conda_runner.py:21:18: S602 `subprocess` call with `shell=True` identified, security issue
   |
19 |     try:
20 |         # If not live, capture output and return it
21 |         result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True, encoding='cp850')
   |                  ^^^^^^^^^^^^^^ S602
22 |         return True, result.stdout
23 |     except subprocess.CalledProcessError as e:
   |

toolboxv2\utils\system\file_handler.py:37:41: SIM115 Use a context manager for opening files
   |
35 |             self.file_handler_storage = None
36 |         try:
37 |             self.file_handler_storage = open(self.file_handler_file_prefix + self.file_handler_filename, mode)
   |                                         ^^^^ SIM115
38 |             self.file_handler_max_loaded_index_ += 1
39 |         except FileNotFoundError:
   |

toolboxv2\utils\system\file_handler.py:60:16: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
58 |                 self.file_handler_max_loaded_index_ = -1
59 |             rdu()
60 |         except OSError and PermissionError as e:
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
61 |             raise e
   |

toolboxv2\utils\system\file_handler.py:111:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
109 |               )
110 |               return False
111 | /         if key not in self.file_handler_load:
112 | |             if key in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
113 |                   key = self.file_handler_key_mapper[key]
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\system\file_handler.py:148:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
146 |                 self.file_handler_load[key] = self.decode_code(line)
147 |
148 |         except json.decoder.JSONDecodeError and Exception:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
149 |
150 |             for line in self.file_handler_storage:
    |

toolboxv2\utils\system\file_handler.py:166:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
164 |       def get_file_handler(self, obj: str, default=None) -> str or None:
165 |           logger = get_logger()
166 | /         if obj not in self.file_handler_load:
167 | |             if obj in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
168 |                   obj = self.file_handler_key_mapper[obj]
169 |           logger.info(Style.ITALIC(Style.GREY(f"Collecting data from storage key : {obj}")))
    |
    = help: Combine `if` statements using `and`

toolboxv2\utils\system\getting_and_closing_app.py:15:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
13 |   def override_main_app(app):
14 |       global registered_apps
15 | /     if registered_apps[0] is not None:
16 | |         if time.time() - registered_apps[0].called_exit[1] > 30:
   | |________________________________________________________________^ SIM102
17 |               raise PermissionError("Permission denied because of overtime fuction override_main_app sud only be called "
18 |                                     f"once and ontime overtime {time.time() - registered_apps[0].called_exit[1]}")
   |
   = help: Combine `if` statements using `and`

toolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^^^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

toolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

toolboxv2\utils\system\ipy_completer.py:64:9: SIM102 Use a single `if` statement instead of nested `if` statements
   |
62 |       for _name, obj in inspect.getmembers(module, inspect.isclass):
63 |           # Check if the class is defined in the current module
64 | /         if obj.__module__ == module.__name__:
65 | |             # Check if the class is a dataclass
66 | |             if is_dataclass(obj):
   | |_________________________________^ SIM102
67 |                   dataclasses.append(obj)
   |
   = help: Combine `if` statements using `and`

toolboxv2\utils\system\main_tool.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import inspect
 3 | | import os
 4 | | from toolboxv2.utils.extras import Style
 5 | |
 6 | | from .getting_and_closing_app import get_app
 7 | | from .tb_logger import get_logger
 8 | | from .types import Result, ToolBoxError, ToolBoxInfo, ToolBoxInterfaces, ToolBoxResult
   | |______________________________________________________________________________________^ I001
 9 |
10 |   try:
   |
   = help: Organize imports

toolboxv2\utils\system\session.py:265:24: S113 Probable use of `requests` call without timeout
    |
263 |             except Exception as e:
264 |                 print("Error session fetch:", e, self.username)
265 |                 return requests.request(method, url, data=data)
    |                        ^^^^^^^^^^^^^^^^ S113
266 |         else:
267 |             print(f"Could not find session using request on {url}")
    |

toolboxv2\utils\system\session.py:269:24: S113 Probable use of `requests` call without timeout
    |
267 |             print(f"Could not find session using request on {url}")
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
    |                        ^^^^^^^^^^^^^^^^ S113
270 |             return requests.request(method, url, data=data)
271 |             # raise Exception("Session not initialized. Please login first.")
    |

toolboxv2\utils\system\session.py:270:20: S113 Probable use of `requests` call without timeout
    |
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
270 |             return requests.request(method, url, data=data)
    |                    ^^^^^^^^^^^^^^^^ S113
271 |             # raise Exception("Session not initialized. Please login first.")
    |

toolboxv2\utils\system\session.py:351:20: S113 Probable use of `requests` call without timeout
    |
349 | def get_public_ip():
350 |     try:
351 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^ S113
352 |         ip_address = response.json()['ip']
353 |         return ip_address
    |

toolboxv2\utils\system\tb_logger.py:56:29: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
54 |         log_info_data_str = li.read()
55 |         try:
56 |             log_info_data = eval(log_info_data_str)
   |                             ^^^^^^^^^^^^^^^^^^^^^^^ S307
57 |         except SyntaxError:
58 |             if log_info_data_str:
   |

toolboxv2\utils\system\types.py:20:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
   |
18 | from ..extras import generate_test_cases
19 | from ..extras.Style import Spinner
20 | from .all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
21 | from .file_handler import FileHandler
   |

toolboxv2\utils\system\types.py:490:25: F405 `Enum` may be undefined, or defined from star imports
    |
490 | class ToolBoxError(str, Enum):
    |                         ^^^^ F405
491 |     none = "none"
492 |     input_error = "InputError"
    |

toolboxv2\utils\system\types.py:497:30: F405 `Enum` may be undefined, or defined from star imports
    |
497 | class ToolBoxInterfaces(str, Enum):
    |                              ^^^^ F405
498 |     cli = "CLI"
499 |     api = "API"
    |

toolboxv2\utils\system\types.py:540:62: F405 `Enum` may be undefined, or defined from star imports
    |
538 |     def as_result(self):
539 |         return Result(
540 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
541 |             result=ToolBoxResult(
542 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

toolboxv2\utils\system\types.py:542:86: F405 `Enum` may be undefined, or defined from star imports
    |
540 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
541 |             result=ToolBoxResult(
542 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
543 |                 data_info=self.result.data_info,
544 |                 data=self.result.data,
    |

toolboxv2\utils\system\types.py:582:64: F405 `Enum` may be undefined, or defined from star imports
    |
580 |     def as_dict(self):
581 |         return {
582 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                                ^^^^ F405
583 |         "result" : {
584 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

toolboxv2\utils\system\types.py:584:84: F405 `Enum` may be undefined, or defined from star imports
    |
582 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
583 |         "result" : {
584 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                    ^^^^ F405
585 |             "data_info":self.result.data_info,
586 |             "data":self.result.data,
    |

toolboxv2\utils\system\types.py:619:62: F405 `Enum` may be undefined, or defined from star imports
    |
617 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
618 |         return ApiResult(
619 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
620 |             result=ToolBoxResultBM(
621 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

toolboxv2\utils\system\types.py:621:86: F405 `Enum` may be undefined, or defined from star imports
    |
619 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
620 |             result=ToolBoxResultBM(
621 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
622 |                 data_info=self.result.data_info,
623 |                 data=self.result.data,
    |

toolboxv2\utils\system\types.py:641:46: F405 `Enum` may be undefined, or defined from star imports
    |
639 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
640 |         return ApiResult(
641 |             error=error if isinstance(error, Enum) else error,
    |                                              ^^^^ F405
642 |             result=ToolBoxResultBM(
643 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |

toolboxv2\utils\system\types.py:643:84: F405 `Enum` may be undefined, or defined from star imports
    |
641 |             error=error if isinstance(error, Enum) else error,
642 |             result=ToolBoxResultBM(
643 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |                                                                                    ^^^^ F405
644 |                 data_info=result.get('data_info', '404'),
645 |                 data=result.get('data'),
    |

toolboxv2\utils\system\types.py:892:45: F405 `Enum` may be undefined, or defined from star imports
    |
890 |     async def aget(self, key=None, default=None):
891 |         if asyncio.isfuture(self.result.data) or asyncio.iscoroutine(self.result.data) or (
892 |             isinstance(self.result.data_to, Enum) and self.result.data_to.name == ToolBoxInterfaces.future.name):
    |                                             ^^^^ F405
893 |             data = await self.result.data
894 |         else:
    |

toolboxv2\utils\system\types.py:1042:35: F405 `CLOUDM_AUTHMANAGER` may be undefined, or defined from star imports
     |
1041 |     async def get_user(self, username: str) -> Result:
1042 |         return self.app.a_run_any(CLOUDM_AUTHMANAGER.GET_USER_BY_NAME, username=username, get_results=True)
     |                                   ^^^^^^^^^^^^^^^^^^ F405
     |

toolboxv2\utils\system\types.py:1200:29: F405 `Enum` may be undefined, or defined from star imports
     |
1199 |     def _get_function(self,
1200 |                       name: Enum or None,
     |                             ^^^^ F405
1201 |                       state: bool = True,
1202 |                       specification: str = "app",
     |

toolboxv2\utils\system\types.py:1259:34: F405 `Enum` may be undefined, or defined from star imports
     |
1257 |         """proxi attr"""
1258 |
1259 |     def get_function(self, name: Enum or tuple, **kwargs):
     |                                  ^^^^ F405
1260 |         """
1261 |         Kwargs for _get_function
     |

toolboxv2\utils\system\types.py:1274:47: F405 `Enum` may be undefined, or defined from star imports
     |
1272 |         """
1273 |
1274 |     def run_function(self, mod_function_name: Enum or tuple,
     |                                               ^^^^ F405
1275 |                      tb_run_function_with_state=True,
1276 |                      tb_run_with_specification='app',
     |

toolboxv2\utils\system\types.py:1284:55: F405 `Enum` may be undefined, or defined from star imports
     |
1282 |         """proxi attr"""
1283 |
1284 |     async def a_run_function(self, mod_function_name: Enum or tuple,
     |                                                       ^^^^ F405
1285 |                              tb_run_function_with_state=True,
1286 |                              tb_run_with_specification='app',
     |

toolboxv2\utils\system\types.py:1314:49: F405 `Enum` may be undefined, or defined from star imports
     |
1312 |         """
1313 |
1314 |     async def run_http(self, mod_function_name: Enum or str or tuple, function_name=None, method="GET",
     |                                                 ^^^^ F405
1315 |                        args_=None,
1316 |                        kwargs_=None,
     |

toolboxv2\utils\system\types.py:1320:42: F405 `Enum` may be undefined, or defined from star imports
     |
1318 |         """run a function remote via http / https"""
1319 |
1320 |     def run_any(self, mod_function_name: Enum or str or tuple, backwords_compability_variabel_string_holder=None,
     |                                          ^^^^ F405
1321 |                 get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
1322 |                 kwargs_=None,
     |

toolboxv2\utils\system\types.py:1326:50: F405 `Enum` may be undefined, or defined from star imports
     |
1324 |         """proxi attr"""
1325 |
1326 |     async def a_run_any(self, mod_function_name: Enum or str or tuple,
     |                                                  ^^^^ F405
1327 |                         backwords_compability_variabel_string_holder=None,
1328 |                         get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
     |

toolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^^^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

toolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

toolboxv2\utils\toolbox.py:329:9: S605 Starting a process with a shell, possible injection detected
    |
327 |             return
328 |         self.print(f"Installing {module_name} GREEDY")
329 |         os.system(f"{sys.executable} -m pip install {module_name}")
    |         ^^^^^^^^^ S605
330 |
331 |     def python_module_import_classifier(self, mod_name, error_message):
    |

toolboxv2\utils\toolbox.py:689:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
687 |                           # Allow tasks time to clean up
688 |                           loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
689 | /                 except Exception:
690 | |                     pass
    | |________________________^ S110
691 |
692 |                   loop.close()
    |

toolboxv2\utils\toolbox.py:879:60: B023 Function definition does not bind loop variable `result`
    |
877 | …                     async def _():
878 | …                         try:
879 | …                             if asyncio.iscoroutine(result):
    |                                                      ^^^^^^ B023
880 | …                                 await result
881 | …                             if hasattr(result, 'Name'):
    |

toolboxv2\utils\toolbox.py:880:47: B023 Function definition does not bind loop variable `result`
    |
878 | …                     try:
879 | …                         if asyncio.iscoroutine(result):
880 | …                             await result
    |                                     ^^^^^^ B023
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
    |

toolboxv2\utils\toolbox.py:881:48: B023 Function definition does not bind loop variable `result`
    |
879 | …                     if asyncio.iscoroutine(result):
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
    |                                  ^^^^^^ B023
882 | …                         print('Opened :', result.Name)
883 | …                     elif hasattr(result, 'name'):
    |

toolboxv2\utils\toolbox.py:882:59: B023 Function definition does not bind loop variable `result`
    |
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
882 | …                         print('Opened :', result.Name)
    |                                             ^^^^^^ B023
883 | …                     elif hasattr(result, 'name'):
884 | …                         print('Opened :', result.name)
    |

toolboxv2\utils\toolbox.py:883:50: B023 Function definition does not bind loop variable `result`
    |
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
    |                                        ^^^^^^ B023
884 | …                             print('Opened :', result.name)
885 | …                     except Exception as e:
    |

toolboxv2\utils\toolbox.py:884:59: B023 Function definition does not bind loop variable `result`
    |
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
884 | …                             print('Opened :', result.name)
    |                                                 ^^^^^^ B023
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
    |

toolboxv2\utils\toolbox.py:887:48: B023 Function definition does not bind loop variable `result`
    |
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
887 | …                         if hasattr(result, 'Name'):
    |                                      ^^^^^^ B023
888 | …                             print('Error opening :', result.Name)
889 | …                         elif hasattr(result, 'name'):
    |

toolboxv2\utils\toolbox.py:888:66: B023 Function definition does not bind loop variable `result`
    |
886 | …                     self.debug_rains(e)
887 | …                     if hasattr(result, 'Name'):
888 | …                         print('Error opening :', result.Name)
    |                                                    ^^^^^^ B023
889 | …                     elif hasattr(result, 'name'):
890 | …                         print('Error opening :', result.name)
    |

toolboxv2\utils\toolbox.py:889:50: B023 Function definition does not bind loop variable `result`
    |
887 | …                             if hasattr(result, 'Name'):
888 | …                                 print('Error opening :', result.Name)
889 | …                             elif hasattr(result, 'name'):
    |                                            ^^^^^^ B023
890 | …                                 print('Error opening :', result.name)
891 | …                     asyncio.create_task(_())
    |

toolboxv2\utils\toolbox.py:890:66: B023 Function definition does not bind loop variable `result`
    |
888 |                                         print('Error opening :', result.Name)
889 |                                     elif hasattr(result, 'name'):
890 |                                         print('Error opening :', result.name)
    |                                                                  ^^^^^^ B023
891 |                             asyncio.create_task(_())
892 |                         else:
    |

toolboxv2\utils\toolbox.py:1583:33: SIM115 Use a context manager for opening files
     |
1581 |     def web_context(self):
1582 |         if self._web_context is None:
1583 |             self._web_context = open("./dist/helper.html", encoding="utf-8").read()
     |                                 ^^^^ SIM115
1584 |         return self._web_context
     |

toolboxv2\utils\toolbox.py:1650:21: S102 Use of `exec` detected
     |
1648 |                     code = compile(source, module.__file__, 'exec')
1649 |                     # Execute the code in the module's namespace
1650 |                     exec(code, module.__dict__)
     |                     ^^^^ S102
1651 |                 except Exception:
1652 |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
     |

toolboxv2\utils\toolbox.py:1651:17: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
1649 |                       # Execute the code in the module's namespace
1650 |                       exec(code, module.__dict__)
1651 | /                 except Exception:
1652 | |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
1653 | |                     pass
     | |________________________^ S110
1654 |                   return module
     |

uv_api_python_helper.py:11:9: S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
   |
 9 |     if not os.path.exists(dest):
10 |         print(f"Downloading {url}...")
11 |         urllib.request.urlretrieve(url, dest)
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S310
   |

uv_api_python_helper.py:46:13: S202 Uses of `tarfile.extractall()`
   |
44 |         download_file(python_url, archive_path)
45 |         with tarfile.open(archive_path, "r:gz") as tar:
46 |             tar.extractall(target_dir) # nosec: S202
   |             ^^^^^^^^^^^^^^ S202
47 |         os.remove(archive_path)
48 |         python_extracted = os.path.join(target_dir, f"Python-{version}")
   |

uv_api_python_helper.py:57:18: UP022 Prefer `capture_output` over sending `stdout` and `stderr` to `PIPE`
   |
55 |   def pip_exists(python_exe):
56 |       try:
57 |           result = subprocess.run(
   |  __________________^
58 | |             [python_exe, "-m", "pip", "--version"],
59 | |             stdout=subprocess.PIPE,
60 | |             stderr=subprocess.PIPE,
61 | |             check=True
62 | |         )
   | |_________^ UP022
63 |           print(result.stdout.decode().strip())
64 |           print("pip already installed.")
   |
   = help: Replace with `capture_output` keyword argument

~oolboxv2\__gui__.py:10:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
 8 |     import customtkinter as ctk
 9 | except ImportError:
10 |     os.system("pip install customtkinter")
   |     ^^^^^^^^^ S605
11 |     import customtkinter as ctk
   |

~oolboxv2\__init__.py:4:18: F401 `yaml.safe_load` imported but unused
  |
2 | import os
3 |
4 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
5 |
6 | try:
  |
  = help: Remove unused import: `yaml.safe_load`

~oolboxv2\__init__.py:116:12: F401 `toolboxv2.mods` imported but unused; consider using `importlib.util.find_spec` to test for availability
    |
114 | try:
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
    |            ^^^^^^^^^^^^^^ F401
117 |     from toolboxv2.mods import *
118 | except ImportError as e:
    |
    = help: Remove unused import: `toolboxv2.mods`

~oolboxv2\__init__.py:117:5: F403 `from toolboxv2.mods import *` used; unable to detect undefined names
    |
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
117 |     from toolboxv2.mods import *
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
118 | except ImportError as e:
119 |     MODS_ERROR = e
    |

~oolboxv2\__init__.py:129:22: F401 `platform.node` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                      ^^^^ F401
130 |
131 | __cwd__ = cwd = Path.cwd()
    |
    = help: Remove unused import

~oolboxv2\__init__.py:129:28: F401 `platform.system` imported but unused
    |
128 | from pathlib import Path
129 | from platform import node, system
    |                            ^^^^^^ F401
130 |
131 | __cwd__ = cwd = Path.cwd()
    |
    = help: Remove unused import

~oolboxv2\__init__.py:149:5: F405 `mods` may be undefined, or defined from star imports
    |
147 |     "get_logger",
148 |     "flows_dict",
149 |     "mods",
    |     ^^^^^^ F405
150 |     "get_app",
151 |     "TBEF",
    |

~oolboxv2\__main__.py:30:12: F401 `hmr` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
29 | try:
30 |     import hmr
   |            ^^^ F401
31 |
32 |     HOT_RELOADER = True
   |
   = help: Remove unused import: `hmr`

~oolboxv2\__main__.py:104:5: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
102 |     def profile_execute_all_functions(*args):
103 |         return print(args)
104 |     raise ValueError("Failed to import function for profiling")
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
105 |
106 | try:
    |

~oolboxv2\__main__.py:868:17: S605 Starting a process with a shell, possible injection detected
    |
866 |             print(f"Exit app {app_pid}")
867 |             if system() == "Windows":
868 |                 os.system(f"taskkill /pid {app_pid} /F")
    |                 ^^^^^^^^^ S605
869 |             else:
870 |                 os.system(f"kill -9 {app_pid}")
    |

~oolboxv2\__main__.py:870:17: S605 Starting a process with a shell, possible injection detected
    |
868 |                 os.system(f"taskkill /pid {app_pid} /F")
869 |             else:
870 |                 os.system(f"kill -9 {app_pid}")
    |                 ^^^^^^^^^ S605
871 |
872 |     if args.command and not args.background_application:
    |

~oolboxv2\__main__.py:894:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
    |
893 | def install_ipython():
894 |     os.system('pip install ipython prompt_toolkit')
    |     ^^^^^^^^^ S605
    |

~oolboxv2\__main__.py:1025:5: B018 Found useless expression. Either assign it to a variable or remove it.
     |
1024 | """)
1025 |     ()
     |     ^^ B018
1026 |     return c
     |

~oolboxv2\setup_helper.py:66:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
64 |       print("🔧 Installiere Dev-Tools...")
65 |       d = ["cargo", "node"]
66 | /     if a := input("With docker (N/y)"):
67 | |         if a.lower() == 'y':
   | |____________________________^ SIM102
68 |               d.append("docker")
69 |       for _d in d.copy():
   |
   = help: Combine `if` statements using `and`

~oolboxv2\setup_helper.py:152:9: S602 `subprocess` call with `shell=True` identified, security issue
    |
150 |         cwd = _cwd
151 |     try:
152 |         subprocess.run(command, cwd=cwd, shell=True, check=True,
    |         ^^^^^^^^^^^^^^ S602
153 |                        stdout=subprocess.PIPE if silent else None)
154 |         return True
    |

~oolboxv2\tests\test_utils\test_daemon\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(daemon_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await DaemonUtil(class_instance=None, host='0.0.0.0', port=6582, t=False,
20 |                              app=None, peer=False, name='daemonApp-server',
   |

~oolboxv2\tests\test_utils\test_proxy\test.py:18:14: B017 Do not assert blind exception: `Exception`
   |
16 |         self.assertFalse(proxy_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
19 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
20 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

~oolboxv2\tests\test_utils\test_proxy\test.py:23:14: B017 Do not assert blind exception: `Exception`
   |
21 |                                          unix_socket=False)
22 |
23 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
24 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
25 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:63:22: S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
   |
61 |         # Check if the environment exists
62 |         try:
63 |             output = subprocess.check_output("conda env list", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
64 |             self.assertIn(self.test_env_name, output)
65 |         except subprocess.CalledProcessError:
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:81:13: S602 `subprocess` call with `shell=True` identified, security issue
   |
79 |         # Check that the environment no longer exists
80 |         with self.assertRaises(subprocess.CalledProcessError):
81 |             subprocess.check_output(f"conda env list | grep {self.test_env_name}", shell=True, text=True)
   |             ^^^^^^^^^^^^^^^^^^^^^^^ S602
82 |
83 |     def test_add_dependency(self):
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:96:22: S602 `subprocess` call with `shell=True` identified, security issue
   |
94 |         # Verify the dependency was added by checking conda list
95 |         try:
96 |             output = subprocess.check_output(f"conda list -n {self.test_env_name} numpy", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^ S602
97 |             self.assertIn("numpy", output)
98 |         except subprocess.CalledProcessError:
   |

~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:148:14: B017 Do not assert blind exception: `Exception`
    |
146 |         if not get_app(name="test").local_test:
147 |             return
148 |         with self.assertRaises(Exception):
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B017
149 |             res = add_dependency(self.test_env_name, "non_existent_package_xyz")
150 |             if res is False:
    |

~oolboxv2\tests\web_test\__init__.py:1:1: F403 `from .test_main_page import *` used; unable to detect undefined names
  |
1 | from .test_main_page import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 |
3 | in_valid_session_tests = []
  |

~oolboxv2\tests\web_test\__init__.py:4:24: F405 `contact_page_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                        ^^^^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

~oolboxv2\tests\web_test\__init__.py:4:51: F405 `installer_interactions` may be undefined, or defined from star imports
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                                                   ^^^^^^^^^^^^^^^^^^^^^^ F405
5 | loot_session_tests = []
  |

~oolboxv2\tests\web_util.py:22:5: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
20 |     )
21 | except ImportError:
22 |     os.system("pip install playwright")
   |     ^^^^^^^^^ S605
23 |     from playwright.async_api import Browser as ABrowser
24 |     from playwright.async_api import BrowserContext as ABrowserContext
   |

~oolboxv2\utils\__init__.py:1:8: F401 `os` imported but unused
  |
1 | import os
  |        ^^ F401
2 |
3 | from yaml import safe_load
  |
  = help: Remove unused import: `os`

~oolboxv2\utils\__init__.py:3:18: F401 `yaml.safe_load` imported but unused
  |
1 | import os
2 |
3 | from yaml import safe_load
  |                  ^^^^^^^^^ F401
4 |
5 | from .extras.show_and_hide_console import show_console
  |
  = help: Remove unused import: `yaml.safe_load`

~oolboxv2\utils\brodcast\server.py:32:5: S110 `try`-`except`-`pass` detected, consider logging the exception
   |
30 |           data = server.recv(1024)
31 |           print(f"data received! {data.decode()}", flush=True)
32 | /     except:
33 | |         pass
   | |____________^ S110
34 |       finally:
35 |           server.close()
   |

~oolboxv2\utils\daemon\daemon_util.py:10:1: F403 `from ..system.all_functions_enums import *` used; unable to detect undefined names
   |
 8 | from ..extras.show_and_hide_console import show_console
 9 | from ..extras.Style import Style
10 | from ..system.all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
11 | from ..system.getting_and_closing_app import get_app
12 | from ..system.tb_logger import get_logger
   |

~oolboxv2\utils\daemon\daemon_util.py:85:45: F405 `SOCKETMANAGER` may be undefined, or defined from star imports
   |
83 |         if not app.mod_online("SocketManager"):
84 |             await app.load_mod("SocketManager")
85 |         server_result = await app.a_run_any(SOCKETMANAGER.CREATE_SOCKET,
   |                                             ^^^^^^^^^^^^^ F405
86 |                                             get_results=True,
87 |                                             name=self._name,
   |

~oolboxv2\utils\daemon\daemon_util.py:216:71: B023 Function definition does not bind loop variable `name`
    |
214 |                         async def _helper_runner():
215 |                             try:
216 |                                 attr_f = getattr(self.class_instance, name)
    |                                                                       ^^^^ B023
217 |
218 |                                 if asyncio.iscoroutinefunction(attr_f):
    |

~oolboxv2\utils\daemon\daemon_util.py:219:57: B023 Function definition does not bind loop variable `args`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                               ^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

~oolboxv2\utils\daemon\daemon_util.py:219:65: B023 Function definition does not bind loop variable `kwargs`
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                                       ^^^^^^ B023
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

~oolboxv2\utils\daemon\daemon_util.py:221:51: B023 Function definition does not bind loop variable `args`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                   ^^^^ B023
222 |
223 |                                 if res is None:
    |

~oolboxv2\utils\daemon\daemon_util.py:221:59: B023 Function definition does not bind loop variable `kwargs`
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                           ^^^^^^ B023
222 |
223 |                                 if res is None:
    |

~oolboxv2\utils\daemon\daemon_util.py:237:51: B023 Function definition does not bind loop variable `identifier`
    |
235 |                                 get_logger().info(f"sending response {res} {type(res)}")
236 |
237 |                                 await sender(res, identifier)
    |                                                   ^^^^^^^^^^ B023
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |

~oolboxv2\utils\daemon\daemon_util.py:239:64: B023 Function definition does not bind loop variable `identifier`
    |
237 |                                 await sender(res, identifier)
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |                                                                ^^^^^^^^^^ B023
240 |
241 |                         await _helper_runner()
    |

~oolboxv2\utils\extras\Style.py:45:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
43 | def cls():
44 |     if system() == "Windows":
45 |         os.system("cls")
   |         ^^^^^^^^^ S605
46 |     if system() == "Linux":
47 |         os.system("clear")
   |

~oolboxv2\utils\extras\Style.py:47:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
45 |         os.system("cls")
46 |     if system() == "Linux":
47 |         os.system("clear")
   |         ^^^^^^^^^ S605
   |

~oolboxv2\utils\extras\Style.py:369:28: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
    |
367 |             if i < len(words) - 1:
368 |                 print(" ", end="", flush=True)
369 |             typing_speed = uniform(min_typing_speed, max_typing_speed)
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
370 |             time.sleep(typing_speed)
371 |             # type faster after each word
    |

~oolboxv2\utils\extras\base_widget.py:74:24: B023 Function definition does not bind loop variable `fuction`
   |
72 |         for fuction in functions:
73 |             def x(r):
74 |                 return fuction(request=r)
   |                        ^^^^^^^ B023
75 |             self.onReload.append(x)
   |

~oolboxv2\utils\extras\base_widget.py:142:66: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
140 |         return asset
141 |
142 |     def generate_html(self, app, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                  ^^^^^^^^^^^^ B008
143 |         return app.run_any(MINIMALHTML.GENERATE_HTML,
144 |                            group_name=self.name,
    |

~oolboxv2\utils\extras\base_widget.py:147:73: B008 Do not perform function call `uuid.uuid4` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
    |
145 |                            collection_name=f"{name}-{asset_id}")
146 |
147 |     def load_widget(self, app, request, name="MainWidget", asset_id=str(uuid.uuid4())[:4]):
    |                                                                         ^^^^^^^^^^^^ B008
148 |         app.run_any(MINIMALHTML.ADD_GROUP, command=self.name)
149 |         self.reload(request)
    |

~oolboxv2\utils\extras\blobs.py:55:13: SIM113 Use `enumerate()` for index variable `current_blob_id` in `for` loop
   |
53 |                 if index_ + 1 > len(blob_ids) and len(all_link[i + splitter:]) > 1:
54 |                     self.add_link(blob_ids[current_blob_id], blob_ids[current_blob_id], link_port)
55 |             current_blob_id += 1
   |             ^^^^^^^^^^^^^^^^^^^^ SIM113
56 |
57 |     def recover_blob(self, blob_ids, check_blobs_ids):
   |

~oolboxv2\utils\extras\blobs.py:158:20: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
156 |             return self.create_blob(pickle.dumps({}), blob_id)
157 |         with open(blob_file, 'rb') as f:
158 |             return pickle.load(f)
    |                    ^^^^^^^^^^^^^^ S301
159 |
160 |     def _generate_recovery_bytes(self, blob_id):
    |

~oolboxv2\utils\extras\blobs.py:180:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
178 |           self.storage = storage
179 |           self.data = b""
180 | /         if key is not None:
181 | |             if Code.decrypt_symmetric(Code.encrypt_symmetric("test", key), key) != "test":
    | |__________________________________________________________________________________________^ SIM102
182 |                   raise ValueError("Invalid Key")
183 |           self.key = key
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\extras\blobs.py:202:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
200 |     def __enter__(self):
201 |         if 'r' in self.mode:
202 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
203 |             if self.folder in blob_data:
204 |                 blob_folder = blob_data[self.folder]
    |

~oolboxv2\utils\extras\blobs.py:220:25: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
218 |             if self.key is not None:
219 |                 data = Code.encrypt_symmetric(data, self.key)
220 |             blob_data = pickle.loads(self.storage.read_blob(self.blob_id))
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
221 |             if self.folder not in blob_data:
222 |                 blob_data[self.folder] = {self.datei: data}
    |

~oolboxv2\utils\extras\blobs.py:268:16: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
    |
266 |         if self.data == b"":
267 |             return {}
268 |         return pickle.loads(self.data)
    |                ^^^^^^^^^^^^^^^^^^^^^^^ S301
269 |
270 |     def write_pickle(self, data):
    |

~oolboxv2\utils\extras\bottleup.py:18:9: S605 Starting a process with a shell: seems safe, but may be changed in the future; consider rewriting without `shell`
   |
16 |     except ImportError:
17 |         print("Bottle is not available auto installation")
18 |         os.system("pip install bottle")
   |         ^^^^^^^^^ S605
19 |         return bottle_up(tb_app, user=user, main_route=main_route, **kwargs)
   |

~oolboxv2\utils\extras\bottleup.py:125:112: B023 Function definition does not bind loop variable `tb_func`
    |
123 |                         if request_as_kwarg:
124 |                             def tb_func_(**kw):
125 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read()+tb_func(**kw)
    |                                                                                                                ^^^^^^^ B023
126 |                         else:
127 |                             def tb_func_():
    |

~oolboxv2\utils\extras\bottleup.py:128:114: B023 Function definition does not bind loop variable `tb_func`
    |
126 |                         else:
127 |                             def tb_func_():
128 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read() + tb_func()
    |                                                                                                                  ^^^^^^^ B023
129 |                         self.route(f'/{mod_name}', method='GET')(tb_func_)
130 |                         print("adding root:", f'/{mod_name}')
    |

~oolboxv2\utils\extras\gist_control.py:20:9: S102 Use of `exec` detected
   |
18 |         # Erstelle ein neues Modul
19 |         module = importlib.util.module_from_spec(self.get_spec(module_name))
20 |         exec(self.module_code, module.__dict__)
   |         ^^^^ S102
21 |         return module
   |

~oolboxv2\utils\extras\gist_control.py:35:20: S113 Probable use of `requests` call without timeout
   |
33 |         api_url = f"https://api.github.com/gists/{gist_id}"
34 |
35 |         response = requests.get(api_url)
   |                    ^^^^^^^^^^^^ S113
36 |
37 |         if response.status_code == 200:
   |

~oolboxv2\utils\extras\gist_control.py:77:20: S113 Probable use of `requests` call without timeout
   |
75 |         # Update an existing Gist
76 |         url = f"https://api.github.com/gists/{gist_id}"
77 |         response = requests.patch(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^^ S113
78 |     else:
79 |         # Create a new Gist
   |

~oolboxv2\utils\extras\gist_control.py:81:20: S113 Probable use of `requests` call without timeout
   |
79 |         # Create a new Gist
80 |         url = "https://api.github.com/gists"
81 |         response = requests.post(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^ S113
82 |
83 |     # Check if the request was successful
   |

~oolboxv2\utils\extras\helper_test_functions.py:52:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
50 |     """
51 |     if param_type in [int, float]:
52 |         return random.randint(0, 100)  # Zufällige normale Zahlen
   |                ^^^^^^^^^^^^^^^^^^^^^^ S311
53 |     elif param_type == str:
54 |         return "test" # Zufälliges Wort
   |

~oolboxv2\utils\proxy\prox_util.py:149:21: SIM102 Use a single `if` statement instead of nested `if` statements
    |
147 |                               return await app_attr(*args, **kwargs)
148 |                           return app_attr(*args, **kwargs)
149 | /                     if (name == 'run_any' or name == 'a_run_any') and kwargs.get('get_results', False):
150 | |                         if isinstance(args[0], Enum):
    | |_____________________________________________________^ SIM102
151 |                               args = (args[0].__class__.NAME.value, args[0].value), args[1:]
152 |                       self.app.sprint(f"Calling method {name}, {args=}, {kwargs}=")
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\security\cryp.py:76:16: S311 Standard pseudo-random generators are not suitable for cryptographic purposes
   |
74 |             int: Eine zufällige Zahl.
75 |         """
76 |         return random.randint(2 ** 32 - 1, 2 ** 64 - 1)
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S311
77 |
78 |     @staticmethod
   |

~oolboxv2\utils\security\cryp.py:334:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
332 |               )
333 |               return True
334 | /         except:
335 | |             pass
    | |________________^ S110
336 |           return False
    |

~oolboxv2\utils\security\cryp.py:361:9: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
359 |               )
360 |               return True
361 | /         except:
362 | |             pass
    | |________________^ S110
363 |           return False
    |

~oolboxv2\utils\system\__init__.py:1:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
  |
1 | from .all_functions_enums import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
2 | from .cache import FileCache, MemoryCache
3 | from .file_handler import FileHandler
  |

~oolboxv2\utils\system\api.py:151:20: S113 Probable use of `requests` call without timeout
    |
149 |     print(f"Attempting to download executable from {url}...")
150 |     try:
151 |         response = requests.get(url, stream=True)
    |                    ^^^^^^^^^^^^ S113
152 |     except Exception as e:
153 |         print(f"Download error: {e}")
    |

~oolboxv2\utils\system\api.py:163:33: S103 `os.chmod` setting a permissive mask `0o755` on file or directory
    |
161 |         # Make the file executable on non-Windows systems
162 |         if platform.system().lower() != "windows":
163 |             os.chmod(file_name, 0o755)
    |                                 ^^^^^ S103
164 |         return file_name
165 |     else:
    |

~oolboxv2\utils\system\api.py:350:20: S108 Probable insecure usage of temporary file or directory: "/tmp/dill_package"
    |
348 |     """Package dill and all dependencies into a single .dill archive."""
349 |     try:
350 |         temp_dir = "/tmp/dill_package"
    |                    ^^^^^^^^^^^^^^^^^^^ S108
351 |         os.makedirs(temp_dir, exist_ok=True)
    |

~oolboxv2\utils\system\api.py:761:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
759 |                     with open(PERSISTENT_FD_FILE) as f_fd: fd_val = f_fd.read().strip()
760 |                     print(f"  Listening FD (from file, POSIX only): {fd_val}")
761 |                 except Exception: pass
    |                 ^^^^^^^^^^^^^^^^^^^^^^ S110
762 |         else:
763 |             print("Server is STOPPED (or state inconsistent).")
    |

~oolboxv2\utils\system\cache.py:16:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
14 |     def get(self, key):
15 |         try:
16 |             with shelve.open(self.filename) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
17 |                 return db.get(key.replace('\x00', ''))
18 |         except Exception:
   |

~oolboxv2\utils\system\cache.py:23:18: S301 `pickle` and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue
   |
21 |     def set(self, key, value):
22 |         try:
23 |             with shelve.open(self.filename, writeback=True) as db:
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ S301
24 |                 db[key.replace('\x00', '')] = value
25 |         except Exception:
   |

~oolboxv2\utils\system\conda_runner.py:15:19: S602 `subprocess` call with `shell=True` identified, security issue
   |
13 |     if live:
14 |         # Using subprocess.Popen to stream stdout and stderr live
15 |         process = subprocess.Popen(command, shell=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
   |                   ^^^^^^^^^^^^^^^^ S602
16 |         process.communicate()  # Wait for the process to complete
17 |         return process.returncode == 0, None
   |

~oolboxv2\utils\system\conda_runner.py:21:18: S602 `subprocess` call with `shell=True` identified, security issue
   |
19 |     try:
20 |         # If not live, capture output and return it
21 |         result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True, encoding='cp850')
   |                  ^^^^^^^^^^^^^^ S602
22 |         return True, result.stdout
23 |     except subprocess.CalledProcessError as e:
   |

~oolboxv2\utils\system\file_handler.py:37:41: SIM115 Use a context manager for opening files
   |
35 |             self.file_handler_storage = None
36 |         try:
37 |             self.file_handler_storage = open(self.file_handler_file_prefix + self.file_handler_filename, mode)
   |                                         ^^^^ SIM115
38 |             self.file_handler_max_loaded_index_ += 1
39 |         except FileNotFoundError:
   |

~oolboxv2\utils\system\file_handler.py:60:16: B030 `except` handlers should only be exception classes or tuples of exception classes
   |
58 |                 self.file_handler_max_loaded_index_ = -1
59 |             rdu()
60 |         except OSError and PermissionError as e:
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
61 |             raise e
   |

~oolboxv2\utils\system\file_handler.py:111:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
109 |               )
110 |               return False
111 | /         if key not in self.file_handler_load:
112 | |             if key in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
113 |                   key = self.file_handler_key_mapper[key]
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\system\file_handler.py:148:16: B030 `except` handlers should only be exception classes or tuples of exception classes
    |
146 |                 self.file_handler_load[key] = self.decode_code(line)
147 |
148 |         except json.decoder.JSONDecodeError and Exception:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B030
149 |
150 |             for line in self.file_handler_storage:
    |

~oolboxv2\utils\system\file_handler.py:166:9: SIM102 Use a single `if` statement instead of nested `if` statements
    |
164 |       def get_file_handler(self, obj: str, default=None) -> str or None:
165 |           logger = get_logger()
166 | /         if obj not in self.file_handler_load:
167 | |             if obj in self.file_handler_key_mapper:
    | |___________________________________________________^ SIM102
168 |                   obj = self.file_handler_key_mapper[obj]
169 |           logger.info(Style.ITALIC(Style.GREY(f"Collecting data from storage key : {obj}")))
    |
    = help: Combine `if` statements using `and`

~oolboxv2\utils\system\getting_and_closing_app.py:15:5: SIM102 Use a single `if` statement instead of nested `if` statements
   |
13 |   def override_main_app(app):
14 |       global registered_apps
15 | /     if registered_apps[0] is not None:
16 | |         if time.time() - registered_apps[0].called_exit[1] > 30:
   | |________________________________________________________________^ SIM102
17 |               raise PermissionError("Permission denied because of overtime fuction override_main_app sud only be called "
18 |                                     f"once and ontime overtime {time.time() - registered_apps[0].called_exit[1]}")
   |
   = help: Combine `if` statements using `and`

~oolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^^^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

~oolboxv2\utils\system\getting_and_closing_app.py:26:41: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
26 | def get_app(from_=None, name=None, args=AppArgs().default(), app_con=None, sync=False) -> AppType:
   |                                         ^^^^^^^^^ B008
27 |     global registered_apps
28 |     # name = None
   |

~oolboxv2\utils\system\ipy_completer.py:64:9: SIM102 Use a single `if` statement instead of nested `if` statements
   |
62 |       for _name, obj in inspect.getmembers(module, inspect.isclass):
63 |           # Check if the class is defined in the current module
64 | /         if obj.__module__ == module.__name__:
65 | |             # Check if the class is a dataclass
66 | |             if is_dataclass(obj):
   | |_________________________________^ SIM102
67 |                   dataclasses.append(obj)
   |
   = help: Combine `if` statements using `and`

~oolboxv2\utils\system\main_tool.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import asyncio
 2 | | import inspect
 3 | | import os
 4 | | from toolboxv2.utils.extras import Style
 5 | |
 6 | | from .getting_and_closing_app import get_app
 7 | | from .tb_logger import get_logger
 8 | | from .types import Result, ToolBoxError, ToolBoxInfo, ToolBoxInterfaces, ToolBoxResult
   | |______________________________________________________________________________________^ I001
 9 |
10 |   try:
   |
   = help: Organize imports

~oolboxv2\utils\system\session.py:265:24: S113 Probable use of `requests` call without timeout
    |
263 |             except Exception as e:
264 |                 print("Error session fetch:", e, self.username)
265 |                 return requests.request(method, url, data=data)
    |                        ^^^^^^^^^^^^^^^^ S113
266 |         else:
267 |             print(f"Could not find session using request on {url}")
    |

~oolboxv2\utils\system\session.py:269:24: S113 Probable use of `requests` call without timeout
    |
267 |             print(f"Could not find session using request on {url}")
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
    |                        ^^^^^^^^^^^^^^^^ S113
270 |             return requests.request(method, url, data=data)
271 |             # raise Exception("Session not initialized. Please login first.")
    |

~oolboxv2\utils\system\session.py:270:20: S113 Probable use of `requests` call without timeout
    |
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
270 |             return requests.request(method, url, data=data)
    |                    ^^^^^^^^^^^^^^^^ S113
271 |             # raise Exception("Session not initialized. Please login first.")
    |

~oolboxv2\utils\system\session.py:351:20: S113 Probable use of `requests` call without timeout
    |
349 | def get_public_ip():
350 |     try:
351 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^ S113
352 |         ip_address = response.json()['ip']
353 |         return ip_address
    |

~oolboxv2\utils\system\tb_logger.py:56:29: S307 Use of possibly insecure function; consider using `ast.literal_eval`
   |
54 |         log_info_data_str = li.read()
55 |         try:
56 |             log_info_data = eval(log_info_data_str)
   |                             ^^^^^^^^^^^^^^^^^^^^^^^ S307
57 |         except SyntaxError:
58 |             if log_info_data_str:
   |

~oolboxv2\utils\system\types.py:20:1: F403 `from .all_functions_enums import *` used; unable to detect undefined names
   |
18 | from ..extras import generate_test_cases
19 | from ..extras.Style import Spinner
20 | from .all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F403
21 | from .file_handler import FileHandler
   |

~oolboxv2\utils\system\types.py:482:25: F405 `Enum` may be undefined, or defined from star imports
    |
482 | class ToolBoxError(str, Enum):
    |                         ^^^^ F405
483 |     none = "none"
484 |     input_error = "InputError"
    |

~oolboxv2\utils\system\types.py:489:30: F405 `Enum` may be undefined, or defined from star imports
    |
489 | class ToolBoxInterfaces(str, Enum):
    |                              ^^^^ F405
490 |     cli = "CLI"
491 |     api = "API"
    |

~oolboxv2\utils\system\types.py:532:62: F405 `Enum` may be undefined, or defined from star imports
    |
530 |     def as_result(self):
531 |         return Result(
532 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
533 |             result=ToolBoxResult(
534 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

~oolboxv2\utils\system\types.py:534:86: F405 `Enum` may be undefined, or defined from star imports
    |
532 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
533 |             result=ToolBoxResult(
534 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
535 |                 data_info=self.result.data_info,
536 |                 data=self.result.data,
    |

~oolboxv2\utils\system\types.py:574:64: F405 `Enum` may be undefined, or defined from star imports
    |
572 |     def as_dict(self):
573 |         return {
574 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                                ^^^^ F405
575 |         "result" : {
576 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

~oolboxv2\utils\system\types.py:576:84: F405 `Enum` may be undefined, or defined from star imports
    |
574 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
575 |         "result" : {
576 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                    ^^^^ F405
577 |             "data_info":self.result.data_info,
578 |             "data":self.result.data,
    |

~oolboxv2\utils\system\types.py:611:62: F405 `Enum` may be undefined, or defined from star imports
    |
609 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
610 |         return ApiResult(
611 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^ F405
612 |             result=ToolBoxResultBM(
613 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

~oolboxv2\utils\system\types.py:613:86: F405 `Enum` may be undefined, or defined from star imports
    |
611 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
612 |             result=ToolBoxResultBM(
613 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^ F405
614 |                 data_info=self.result.data_info,
615 |                 data=self.result.data,
    |

~oolboxv2\utils\system\types.py:633:46: F405 `Enum` may be undefined, or defined from star imports
    |
631 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
632 |         return ApiResult(
633 |             error=error if isinstance(error, Enum) else error,
    |                                              ^^^^ F405
634 |             result=ToolBoxResultBM(
635 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |

~oolboxv2\utils\system\types.py:635:84: F405 `Enum` may be undefined, or defined from star imports
    |
633 |             error=error if isinstance(error, Enum) else error,
634 |             result=ToolBoxResultBM(
635 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |                                                                                    ^^^^ F405
636 |                 data_info=result.get('data_info', '404'),
637 |                 data=result.get('data'),
    |

~oolboxv2\utils\system\types.py:884:45: F405 `Enum` may be undefined, or defined from star imports
    |
882 |     async def aget(self, key=None, default=None):
883 |         if asyncio.isfuture(self.result.data) or asyncio.iscoroutine(self.result.data) or (
884 |             isinstance(self.result.data_to, Enum) and self.result.data_to.name == ToolBoxInterfaces.future.name):
    |                                             ^^^^ F405
885 |             data = await self.result.data
886 |         else:
    |

~oolboxv2\utils\system\types.py:1034:35: F405 `CLOUDM_AUTHMANAGER` may be undefined, or defined from star imports
     |
1033 |     async def get_user(self, username: str) -> Result:
1034 |         return self.app.a_run_any(CLOUDM_AUTHMANAGER.GET_USER_BY_NAME, username=username, get_results=True)
     |                                   ^^^^^^^^^^^^^^^^^^ F405
     |

~oolboxv2\utils\system\types.py:1192:29: F405 `Enum` may be undefined, or defined from star imports
     |
1191 |     def _get_function(self,
1192 |                       name: Enum or None,
     |                             ^^^^ F405
1193 |                       state: bool = True,
1194 |                       specification: str = "app",
     |

~oolboxv2\utils\system\types.py:1251:34: F405 `Enum` may be undefined, or defined from star imports
     |
1249 |         """proxi attr"""
1250 |
1251 |     def get_function(self, name: Enum or tuple, **kwargs):
     |                                  ^^^^ F405
1252 |         """
1253 |         Kwargs for _get_function
     |

~oolboxv2\utils\system\types.py:1266:47: F405 `Enum` may be undefined, or defined from star imports
     |
1264 |         """
1265 |
1266 |     def run_function(self, mod_function_name: Enum or tuple,
     |                                               ^^^^ F405
1267 |                      tb_run_function_with_state=True,
1268 |                      tb_run_with_specification='app',
     |

~oolboxv2\utils\system\types.py:1276:55: F405 `Enum` may be undefined, or defined from star imports
     |
1274 |         """proxi attr"""
1275 |
1276 |     async def a_run_function(self, mod_function_name: Enum or tuple,
     |                                                       ^^^^ F405
1277 |                              tb_run_function_with_state=True,
1278 |                              tb_run_with_specification='app',
     |

~oolboxv2\utils\system\types.py:1306:49: F405 `Enum` may be undefined, or defined from star imports
     |
1304 |         """
1305 |
1306 |     async def run_http(self, mod_function_name: Enum or str or tuple, function_name=None, method="GET",
     |                                                 ^^^^ F405
1307 |                        args_=None,
1308 |                        kwargs_=None,
     |

~oolboxv2\utils\system\types.py:1312:42: F405 `Enum` may be undefined, or defined from star imports
     |
1310 |         """run a function remote via http / https"""
1311 |
1312 |     def run_any(self, mod_function_name: Enum or str or tuple, backwords_compability_variabel_string_holder=None,
     |                                          ^^^^ F405
1313 |                 get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
1314 |                 kwargs_=None,
     |

~oolboxv2\utils\system\types.py:1318:50: F405 `Enum` may be undefined, or defined from star imports
     |
1316 |         """proxi attr"""
1317 |
1318 |     async def a_run_any(self, mod_function_name: Enum or str or tuple,
     |                                                  ^^^^ F405
1319 |                         backwords_compability_variabel_string_holder=None,
1320 |                         get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
     |

~oolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^^^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

~oolboxv2\utils\toolbox.py:45:47: B008 Do not perform function call `AppArgs` in argument defaults; instead, perform the call within the function, or read the default from a module-level singleton variable
   |
43 | class App(AppType, metaclass=Singleton):
44 |
45 |     def __init__(self, prefix: str = "", args=AppArgs().default()):
   |                                               ^^^^^^^^^ B008
46 |         super().__init__(prefix, args)
47 |         self._web_context = None
   |

~oolboxv2\utils\toolbox.py:329:9: S605 Starting a process with a shell, possible injection detected
    |
327 |             return
328 |         self.print(f"Installing {module_name} GREEDY")
329 |         os.system(f"{sys.executable} -m pip install {module_name}")
    |         ^^^^^^^^^ S605
330 |
331 |     def python_module_import_classifier(self, mod_name, error_message):
    |

~oolboxv2\utils\toolbox.py:689:17: S110 `try`-`except`-`pass` detected, consider logging the exception
    |
687 |                           # Allow tasks time to clean up
688 |                           loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
689 | /                 except Exception:
690 | |                     pass
    | |________________________^ S110
691 |
692 |                   loop.close()
    |

~oolboxv2\utils\toolbox.py:879:60: B023 Function definition does not bind loop variable `result`
    |
877 | …                     async def _():
878 | …                         try:
879 | …                             if asyncio.iscoroutine(result):
    |                                                      ^^^^^^ B023
880 | …                                 await result
881 | …                             if hasattr(result, 'Name'):
    |

~oolboxv2\utils\toolbox.py:880:47: B023 Function definition does not bind loop variable `result`
    |
878 | …                     try:
879 | …                         if asyncio.iscoroutine(result):
880 | …                             await result
    |                                     ^^^^^^ B023
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
    |

~oolboxv2\utils\toolbox.py:881:48: B023 Function definition does not bind loop variable `result`
    |
879 | …                     if asyncio.iscoroutine(result):
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
    |                                  ^^^^^^ B023
882 | …                         print('Opened :', result.Name)
883 | …                     elif hasattr(result, 'name'):
    |

~oolboxv2\utils\toolbox.py:882:59: B023 Function definition does not bind loop variable `result`
    |
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
882 | …                         print('Opened :', result.Name)
    |                                             ^^^^^^ B023
883 | …                     elif hasattr(result, 'name'):
884 | …                         print('Opened :', result.name)
    |

~oolboxv2\utils\toolbox.py:883:50: B023 Function definition does not bind loop variable `result`
    |
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
    |                                        ^^^^^^ B023
884 | …                             print('Opened :', result.name)
885 | …                     except Exception as e:
    |

~oolboxv2\utils\toolbox.py:884:59: B023 Function definition does not bind loop variable `result`
    |
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
884 | …                             print('Opened :', result.name)
    |                                                 ^^^^^^ B023
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
    |

~oolboxv2\utils\toolbox.py:887:48: B023 Function definition does not bind loop variable `result`
    |
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
887 | …                         if hasattr(result, 'Name'):
    |                                      ^^^^^^ B023
888 | …                             print('Error opening :', result.Name)
889 | …                         elif hasattr(result, 'name'):
    |

~oolboxv2\utils\toolbox.py:888:66: B023 Function definition does not bind loop variable `result`
    |
886 | …                     self.debug_rains(e)
887 | …                     if hasattr(result, 'Name'):
888 | …                         print('Error opening :', result.Name)
    |                                                    ^^^^^^ B023
889 | …                     elif hasattr(result, 'name'):
890 | …                         print('Error opening :', result.name)
    |

~oolboxv2\utils\toolbox.py:889:50: B023 Function definition does not bind loop variable `result`
    |
887 | …                             if hasattr(result, 'Name'):
888 | …                                 print('Error opening :', result.Name)
889 | …                             elif hasattr(result, 'name'):
    |                                            ^^^^^^ B023
890 | …                                 print('Error opening :', result.name)
891 | …                     asyncio.create_task(_())
    |

~oolboxv2\utils\toolbox.py:890:66: B023 Function definition does not bind loop variable `result`
    |
888 |                                         print('Error opening :', result.Name)
889 |                                     elif hasattr(result, 'name'):
890 |                                         print('Error opening :', result.name)
    |                                                                  ^^^^^^ B023
891 |                             asyncio.create_task(_())
892 |                         else:
    |

~oolboxv2\utils\toolbox.py:1575:33: SIM115 Use a context manager for opening files
     |
1573 |     def web_context(self):
1574 |         if self._web_context is None:
1575 |             self._web_context = open("./dist/helper.html", encoding="utf-8").read()
     |                                 ^^^^ SIM115
1576 |         return self._web_context
     |

~oolboxv2\utils\toolbox.py:1639:21: S102 Use of `exec` detected
     |
1637 |                     code = compile(source, module.__file__, 'exec')
1638 |                     # Execute the code in the module's namespace
1639 |                     exec(code, module.__dict__)
     |                     ^^^^ S102
1640 |                 except Exception:
1641 |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
     |

~oolboxv2\utils\toolbox.py:1640:17: S110 `try`-`except`-`pass` detected, consider logging the exception
     |
1638 |                       # Execute the code in the module's namespace
1639 |                       exec(code, module.__dict__)
1640 | /                 except Exception:
1641 | |                     # print(f"No source for {str(module_name).split('from')[0]}: {e}")
1642 | |                     pass
     | |________________________^ S110
1643 |                   return module
     |

Found 616 errors.
[*] 37 fixable with the `--fix` option (4 hidden fixes can be enabled with the `--unsafe-fixes` option).

[Safety] Exit Code: 1
[Safety] Output:
[31mUnhandled exception happened: 'charmap' codec can't encode character '\u2011' in position 2694: character maps to <undefined>[0m
[0m

[Versions] Exit Code: 0
[Versions] Output:
Starting ToolBox as main from : [1m[36mC:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2[0m[0m
[33m[1mCould not rename log file appending on Logs-toolbox-main-2025-05-24-ERROR[0m[0m
Logger in Default
================================
[36mSystem$main-DESKTOP-CI57V1L:[0m Infos:
  Name     -> DESKTOP-CI57V1L
  ID       -> main-DESKTOP-CI57V1L
  Version  -> 0.1.21

LOADING ALL MODS FROM FOLDER : mods
Opened : FileWidget
Opened : CodeVerification
Opened : <module 'toolboxv2.mods.TruthSeeker' from 'C:\\Users\\<USER>\\Workspace\\ToolBoxV2\\toolboxv2\\mods\\TruthSeeker\\__init__.py'>
Opened : talk
Opened : WhatsAppTb
Opened : MinimalHtml
Opened : ProcessManager
Opened : TestWidget

**************************************************************************
Opened : welcome
Pers�nlicher Organisationsassistent Modul (POA v0.1.0) initialisiert.
Opened : SocketManager
Opened : EventManager
SchedulerManager try loading from file
SchedulerManager Successfully loaded
STARTING SchedulerManager
Opened : SchedulerManager
Opened : WebSocketManager
Overriding function delete from DB
Opened : cli_functions
Opened : FastApi
Overriding function Version from DB
Opened : DB
Opened : WidgetsProvider.BoardWidget
Overriding function Version from CloudM
Overriding function show_version from CloudM
ADDING DoNext
Overriding function Version from CloudM
Opened : DoNext
Overriding function show_version from CloudM
Overriding function show_version from CloudM
Overriding function get_mod_snapshot from CloudM
ADDING POA
Overriding function get_mod_snapshot from CloudM
Opened : CloudM
Function POA On start result: Function Exec code: 0
Info's: POA Modul bereit. <|> 
NO Origin
Opened : POA
[Taichi] version 1.7.3, llvm 15.0.1, commit 5ec301be, win, python 3.12.9
INFO     [browser_use] BrowserUse logging setup complete with level info
[95misaa[0m: Start app.isaa
Spinner Manager not in the min Thread no signal possible

[KOpened : isaa

[KOpened 27 modules in 8.50s

------------------ Version ------------------

[1m[36m[3mRE[0m[0m[0m[3mSimple[0mToolBox:  0.1.21  

         CodeVerification          :  0.0.1   
            FileWidget             :  1.0.0   
              DoNext               :  0.1.21  
            TruthSeeker            : unknown  
               talk                :  0.0.1   
            WhatsAppTb             : unknown  
            MinimalHtml            :  0.0.2   
          ProcessManager           :  0.0.1   
            TestWidget             :  0.0.1   
                POA                :  0.1.0   
              welcome              :  0.1.21  
           SocketManager           :  0.1.9   
           EventManager            :  0.0.3   
         SchedulerManager          :  0.0.2   
         WebSocketManager          :  0.0.3   
                DB                 :  0.0.3   
           cli_functions           :  0.0.1   
              FastApi              :  0.2.2   
       CloudM.email_services       :  0.1.0   
        CloudM.AuthManager         :  0.1.21  
              CloudM               :  0.0.3   
       CloudM.UserInstances        :  0.0.2   
   CloudM.UI.UserAccountManager    :  0.0.1   
         CloudM.UI.widget          :  0.0.1   
          WidgetsProvider          :  0.0.1   
    WidgetsProvider.BoardWidget    :  0.0.1   
               isaa                :  0.1.5   



[K
Building State data:   0%|          | 0/6 [00:00<?, ?chunk/s]
                                                             

Building State data:   0%|          | 0/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  17%|#6        | 1/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  33%|###3      | 2/6 [00:00<?, ?chunk/s]
                                                             

Building State data:  50%|#####     | 3/6 [00:00<?, ?chunk/s]working on utils files
working on api files
working on app files
working on mods files
ADDING DoNext
Overriding function get_widget from FileWidget
Overriding function upload from FileWidget
Overriding function download from FileWidget
Overriding function files from FileWidget
Overriding function Version from MinimalHtml
Overriding function add_group from MinimalHtml
Function On Exit result: saved 0 jobs in C:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2\.data\main-DESKTOP-CI57V1L/jobs.compact
Overriding function add_collection_to_group from MinimalHtml
[1m[3m- end -[0m[0m

