# ToolBox Password Manager

A comprehensive password management system integrated into the ToolBoxV2 framework with browser extension support, device key encryption, and automatic 2FA capabilities.

## Features

### 🔐 Core Password Management
- **Secure Storage**: Passwords encrypted with device keys using AES-GCM encryption
- **Blob Database**: Distributed storage using BlobDB with Reed-Solomon error correction
- **Password Generation**: Cryptographically secure password generation with customizable options
- **Auto-fill**: Intelligent form detection and automatic password filling
- **Search & Organization**: Full-text search with folder organization and tagging

### 🔑 Import & Export
- **Universal Import**: Support for Chrome, Firefox, LastPass, Bitwarden, 1Password, and generic CSV/JSON formats
- **Batch Processing**: Import thousands of passwords with progress tracking and error reporting
- **Export Options**: Secure export to CSV or JSON formats (passwords masked for security)
- **Format Detection**: Automatic detection and parsing of different password manager formats

### 🛡️ Two-Factor Authentication (2FA)
- **TOTP Support**: Time-based One-Time Password generation and management
- **QR Code Parsing**: Automatic parsing of 2FA QR codes and setup URIs
- **Auto-Generation**: Automatic 2FA code generation during login
- **Multiple Accounts**: Support for multiple 2FA accounts per password entry

### 🌐 Browser Extension Integration
- **Context Menus**: Right-click access to password manager functions
- **Keyboard Shortcuts**: Quick access via customizable keyboard shortcuts
- **Auto-fill Detection**: Automatic detection of login forms
- **Real-time Sync**: Instant synchronization across devices
- **Secure Communication**: Encrypted communication between extension and server

## Architecture

### Data Structure

```python
@dataclass
class PasswordEntry:
    id: str                          # Unique identifier
    url: str                         # Website URL
    username: str                    # Username/email
    password: str                    # Encrypted password
    title: str                       # Display title
    notes: str                       # Additional notes
    totp_secret: str                 # 2FA secret (encrypted)
    totp_issuer: str                 # 2FA issuer name
    totp_account: str                # 2FA account name
    folder: str                      # Organization folder
    tags: List[str]                  # Tags for categorization
    favorite: bool                   # Favorite flag
    created_at: float                # Creation timestamp
    updated_at: float                # Last update timestamp
    last_used: float                 # Last access timestamp
    password_history: List[Dict]     # Previous passwords
    custom_fields: Dict[str, str]    # Custom field data
    breach_detected: bool            # Security breach flag
    auto_fill_enabled: bool          # Auto-fill preference
```

### Security Model

1. **Device Key Encryption**: All passwords encrypted with device-specific keys
2. **Blob Storage**: Distributed storage with redundancy and error correction
3. **Zero-Knowledge**: Server never sees unencrypted passwords
4. **Session Security**: Secure session management with timeouts
5. **Breach Detection**: Integration with breach databases for security alerts

## Installation & Setup

### 1. Install Dependencies

```bash
# Install required Python packages
pip install cryptography flask werkzeug

# Install browser extension
# Load unpacked extension from toolboxv2/tb_browser_extension/
```

### 2. Initialize Password Manager

```python
from toolboxv2 import get_app
from toolboxv2.mods.PasswordManager import PasswordManagerCore

app = get_app()
pm = PasswordManagerCore(app)
```

### 3. Start ToolBox Server

The password manager uses ToolBox's built-in API system - no additional server needed!

```python
# Just start ToolBox normally - password manager APIs are automatically available
from toolboxv2 import App
app = App()
app.run()  # Password manager APIs available at http://localhost:8080/api/call/PasswordManager/
```

## Usage Examples

### Adding Passwords

```python
from toolboxv2.mods.PasswordManager import add_password

# Add a new password entry
result = add_password(
    app,
    url="https://example.com",
    username="<EMAIL>",
    password="secure_password_123",
    title="Example Website",
    notes="Work account",
    folder="Work"
)
```

### Importing Passwords

```python
from toolboxv2.mods.PasswordManager import import_passwords

# Import from Chrome CSV export
with open('chrome_passwords.csv', 'r') as f:
    csv_content = f.read()

result = import_passwords(
    app,
    file_content=csv_content,
    file_format="chrome",
    folder="Imported from Chrome"
)

print(f"Imported: {result.get()['imported_count']} passwords")
```

### 2FA Management

```python
from toolboxv2.mods.PasswordManager import add_totp_secret, generate_totp_code

# Add 2FA secret to existing password entry
result = add_totp_secret(
    app,
    entry_id="password_entry_id",
    secret="JBSWY3DPEHPK3PXP",
    issuer="Example Corp",
    account="<EMAIL>"
)

# Generate current TOTP code
totp_result = generate_totp_code(app, "password_entry_id")
current_code = totp_result.get()['code']
print(f"Current 2FA code: {current_code}")
```

### Password Generation

```python
from toolboxv2.mods.PasswordManager import generate_password

# Generate secure password
result = generate_password(
    app,
    length=20,
    include_symbols=True,
    include_numbers=True,
    include_uppercase=True,
    include_lowercase=True,
    exclude_ambiguous=True
)

secure_password = result.get()['password']
```

## Browser Extension Usage

### Keyboard Shortcuts
- `Ctrl+Shift+P`: Open Password Manager
- `Ctrl+Shift+F`: Auto-fill current form
- `Ctrl+Shift+G`: Generate secure password

### Context Menu Options
- **Password Manager** → **Import Passwords**: Import from other password managers
- **Password Manager** → **View Passwords**: Browse and manage stored passwords
- **Password Manager** → **Auto-fill Login**: Fill current login form

### Auto-fill Features
1. **Automatic Detection**: Forms are automatically detected on page load
2. **Smart Matching**: Passwords matched by domain and username
3. **2FA Integration**: TOTP codes automatically generated and displayed
4. **Multiple Accounts**: Choose from multiple accounts for the same site

## API Endpoints

The password manager uses ToolBox's built-in API system. All functions are available at:
`http://localhost:8080/api/PasswordManager/{function_name}`

### Available Functions
- `add_password` - Add new password entry
- `get_password` - Get password by ID
- `search_passwords` - Search password entries
- `list_passwords` - List all passwords
- `generate_password` - Generate secure password
- `import_passwords` - Import from various formats
- `generate_totp_code` - Generate 2FA code
- `add_totp_secret` - Add 2FA secret to entry
- `parse_totp_qr_code` - Parse 2FA QR codes
- `get_password_for_autofill` - Get password for browser autofill

See `docs/PasswordManager_API.md` for detailed API documentation with examples.

## Security Considerations

### Encryption
- All passwords encrypted with AES-GCM using device-specific keys
- TOTP secrets encrypted separately with additional key derivation
- Blob storage provides additional encryption layer

### Access Control
- Device key required for all operations
- Session timeouts prevent unauthorized access
- Secure communication between browser extension and server

### Best Practices
1. **Regular Backups**: Export encrypted backups regularly
2. **Strong Master Password**: Use strong device authentication
3. **2FA Everywhere**: Enable 2FA for all supported accounts
4. **Regular Updates**: Keep extension and server updated
5. **Breach Monitoring**: Monitor for password breaches

## Testing

Run the comprehensive test suite:

```bash
cd toolboxv2
python -m pytest tests/test_password_manager.py -v
```

Test coverage includes:
- Password entry CRUD operations
- Import/export functionality
- 2FA code generation and validation
- Browser extension integration
- API endpoint testing
- Security and encryption validation

## Troubleshooting

### Common Issues

1. **Import Failures**
   - Check file format matches selected type
   - Ensure CSV headers are correct
   - Verify file encoding (UTF-8 recommended)

2. **2FA Not Working**
   - Verify TOTP secret is correctly formatted
   - Check system time synchronization
   - Ensure QR code parsing was successful

3. **Auto-fill Issues**
   - Check if forms are properly detected
   - Verify URL matching is working
   - Ensure extension has necessary permissions

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Start with debug enabled
start_api_server(app, debug=True)
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit pull request

## License

This password manager is part of the ToolBoxV2 framework and follows the same licensing terms.
