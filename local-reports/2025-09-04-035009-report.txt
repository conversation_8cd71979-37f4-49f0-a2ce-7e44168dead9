Quality Check Report - 2025-09-04-035009
Commit: 82014574755785450b0f0efc1428a43af7e352b3
========================================

[Deptry] Exit Code: 2
[Deptry] Output:
Usage: deptry [OPTIONS] ROOT...
Try 'deptry --help' for help.

Error: Invalid value for 'ROOT...': Path '.toolboxv2\\' does not exist.

[Ruff] Exit Code: 0
[Ruff] Output:
warning: Failed to lint .toolboxv2: Das System kann die angegebene Datei nicht finden. (os error 2)
All checks passed!

[Safety] Exit Code: 1
[Safety] Output:

Please login or register Safety CLI (free forever) to scan and secure your 
projects with Safety

(R)egister for a free account in 30 seconds, or (L)ogin with an existing 
account to continue (R/L): Unhandled exception happened: EOF when reading a line

[Versions] Exit Code: 0
[Versions] Output:
Starting ToolBox as main from : [1m[36mC:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2[0m[0m
[33m[1mCould not rename log file appending on Logs-toolbox-main-2025-09-04-ERROR[0m[0m
Logger in Default
================================
[36mSystem$main-DESKTOP-CI57V1L:[0m Infos:
  Name     -> DESKTOP-CI57V1L
  ID       -> main-DESKTOP-CI57V1L
  Version  -> 0.1.23


------------------ Version ------------------

[1m[36m[3mRE[0m[0m[0m[3mSimple[0mToolBox:  0.1.23  



[1m[3m- end -[0m[0m

