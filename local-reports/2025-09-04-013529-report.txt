Quality Check Report - 2025-09-04-013529
Commit: f884f1a0403b9f9da881d554a6c3008262bbfe5a
========================================

[Deptry] Exit Code: 2
[Deptry] Output:
Usage: deptry [OPTIONS] ROOT...
Try 'deptry --help' for help.

Error: Invalid value for 'ROOT...': Path '.toolboxv2\\' does not exist.

[Ruff] Exit Code: 0
[Ruff] Output:
warning: Failed to lint .toolboxv2: Das System kann die angegebene Datei nicht finden. (os error 2)
All checks passed!

[Safety] Exit Code: 1
[Safety] Output:
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Scripts\safety.exe\__main__.py", line 4, in <module>
  File "C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\safety\cli.py", line 593, in <module>
    typer.rich_utils.STYLE_HELPTEXT = ""
    ^^^^^^^^^^^^^^^^
AttributeError: module 'typer' has no attribute 'rich_utils'

[Versions] Exit Code: 0
[Versions] Output:
Starting ToolBox as main from : [1m[36mC:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2[0m[0m
Logger in Default
================================
[36mSystem$main-DESKTOP-CI57V1L:[0m Infos:
  Name     -> DESKTOP-CI57V1L
  ID       -> main-DESKTOP-CI57V1L
  Version  -> 0.1.23


------------------ Version ------------------

[1m[36m[3mRE[0m[0m[0m[3mSimple[0mToolBox:  0.1.23  



[1m[3m- end -[0m[0m

