# ToolBox Password Manager API

The Password Manager is fully integrated with ToolBox's existing API system. All functions are automatically available over HTTP without any additional dependencies.

## API Endpoints

All password manager functions are available at:
```
http://localhost:8080/api/PasswordManager/{function_name}
```

### Available Functions

#### 1. Add Password
```http
POST http://localhost:8080/api/PasswordManager/add_password
Content-Type: application/json

{
    "url": "https://example.com",
    "username": "<EMAIL>",
    "password": "secure_password_123",
    "title": "Example Website",
    "notes": "Work account",
    "folder": "Work"
}
```

#### 2. Get Password
```http
POST http://localhost:8080/api/PasswordManager/get_password
Content-Type: application/json

{
    "entry_id": "abc123def456"
}
```

#### 3. Search Passwords
```http
POST http://localhost:8080/api/PasswordManager/search_passwords
Content-Type: application/json

{
    "query": "example.com",
    "limit": 50
}
```

#### 4. List Passwords
```http
POST http://localhost:8080/api/PasswordManager/list_passwords
Content-Type: application/json

{
    "folder": "Work",
    "limit": 100
}
```

#### 5. Generate Password
```http
POST http://localhost:8080/api/PasswordManager/generate_password
Content-Type: application/json

{
    "length": 16,
    "include_symbols": true,
    "include_numbers": true,
    "include_uppercase": true,
    "include_lowercase": true,
    "exclude_ambiguous": true
}
```

#### 6. Import Passwords
```http
POST http://localhost:8080/api/PasswordManager/import_passwords
Content-Type: application/json

{
    "file_content": "url,username,password\nhttps://example.com,user,pass123",
    "file_format": "csv",
    "folder": "Imported"
}
```

#### 7. Generate TOTP Code
```http
POST http://localhost:8080/api/PasswordManager/generate_totp_code
Content-Type: application/json

{
    "entry_id": "abc123def456"
}
```

#### 8. Add TOTP Secret
```http
POST http://localhost:8080/api/PasswordManager/add_totp_secret
Content-Type: application/json

{
    "entry_id": "abc123def456",
    "secret": "JBSWY3DPEHPK3PXP",
    "issuer": "Example Corp",
    "account": "<EMAIL>"
}
```

#### 9. Parse TOTP QR Code
```http
POST http://localhost:8080/api/PasswordManager/parse_totp_qr_code
Content-Type: application/json

{
    "qr_data": "otpauth://totp/Example:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Example"
}
```

#### 10. Get Password for Autofill
```http
POST http://localhost:8080/api/PasswordManager/get_password_for_autofill
Content-Type: application/json

{
    "url": "https://example.com",
    "username": "<EMAIL>"
}
```

## Browser Extension Integration

The browser extension uses these same APIs through ToolBox's built-in API client:

```javascript
// Example: Auto-fill password using ToolBox API client
const response = await TB.api.request('PasswordManager', 'get_password_for_autofill', {
    url: window.location.href
});

if (response.get()) {
    const entry = response.get().entry;
    usernameField.value = entry.username;
    passwordField.value = entry.password;
}
```

## Response Format

All API calls return a standard ToolBox Result format:

```json
{
    "success": true,
    "data": {
        // Function-specific data
    },
    "info": "Operation completed successfully"
}
```

Error responses:
```json
{
    "success": false,
    "data": null,
    "info": "Error message describing what went wrong"
}
```

## Benefits of Using ToolBox's Built-in API

1. **No Additional Dependencies**: Uses existing ToolBox infrastructure
2. **Consistent Authentication**: Same auth system as all other ToolBox modules
3. **Automatic Documentation**: Functions are self-documenting through ToolBox
4. **Type Safety**: Full Python type hints and validation
5. **Error Handling**: Consistent error handling across all endpoints
6. **Logging**: Automatic logging and monitoring through ToolBox
7. **Security**: Built-in security features like rate limiting and validation

## Security

- All API calls require proper ToolBox authentication
- Passwords are encrypted with device keys before storage
- API responses never expose raw passwords in logs
- Session management handled by ToolBox core
- HTTPS recommended for production use

## Testing

You can test the API using curl:

```bash
# Generate a password
curl -X POST http://localhost:8080/api/PasswordManager/generate_password \
  -H "Content-Type: application/json" \
  -d '{"length": 20, "include_symbols": true}'

# Search passwords
curl -X POST http://localhost:8080/api/PasswordManager/search_passwords \
  -H "Content-Type: application/json" \
  -d '{"query": "example", "limit": 10}'
```

This approach eliminates the need for a separate API service while providing all the same functionality through ToolBox's proven, secure API infrastructure.
