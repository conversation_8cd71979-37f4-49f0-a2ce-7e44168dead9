Quality Check Report - 2025-09-02-170211
Commit: fb5446af85ab9d9aaaedfab4d1e265577f24b6d8
========================================

[Ruff] Exit Code: 1
[Ruff] Output:
I001 [*] Import block is un-sorted or un-formatted
  --> test.py:10:1
   |
 8 |   """
 9 |
10 | / import argparse
11 | | import re
12 | |
13 | | import asyncio
14 | | import json
15 | | import pathlib
16 | | import pickle
17 | | import subprocess
18 | | import sys
19 | | import tempfile
20 | | from typing import List, Dict, Any, Tuple
21 | |
22 | | import pandas as pd
23 | |
24 | | from toolboxv2 import Code
25 | | from toolboxv2.mods.isaa.base.AgentUtils import AISemanticMemory
26 | | from toolboxv2.utils.extras.blobs import BlobFile, BlobStorage
   | |______________________________________________________________^
   |
help: Organize imports

UP035 `typing.List` is deprecated, use `list` instead
  --> test.py:20:1
   |
18 | import sys
19 | import tempfile
20 | from typing import List, Dict, Any, Tuple
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
21 |
22 | import pandas as pd
   |

UP035 `typing.Dict` is deprecated, use `dict` instead
  --> test.py:20:1
   |
18 | import sys
19 | import tempfile
20 | from typing import List, Dict, Any, Tuple
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
21 |
22 | import pandas as pd
   |

UP035 `typing.Tuple` is deprecated, use `tuple` instead
  --> test.py:20:1
   |
18 | import sys
19 | import tempfile
20 | from typing import List, Dict, Any, Tuple
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
21 |
22 | import pandas as pd
   |

S105 Possible hardcoded password assigned to: "secret"
   --> test.py:118:59
    |
116 |         print("\n--- Verifying Recovered Data ---")
117 |         print(f"Decrypted content: {decrypted_secrets}")
118 |         assert json.loads(decrypted_secrets)["secret"] == "very_secret_stuff"
    |                                                           ^^^^^^^^^^^^^^^^^^^
119 |         print("✅ Verification successful: Data is intact.")
    |

I001 [*] Import block is un-sorted or un-formatted
   --> test.py:148:5
    |
147 |   if __name__ == "__main__4":
148 | /     import os
149 | |     from toolboxv2 import init_cwd
    | |__________________________________^
150 |
151 |       print(os.path.join(init_cwd, "mcp.json"))
    |
help: Organize imports

I001 [*] Import block is un-sorted or un-formatted
   --> test.py:168:5
    |
166 |       #}""")
167 |
168 | /     from toolboxv2 import get_app, init_cwd
169 | |     from toolboxv2.mods.isaa.module import Tools as Isaa
170 | |     import os
    | |_____________^
171 |
172 |       async def helper():
    |
help: Organize imports

I001 [*] Import block is un-sorted or un-formatted
   --> test.py:183:9
    |
181 |         agent = await isaa.get_agent(agent_name)
182 |
183 |         from toolboxv2.mods.isaa.extras.terminal_progress import ProgressiveTreePrinter, VerbosityMode
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
184 |         printer = ProgressiveTreePrinter(mode=VerbosityMode.VERBOSE)
185 |         agent.progress_callback = printer.progress_callback
    |
help: Organize imports

B007 Loop control variable `i` not used within loop body
   --> test.py:209:17
    |
207 |             escape_next = False  # NEW: Track if next char is escaped
208 |
209 |             for i, char in enumerate(args_str):
    |                 ^
210 |                 # NEW: Handle escape sequences
211 |                 if escape_next:
    |
help: Rename unused `i` to `_i`

I001 [*] Import block is un-sorted or un-formatted
   --> test.py:444:5
    |
442 |   if __name__ == "__main__8":
443 |
444 | /     import asyncio
445 | |     import random
446 | |     from enum import Enum
447 | |     from typing import Any, Union, List, Dict, Tuple, Optional
448 | |     from pydantic import BaseModel
449 | |     import copy
    | |_______________^
    |
help: Organize imports

UP035 `typing.List` is deprecated, use `list` instead
   --> test.py:447:5
    |
445 |     import random
446 |     from enum import Enum
447 |     from typing import Any, Union, List, Dict, Tuple, Optional
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
448 |     from pydantic import BaseModel
449 |     import copy
    |

UP035 `typing.Dict` is deprecated, use `dict` instead
   --> test.py:447:5
    |
445 |     import random
446 |     from enum import Enum
447 |     from typing import Any, Union, List, Dict, Tuple, Optional
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
448 |     from pydantic import BaseModel
449 |     import copy
    |

UP035 `typing.Tuple` is deprecated, use `tuple` instead
   --> test.py:447:5
    |
445 |     import random
446 |     from enum import Enum
447 |     from typing import Any, Union, List, Dict, Tuple, Optional
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
448 |     from pydantic import BaseModel
449 |     import copy
    |

SIM300 [*] Yoda condition detected
   --> test.py:477:39
    |
475 |                                  max_retries: int = 2):
476 |             print(f"FlowAgent {self.name} formatting class: {pydantic_model.__name__}")
477 |             return {"value": 'yes' if 0.5 > random.random() else 'no'}
    |                                       ^^^^^^^^^^^^^^^^^^^^^
478 |
479 |         def pause(self):
    |
help: Rewrite as `random.random() < 0.5`

UP007 [*] Use `X | Y` for type annotations
   --> test.py:669:38
    |
667 |             return chain
668 |
669 |         async def a_run(self, query: Union[str, BaseModel], use="auto"):
    |                                      ^^^^^^^^^^^^^^^^^^^^^
670 |             """Execute the chain asynchronously"""
671 |             current_data = query
    |
help: Convert to `X | Y`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> test.py:695:22
    |
693 |                     pass
694 |
695 |                 elif isinstance(task, (ParallelChain, ConditionalChain, ErrorHandlingChain)):
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
696 |                     current_data = await task.a_run(current_data)
    |
help: Convert to `X | Y`

UP007 [*] Use `X | Y` for type annotations
   --> test.py:721:30
    |
719 |                 return data
720 |
721 |         def run(self, query: Union[str, BaseModel], use="auto"):
    |                              ^^^^^^^^^^^^^^^^^^^^^
722 |             """Synchronous wrapper"""
723 |             return asyncio.run(self.a_run(query, use))
    |
help: Convert to `X | Y`

I001 [*] Import block is un-sorted or un-formatted
   --> test.py:873:5
    |
872 |   if __name__ == "__main__":
873 | /     import asyncio
874 | |     from toolboxv2 import get_app
    | |_________________________________^
    |
help: Organize imports

F403 `from toolboxv2.mods import *` used; unable to detect undefined names
   --> toolboxv2\__init__.py:120:5
    |
118 |     MODS_ERROR = None
119 |     import toolboxv2.mods
120 |     from toolboxv2.mods import *
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
121 | except ImportError as e:
122 |     MODS_ERROR = e
    |

F405 `mods` may be undefined, or defined from star imports
   --> toolboxv2\__init__.py:153:5
    |
151 |     "get_logger",
152 |     "flows_dict",
153 |     "mods",
    |     ^^^^^^
154 |     "get_app",
155 |     "TBEF",
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\__main__.py:115:5
    |
113 |     def profile_execute_all_functions(*args):
114 |         return print(args)
115 |     raise ValueError("Failed to import function for profiling")
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
116 |
117 | try:
    |

B018 Found useless expression. Either assign it to a variable or remove it.
    --> toolboxv2\__main__.py:1089:5
     |
1088 | """)
1089 |     ()
     |     ^^
1090 |     return c
     |

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\__main__.py:1173:9
     |
1171 |               print(f"Executable '{name_with_ext}' not found in standard locations. Build or download")
1172 |               return
1173 | /         if not 'bin' in str(gui_exe):
1174 | |             if gui_exe:
     | |_______________________^
1175 |                   bin_dir = tb_root_dir / "bin"
1176 |                   bin_dir.mkdir(exist_ok=True)
     |
help: Combine `if` statements using `and`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> toolboxv2\flows\chain.py:507:16
    |
505 |         """Build chain from components"""
506 |         if len(components) == 1:
507 |             if isinstance(components[0], (Chain, ParallelChain, ConditionalChain)):
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
508 |                 return components[0]
509 |             else:
    |
help: Convert to `X | Y`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\flows\chain.py:733:9
    |
732 |           # Check if chain already exists
733 | /         if self.storage.load_chain(name):
734 | |             if not confirm(f"Chain '{name}' already exists. Overwrite?"):
    | |_________________________________________________________________________^
735 |                   return
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\flows\chain.py:1141:13
     |
1140 |               # Check if chain exists
1141 | /             if self.storage.load_chain(chain_name):
1142 | |                 if not confirm(f"Chain '{chain_name}' already exists. Overwrite?"):
     | |___________________________________________________________________________________^
1143 |                       return
     |
help: Combine `if` statements using `and`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\flows\chain.py:1256:44
     |
1255 |             # Handle ParallelChain
1256 |             if hasattr(comp, 'agents') and isinstance(comp.agents, (list, tuple)):
     |                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1257 |                 return {
1258 |                     "type": "parallel",
     |
help: Convert to `X | Y`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\flows\chain.py:1286:43
     |
1285 |             # Handle Chain
1286 |             if hasattr(comp, 'tasks') and isinstance(comp.tasks, (list, tuple)):
     |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1287 |                 return {
1288 |                     "type": "chain",
     |
help: Convert to `X | Y`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\flows\chain.py:1473:21
     |
1471 |                     return await self.isaa.get_agent(agent_name)
1472 |                 except Exception as e:
1473 |                     raise Exception(f"Failed to load agent '{agent_name}': {e}")
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1474 |
1475 |             # Handle CF (Chain Format)
     |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\flows\chain.py:1524:21
     |
1523 |                 except Exception as e:
1524 |                     raise Exception(f"Failed to reconstruct format model '{model_name}': {e}")
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1525 |
1526 |             # Handle IS (Conditional)
     |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\flows\chain.py:1593:20
     |
1591 |             main_chain = await deserialize_component(structure)
1592 |
1593 |             if not isinstance(main_chain, (Chain, ParallelChain, ConditionalChain, ErrorHandlingChain)):
     |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1594 |                 # Wrap single components in a Chain
1595 |                 if main_chain:
     |
help: Convert to `X | Y`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\flows\chain.py:1603:13
     |
1602 |         except Exception as e:
1603 |             raise Exception(f"Failed to reconstruct chain: {e}")
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1604 |
1605 |     def _print_execution_stats(self, events: list[ProgressEvent]):
     |

SIM118 Use `key in dict` instead of `key in dict.keys()`
    --> toolboxv2\flows\isaa_cli.py:1186:84
     |
1184 |                 # Tools
1185 |                 if hasattr(agent, '_tool_registry') and agent.tool_registry:
1186 |                     tool_names = [tool.name if hasattr(tool, 'name') else tool for tool in agent.tool_registry.keys()]
     |                                                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1187 |                     output_lines.append(f"\n   {Style.Underlined('Tools')}:")
1188 |                     if tool_names:
     |
help: Remove `.keys()`

B025 try-except block with duplicate exception `asyncio.CancelledError`
    --> toolboxv2\flows\isaa_cli.py:2101:25
     |
2099 |                     self.session_stats["agents"][agent_name]["failed_runs"] += 1
2100 |                     main_app.print_text("\nOperation interrupted by user.\n")
2101 |                 except (asyncio.CancelledError, KeyboardInterrupt):
     |                         ^^^^^^^^^^^^^^^^^^^^^^
2102 |                     self.session_stats["agents"][agent_name]["failed_runs"] += 1
2103 |                     self.formatter.print_warning("\nOperation interrupted by user.\n")
     |

B023 Function definition does not bind loop variable `cpu_usage`
   --> toolboxv2\flows\minicli.py:264:36
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                    ^^^^^^^^^
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

B023 Function definition does not bind loop variable `memory_usage`
   --> toolboxv2\flows\minicli.py:264:57
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                                         ^^^^^^^^^^^^
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

B023 Function definition does not bind loop variable `disk_usage`
   --> toolboxv2\flows\minicli.py:264:79
    |
262 |             return HTML(
263 |                 f'<b> App Infos: '
264 |                 f'{app.id} \nCPU: {cpu_usage}% Memory: {memory_usage}% Disk :{disk_usage}%\nTime: {current_time}</b>')
    |                                                                               ^^^^^^^^^^
265 |
266 |         call = app.run_any(TBEF.CLI_FUNCTIONS.USER_INPUT, completer_dict=autocompletion_dict,
    |

B023 Function definition does not bind loop variable `audio_data`
   --> toolboxv2\flows\vad_talk.py:558:55
    |
556 |                         try:
557 |                             # Transcribe the audio
558 |                             result = self._transcribe(audio_data)
    |                                                       ^^^^^^^^^^
559 |                             with self.processing_lock:
    |

B023 Function definition does not bind loop variable `segment_id`
   --> toolboxv2\flows\vad_talk.py:562:36
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
    |                          ^^^^^^^^^^
563 | …                         self.transcription_segments[segment_id] = result
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

B023 Function definition does not bind loop variable `segment_id`
   --> toolboxv2\flows\vad_talk.py:562:62
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
    |                                                    ^^^^^^^^^^
563 | …                         self.transcription_segments[segment_id] = result
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

B023 Function definition does not bind loop variable `segment_id`
   --> toolboxv2\flows\vad_talk.py:563:65
    |
561 | …                     # Store segment result if needed for later concatenation
562 | …                     if segment_id != "final" and segment_id != "legacy":
563 | …                         self.transcription_segments[segment_id] = result
    |                                                       ^^^^^^^^^^
564 | …                         self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |

B023 Function definition does not bind loop variable `segment_id`
   --> toolboxv2\flows\vad_talk.py:564:87
    |
562 |                                 if segment_id != "final" and segment_id != "legacy":
563 |                                     self.transcription_segments[segment_id] = result
564 |                                     self.logger.debug(f"Stored transcription segment {segment_id}: {result[:50]}...")
    |                                                                                       ^^^^^^^^^^
565 |
566 |                                 # For final segments, try to concatenate previous segments if available
    |

B023 Function definition does not bind loop variable `is_final`
   --> toolboxv2\flows\vad_talk.py:567:36
    |
566 | …                     # For final segments, try to concatenate previous segments if available
567 | …                     if is_final and self.transcription_segments:
    |                          ^^^^^^^^
568 | …                         combined_result = self._concatenate_transcriptions(result)
569 | …                         self.logger.info(
    |

B023 Function definition does not bind loop variable `is_final`
   --> toolboxv2\flows\vad_talk.py:577:36
    |
576 | …                     # Store for reference
577 | …                     if is_final:
    |                          ^^^^^^^^
578 | …                         self.last_transcription = result
    |

B023 Function definition does not bind loop variable `callback`
   --> toolboxv2\flows\vad_talk.py:581:36
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
    |                                    ^^^^^^^^
582 |                                     self.results_queue.put((result, is_final, callback))
583 |                         finally:
    |

B023 Function definition does not bind loop variable `is_final`
   --> toolboxv2\flows\vad_talk.py:582:69
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
582 |                                     self.results_queue.put((result, is_final, callback))
    |                                                                     ^^^^^^^^
583 |                         finally:
584 |                             self.task_queue.task_done()
    |

B023 Function definition does not bind loop variable `callback`
   --> toolboxv2\flows\vad_talk.py:582:79
    |
580 |                                 # Queue result for callback processing
581 |                                 if callback:
582 |                                     self.results_queue.put((result, is_final, callback))
    |                                                                               ^^^^^^^^
583 |                         finally:
584 |                             self.task_queue.task_done()
    |

B023 Function definition does not bind loop variable `callback_start`
    --> toolboxv2\flows\vad_talk.py:1374:28
     |
1373 |                         # Trigger any provided callback before starting
1374 |                         if callback_start:
     |                            ^^^^^^^^^^^^^^
1375 |                             callback_start()
     |

B023 Function definition does not bind loop variable `callback_start`
    --> toolboxv2\flows\vad_talk.py:1375:29
     |
1373 |                         # Trigger any provided callback before starting
1374 |                         if callback_start:
1375 |                             callback_start()
     |                             ^^^^^^^^^^^^^^
1376 |
1377 |                         self.logger.debug(f"Starting TTS for text: {text[:50]}{'...' if len(text) > 50 else ''}")
     |

B023 Function definition does not bind loop variable `callback_end`
    --> toolboxv2\flows\vad_talk.py:1390:32
     |
1388 | …                     self.is_speaking = False
1389 | …                     #self.current_text = None
1390 | …                     if callback_end:
     |                          ^^^^^^^^^^^^
1391 | …                         callback_end(text)
1392 | …                     #self.tts_queue.task_done()
     |

B023 Function definition does not bind loop variable `callback_end`
    --> toolboxv2\flows\vad_talk.py:1391:33
     |
1389 | …                     #self.current_text = None
1390 | …                     if callback_end:
1391 | …                         callback_end(text)
     |                           ^^^^^^^^^^^^
1392 | …                     #self.tts_queue.task_done()
1393 | …                     pass
     |

SIM101 Multiple `isinstance` calls for `list_content`, merge into a single call
   --> toolboxv2\mods\Canvas.py:368:106
    |
366 |               list_content = list_res_obj.get()  # Get can return list or string based on DB adapter
367 |               json_str_to_load = list_content[0] if isinstance(list_content,
368 |                                                                list) and list_content else list_content if isinstance(
    |  __________________________________________________________________________________________________________^
369 | |                 list_content, str) or isinstance(list_content, bytes) else "[]"
    | |_____________________________________________________________________^
370 |               user_sessions = json.loads(json_str_to_load)
371 |               if not isinstance(user_sessions, list): user_sessions = []
    |
help: Merge `isinstance` calls for `list_content`

SIM105 Use `contextlib.suppress(ValueError)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\Canvas.py:494:21
    |
492 |                   app.logger.info(f"SSE: Cleaning up for client {session_client_id}, C:{canvas_id}.")
493 |                   if canvas_id in canvas_tool.live_canvas_sessions:
494 | /                     try:
495 | |                         canvas_tool.live_canvas_sessions[canvas_id].remove(sse_client_queue)
496 | |                     except ValueError:
497 | |                         pass
    | |____________________________^
498 |                       if not canvas_tool.live_canvas_sessions[canvas_id]:
499 |                           del canvas_tool.live_canvas_sessions[canvas_id]
    |
help: Replace with `contextlib.suppress(ValueError)`

S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
  --> toolboxv2\mods\CloudM\ModManager.py:80:9
   |
78 |         print_func(f"{url} -> {directory}/{filename}")
79 |         os.makedirs(directory, exist_ok=True)
80 |         urllib.request.urlretrieve(url, f"{directory}/{filename}")
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
81 |     return f"{directory}/{filename}"
   |

S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
  --> toolboxv2\mods\CloudM\ModManager.py:89:9
   |
87 |         requirements_filename = f"{module_name}-requirements.txt"
88 |         print_func(f"Download requirements {requirements_filename}")
89 |         urllib.request.urlretrieve(requirements_url, requirements_filename)
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
90 |
91 |         print_func("Install requirements")
   |

SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
   --> toolboxv2\mods\CloudM\ModManager.py:258:13
    |
256 |               temp_dir = Path(temp_dir)
257 |
258 | /             with Spinner(f"Extracting {zip_path.name}"):
259 | |                 # Entpacke ZIP-Datei
260 | |                 with zipfile.ZipFile(zip_path, 'r') as zip_ref:
    | |_______________________________________________________________^
261 |                       zip_ref.extractall(temp_dir)
    |
help: Combine `with` statements

F811 Redefinition of unused `update_all_mods` from line 454
   --> toolboxv2\mods\CloudM\ModManager.py:493:11
    |
492 | @export(mod_name=Name, name="build_all", test=False)
493 | async def update_all_mods(app, base="mods", upload=True):
    |           ^^^^^^^^^^^^^^^ `update_all_mods` redefined here
494 |     if app is None:
495 |         app = get_app(f"{Name}.update_all")
    |
   ::: toolboxv2\mods\CloudM\ModManager.py:454:11
    |
453 | @export(mod_name=Name, name="update_all", test=False)
454 | async def update_all_mods(app):
    |           --------------- previous definition of `update_all_mods` here
455 |     """
456 |     Aktualisiert alle installierten Module mit minimalen Server-Anfragen.
    |
help: Remove definition: `update_all_mods`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\CloudM\UserInstances.py:145:9
    |
143 |       if instance['SiID'] in UserInstances().live_user_instances:
144 |           instance_live = UserInstances().live_user_instances.get(instance['SiID'], {})
145 | /         if 'live' in instance_live:
146 | |             if instance_live['live'] and instance_live['save']['mods']:
    | |_______________________________________________________________________^
147 |                   logger.info(Style.BLUEBG2("Instance returned from live"))
148 |                   return instance_live
    |
help: Combine `if` statements using `and`

F403 `from .mini import *` used; unable to detect undefined names
 --> toolboxv2\mods\CloudM\__init__.py:3:1
  |
1 | from .AdminDashboard import Name as AdminDashboard
2 | from .extras import login
3 | from .mini import *
  | ^^^^^^^^^^^^^^^^^^^
4 | from .ModManager_tests import run_mod_manager_tests
5 | from .module import Tools
  |

F405 `mini` may be undefined, or defined from star imports
  --> toolboxv2\mods\CloudM\__init__.py:15:12
   |
13 | Name = 'CloudM'
14 | version = Tools.version
15 | __all__ = ["mini"]
   |            ^^^^^^
   |

S701 By default, jinja2 sets `autoescape` to `False`. Consider using `autoescape=True` or the `select_autoescape` function to mitigate XSS vulnerabilities.
   --> toolboxv2\mods\CloudM\email_services.py:120:13
    |
119 | # Jinja2 Environment for inline templates
120 | jinja_env = Environment(loader=BaseLoader())
    |             ^^^^^^^^^^^
121 | base_template_jinja = jinja_env.from_string(BASE_HTML_TEMPLATE)
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\CloudM\extras.py:304:5
    |
302 |           return "Pleas connect first to a redis instance"
303 |
304 | /     if not do_root:
305 | |         if 'y' not in input(Style.RED("Ar u sure : the deb will be cleared type y :")):
    | |_______________________________________________________________________________________^
306 |               return
    |
help: Combine `if` statements using `and`

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\mods\CloudM\mini.py:46:22
   |
45 |             # Add encoding handling for Windows
46 |             result = subprocess.run(
   |                      ^^^^^^^^^^^^^^
47 |                 command,
48 |                 capture_output=True,
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\mods\CloudM\mini.py:75:26
   |
73 |             # Try alternate encoding if cp850 fails
74 |             try:
75 |                 result = subprocess.run(
   |                          ^^^^^^^^^^^^^^
76 |                     command,
77 |                     capture_output=True,
   |

S602 `subprocess` call with `shell=True` identified, security issue
   --> toolboxv2\mods\CloudM\mini.py:100:22
    |
 98 |             command = f'ps -p {pids_str} -o pid='
 99 |
100 |             result = subprocess.run(
    |                      ^^^^^^^^^^^^^^
101 |                 command,
102 |                 capture_output=True,
    |

SIM105 Use `contextlib.suppress(ValueError)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\CounterTracker.py:145:17
    |
143 |               value = values.get(field_name)
144 |               if isinstance(value, str):
145 | /                 try:
146 | |                     values[field_name] = isoparse(value).date()
147 | |                 except ValueError:
148 | |                     pass
    | |________________________^
149 |
150 |           if '_user_timezone_str_context' in values:
    |
help: Replace with `contextlib.suppress(ValueError)`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\CounterTracker.py:174:9
    |
173 |           # Check if the entire series has ended
174 | /         if self.series_end_date:
175 | |             # Convert series_end_date to a datetime at the end of that day in UTC for comparison
176 | |             # Assuming user_timezone_str context is available if needed, but series_end_date is just a date.
177 | |             # For comparison, consider the end of that day in UTC.
178 | |             # A simpler approach: if today's date (UTC) is past series_end_date, it's ended.
179 | |             if date.today() > self.series_end_date:  # Using current system date, ideally in user's TZ for this logic
    | |___________________________________________________^
180 |                   return CounterStatus.INACTIVE  # Series ended
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\CounterTracker.py:525:9
    |
523 |           reminder_lead_time = timedelta(hours=self.settings.poa_reminder_lead_time_hours)
524 |
525 | /         if timedelta(minutes=15) < time_to_period_end <= reminder_lead_time:  # Create reminder if within window
526 | |             # Only if target is not met
527 | |             if counter.current_count_in_period < counter.target_count:
    | |______________________________________________________________________^
528 |                   try:
529 |                       poa_app = self.app.get_mod("POA")
    |
help: Combine `if` statements using `and`

S608 Possible SQL injection vector through string-based query construction
    --> toolboxv2\mods\CounterTracker.py:752:20
     |
 750 |   def counter_tracker_ui_page(app_ref: App | None = None):
 751 |       app_instance = app_ref if app_ref else get_app(MODULE_NAME)
 752 |       html_content = """<!DOCTYPE html>
     |  ____________________^
 753 | | <html lang="en" data-theme="light">
 754 | | <head>
 755 | |     <meta charset="UTF-8">
 756 | |     <meta name="viewport" content="width=device-width, initial-scale=1.0">
 757 | |     <title>Counter Tracker</title>
 758 | |     <style>
 759 | |     """+"""/* Basic styles - will be expanded */
 760 | | body {
 761 | |   font-family: var(--font-family-base);
 762 | |   margin: 0;
 763 | |   background-color: var(--theme-bg, #f8f9fa);
 764 | |   color: var(--theme-text, #181823);
 765 | | }
 766 | |
 767 | | .container {
 768 | |   max-width: 900px;
 769 | |   margin: 20px auto;
 770 | |   padding: 20px;
 771 | |   background-color: var(--theme-bg, #ffffff);
 772 | |   border-radius: var(--radius-md);
 773 | |   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
 774 | | }
 775 | |
 776 | | h1, h2 {
 777 | |   color: var(--theme-header-color, var(--dark-primary-color, #2c3e50));
 778 | | }
 779 | |
 780 | | .hidden {
 781 | |   display: none !important;
 782 | | }
 783 | |
 784 | | .counter-card {
 785 | |   border: 1px solid var(--theme-border, #e5e7eb);
 786 | |   padding: 15px;
 787 | |   margin-bottom: 15px;
 788 | |   border-radius: var(--radius-sm);
 789 | |   background-color: var(--theme-bg, #fdfdfd);
 790 | | }
 791 | |
 792 | | .counter-card h3 {
 793 | |   margin-top: 0;
 794 | | }
 795 | |
 796 | | .counter-card p {
 797 | |   margin: 5px 0;
 798 | | }
 799 | |
 800 | | .progress-bar-container {
 801 | |   background-color: #e0e0e0;
 802 | |   border-radius: var(--radius-sm);
 803 | |   height: 20px;
 804 | |   margin: 10px 0;
 805 | | }
 806 | |
 807 | | .progress-bar {
 808 | |   background-color: var(--color-success, #198754);
 809 | |   height: 100%;
 810 | |   border-radius: var(--radius-sm);
 811 | |   text-align: center;
 812 | |   color: var(--anti-text-clor, white);
 813 | |   line-height: 20px;
 814 | |   transition: width var(--transition-medium);
 815 | | }
 816 | |
 817 | | .btn {
 818 | |   padding: 8px 12px;
 819 | |   margin-right: 5px;
 820 | |   border: none;
 821 | |   border-radius: var(--radius-sm);
 822 | |   cursor: pointer;
 823 | | }
 824 | |
 825 | | .btn-primary {
 826 | |   background-color: var(--button-bg, var(--theme-primary));
 827 | |   color: var(--button-text, white);
 828 | | }
 829 | |
 830 | | .btn-secondary {
 831 | |   background-color: var(--theme-secondary, #537FE7);
 832 | |   color: var(--theme-secondary-text-color, #2c3e50);
 833 | | }
 834 | |
 835 | | label {
 836 | |   display: block;
 837 | |   margin-top: 10px;
 838 | | }
 839 | |
 840 | | input[type="text"],
 841 | | input[type="number"],
 842 | | input[type="date"],
 843 | | select,
 844 | | textarea {
 845 | |   width: calc(100% - 16px);
 846 | |   padding: 8px;
 847 | |   margin-top: 5px;
 848 | |   border: 1px solid var(--input-border, #ccc);
 849 | |   border-radius: var(--radius-sm);
 850 | |   background-color: var(--input-bg, #fff);
 851 | |   color: var(--theme-input-text-color, var(--theme-text));
 852 | | }
 853 | |
 854 | | .tabs button {
 855 | |   padding: 10px 15px;
 856 | |   border: none;
 857 | |   background-color: var(--theme-tab-bg-color, #eee);
 858 | |   cursor: pointer;
 859 | |   color: var(--theme-tab-text-color, #333);
 860 | | }
 861 | |
 862 | | .tabs button.active {
 863 | |   background-color: var(--theme-tab-active-bg-color, #ccc);
 864 | |   font-weight: bold;
 865 | | }
 866 | |
 867 | | .tab-content {
 868 | |   padding: 15px;
 869 | |   border: 1px solid var(--theme-border, #ccc);
 870 | |   border-top: none;
 871 | | }
 872 | |
 873 | | #overallStatsViewPlot,
 874 | | #statsViewPlot {
 875 | |   min-height: 300px;
 876 | |   width: 100%;
 877 | | }
 878 | |
 879 | | .compact-counter-group {
 880 | |   margin-bottom: 10px;
 881 | |   padding: 10px;
 882 | |   border: 1px solid var(--theme-border, #eee);
 883 | |   border-radius: var(--radius-sm);
 884 | | }
 885 | |
 886 | | .compact-counter-group summary {
 887 | |   font-weight: bold;
 888 | |   cursor: pointer;
 889 | | }
 890 | |
 891 | | .compact-counter-group .counter-card {
 892 | |   margin-left: 20px;
 893 | |   margin-top: 10px;
 894 | | }
 895 | |
 896 | | /* Ensure TB Modals are styled nicely if default isn't enough */
 897 | | /* .tb-modal-content {} */
 898 | | """+f"""
 899 | |     </style>
 900 | |     <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
 901 | |     <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>
 902 | | </head>
 903 | | <body>
 904 | |     <div class="container">
 905 | |         <header style="display: flex; justify-content: space-between; align-items: center;">
 906 | |             <h1>{MODULE_NAME}</h1>
 907 | |         </header>
 908 | |
 909 | |         <div class="tabs">
 910 | |             <button class="tab active" data-tab="dashboard">Dashboard</button>
 911 | |             <button class="tab" data-tab="overall-stats">Overall Stats</button>
 912 | |             <button class="tab" data-tab="settings">Settings</button>
 913 | |         </div>
 914 | |
 915 | |         <div id="dashboardTab" class="tab-content">
 916 | |             <h2>My Counters</h2>
 917 | |             <button id="showAddCounterModalBtn" class="btn btn-primary">Add New Counter</button>
 918 | |             <div id="countersList"></div>
 919 | |         </div>
 920 | |
 921 | |         <div id="overallStatsTab" class="tab-content hidden">
 922 | |             <h2>Overall Statistics</h2>
 923 | |             <p>Summary of all your tracking efforts.</p>
 924 | |             <div id="overallStatsSummary"></div>
 925 | |             <div><canvas id="overallStatsViewPlot"></canvas></div>
 926 | |             <div id="topCountersList"><h3>Most Active Counters:</h3><ul></ul></div>
 927 | |         </div>
 928 | |
 929 | |         <div id="settingsTab" class="tab-content hidden">
 930 | |             <h2>Settings</h2>
 931 | |             <form id="settingsForm">
 932 | |                 <div>
 933 | |                     <label for="settingTimezone">Timezone:</label>
 934 | |                     <select id="settingTimezone"></select>
 935 | |                 </div>
 936 | |                 <div>
 937 | |                     <label for="settingEnablePoa">Enable POA Integration:</label>
 938 | |                     <input type="checkbox" id="settingEnablePoa">
 939 | |                 </div>
 940 | |                 <div id="poaSpecificSettings" class="hidden">
 941 | |                     <label for="settingPoaLeadTime">POA Reminder Lead Time (hours):</label>
 942 | |                     <input type="number" id="settingPoaLeadTime" min="0" step="1">
 943 | |                     <label for="settingPoaPriority">POA Default Task Priority (1-5):</label>
 944 | |                     <input type="number" id="settingPoaPriority" min="1" max="5" step="1">
 945 | |                 </div>
 946 | |                 <button type="submit" class="btn btn-primary">Save Settings</button>
 947 | |             </form>
 948 | |         </div>
 949 | |     </div>
 950 | |
 951 | |     """ + """<script unsave="true" defer>
 952 | |     function init () {
 953 | |     setTimeout(_init, 1000);
 954 | |     }
 955 | |     function _init () {
 956 | |         "use strict";
 957 | |
 958 | |         const API_MODULE_NAME = "CounterTracker";
 959 | |         let currentSettings = { timezone: 'UTC', enable_poa_integration: false };
 960 | |         let currentCounters = [];
 961 | |         let statsChartInstance = null;
 962 | |         let overallStatsChartInstance = null;
 963 | |         const DateTime = window.luxon.DateTime;
 964 | |
 965 | |         const elements = {
 966 | |             // Tabs
 967 | |             tabsContainer: document.querySelector('.tabs'),
 968 | |             dashboardTabContent: document.getElementById('dashboardTab'),
 969 | |             overallStatsTabContent: document.getElementById('overallStatsTab'),
 970 | |             settingsTabContent: document.getElementById('settingsTab'),
 971 | |             // Dashboard
 972 | |             showAddCounterModalBtn: document.getElementById('showAddCounterModalBtn'),
 973 | |             countersListDiv: document.getElementById('countersList'),
 974 | |             // Overall Stats
 975 | |             overallStatsSummaryDiv: document.getElementById('overallStatsSummary'),
 976 | |             overallStatsViewPlotCanvas: document.getElementById('overallStatsViewPlot'),
 977 | |             topCountersListUl: document.querySelector('#topCountersList ul'),
 978 | |             // Settings
 979 | |             settingsForm: document.getElementById('settingsForm'),
 980 | |             settingTimezoneSelect: document.getElementById('settingTimezone'),
 981 | |             settingEnablePoaCheckbox: document.getElementById('settingEnablePoa'),
 982 | |             poaSpecificSettingsDiv: document.getElementById('poaSpecificSettings'),
 983 | |             settingPoaLeadTimeInput: document.getElementById('settingPoaLeadTime'),
 984 | |             settingPoaPriorityInput: document.getElementById('settingPoaPriority'),
 985 | |         };
 986 | |
 987 | |         async function initializeApp() {
 988 | |             if (!window.TB?.api?.request || !window.TB?.ui?.Modal) {
 989 | |                 console.error("Toolbox API or TB.ui.Modal not available. CounterTracker cannot run.");
 990 | |                 document.body.innerHTML = "<p style='color:red; font-size:1.2em; padding:20px;'>Error: Toolbox API/UI not available.…
 991 | |                 return;
 992 | |             }
 993 | |             setupEventListeners();
 994 | |             await loadSettings();
 995 | |             await loadCounters(); // Initial load for dashboard
 996 | |             showTab('dashboard');
 997 | |             populateTimezoneSelect();
 998 | |         }
 999 | |
1000 | |         function setupEventListeners() {
1001 | |             elements.tabsContainer.addEventListener('click', (e) => {
1002 | |                 if (e.target.classList.contains('tab')) {
1003 | |                     showTab(e.target.dataset.tab);
1004 | |                 }
1005 | |             });
1006 | |             elements.showAddCounterModalBtn.addEventListener('click', () => openCounterModal());
1007 | |             elements.settingsForm.addEventListener('submit', handleSaveSettings);
1008 | |             elements.settingEnablePoaCheckbox.addEventListener('change', togglePoaSpecificSettingsVisibility);
1009 | |         }
1010 | |
1011 | |         async function apiRequest(endpoint, payload = null, method = 'GET', queryParams = {}) {
1012 | |             if(window.TB?.ui?.Loader) TB.ui.Loader.show({text: "Working...", hideMainContent:false});
1013 | |             try {
1014 | |                 const response = await window.TB.api.request(API_MODULE_NAME, endpoint, payload, method, { queryParams });
1015 | |                 if (response.error !== window.TB.ToolBoxError.none) {
1016 | |                     const errorMsg = response.info?.help_text || response.data?.message || `API Error (${response.error})`;
1017 | |                     console.error(`API Error [${endpoint}]:`, errorMsg, response);
1018 | |                     if(window.TB?.ui?.Toast) TB.ui.Toast.showError(errorMsg.substring(0,150), {duration: 4000});
1019 | |                     return { error: true, message: errorMsg, data: response.get() };
1020 | |                 }
1021 | |                 return { error: false, data: response.get() };
1022 | |             } catch (err) {
1023 | |                 console.error(`Network/JS Error [${endpoint}]:`, err);
1024 | |                 if(window.TB?.ui?.Toast) TB.ui.Toast.showError("Network or application error.", {duration: 4000});
1025 | |                 return { error: true, message: "NETWORK_ERROR" };
1026 | |             } finally {
1027 | |                 if(window.TB?.ui?.Loader) TB.ui.Loader.hide();
1028 | |             }
1029 | |         }
1030 | |
1031 | |         function formatDate(isoString, format = DateTime.DATETIME_MED_WITH_SECONDS) {
1032 | |             if (!isoString) return 'N/A';
1033 | |             try {
1034 | |                 return DateTime.fromISO(isoString, { zone: 'utc' }).setZone(currentSettings.timezone).toLocaleString(format);
1035 | |             } catch(e) {
1036 | |                 console.warn("Error formatting date:", isoString, currentSettings.timezone, e);
1037 | |                 return new Date(isoString).toLocaleString(); // Fallback
1038 | |             }
1039 | |         }
1040 | |         function formatJustDate(isoString) {
1041 | |              if (!isoString) return 'N/A';
1042 | |              try {
1043 | |                  return DateTime.fromISO(isoString).toLocaleString(DateTime.DATE_MED);
1044 | |              } catch (e) { return isoString; }
1045 | |         }
1046 | |
1047 | |         function showTab(tabName) {
1048 | |             document.querySelectorAll('.tabs .tab').forEach(tab => {
1049 | |                 tab.classList.toggle('active', tab.dataset.tab === tabName);
1050 | |             });
1051 | |             document.querySelectorAll('.tab-content').forEach(tc => {
1052 | |                 tc.classList.toggle('hidden', tc.id !== `${tabName}Tab`);
1053 | |             });
1054 | |
1055 | |             if (tabName === 'dashboard') {
1056 | |                 loadCounters();
1057 | |             } else if (tabName === 'overall-stats') {
1058 | |                 loadOverallStats();
1059 | |             } else if (tabName === 'settings') {
1060 | |                 loadSettings();
1061 | |             }
1062 | |         }
1063 | |
1064 | |         async function loadSettings() {
1065 | |             const response = await apiRequest('get-settings');
1066 | |             if (!response.error && response.data) {
1067 | |                 currentSettings = response.data;
1068 | |                 elements.settingTimezoneSelect.value = currentSettings.timezone;
1069 | |                 elements.settingEnablePoaCheckbox.checked = currentSettings.enable_poa_integration;
1070 | |                 elements.settingPoaLeadTimeInput.value = currentSettings.poa_reminder_lead_time_hours;
1071 | |                 elements.settingPoaPriorityInput.value = currentSettings.poa_default_priority;
1072 | |                 togglePoaSpecificSettingsVisibility();
1073 | |             } else {
1074 | |                 console.error("Failed to load settings, using defaults.");
1075 | |                 populateTimezoneSelect();
1076 | |             }
1077 | |         }
1078 | |
1079 | |         async function handleSaveSettings(event) {
1080 | |             event.preventDefault();
1081 | |             const newSettings = {
1082 | |                 timezone: elements.settingTimezoneSelect.value,
1083 | |                 enable_poa_integration: elements.settingEnablePoaCheckbox.checked,
1084 | |                 poa_reminder_lead_time_hours: parseInt(elements.settingPoaLeadTimeInput.value),
1085 | |                 poa_default_priority: parseInt(elements.settingPoaPriorityInput.value)
1086 | |             };
1087 | |             const response = await apiRequest('update-settings', newSettings, 'POST');
1088 | |             if (!response.error) {
1089 | |                 currentSettings = response.data;
1090 | |                 if(window.TB?.ui?.Toast) TB.ui.Toast.showSuccess("Settings saved!");
1091 | |                 await loadCounters(); // Reload counters as timezone might affect period display
1092 | |             }
1093 | |         }
1094 | |
1095 | |         function populateTimezoneSelect() {
1096 | |             const commonTimezones = ["UTC", "GMT", "US/Pacific", "US/Mountain", "US/Central", "US/Eastern", "America/New_York", "Ame…
1097 | |             if (currentSettings.timezone && !commonTimezones.includes(currentSettings.timezone)) {
1098 | |                 commonTimezones.unshift(currentSettings.timezone);
1099 | |             }
1100 | |             elements.settingTimezoneSelect.innerHTML = commonTimezones.map(tz => `<option value="${tz}">${tz}</option>`).join('');
1101 | |             elements.settingTimezoneSelect.value = currentSettings.timezone;
1102 | |         }
1103 | |
1104 | |         function togglePoaSpecificSettingsVisibility() {
1105 | |             elements.poaSpecificSettingsDiv.classList.toggle('hidden', !elements.settingEnablePoaCheckbox.checked);
1106 | |         }
1107 | |
1108 | |         async function loadCounters() {
1109 | |             const response = await apiRequest('list-counters');
1110 | |             if (!response.error && response.data) {
1111 | |                 currentCounters = response.data;
1112 | |                 renderCounters();
1113 | |             } else {
1114 | |                 elements.countersListDiv.innerHTML = "<p>Could not load counters.</p>";
1115 | |             }
1116 | |         }
1117 | |
1118 | |         function renderCounters() {
1119 | |             if (currentCounters.length === 0) {
1120 | |                 elements.countersListDiv.innerHTML = "<p>No counters yet. Add one!</p>";
1121 | |                 return;
1122 | |             }
1123 | |
1124 | |             // Group counters by name
1125 | |             const groupedCounters = currentCounters.reduce((acc, counter) => {
1126 | |                 acc[counter.name] = acc[counter.name] || [];
1127 | |                 acc[counter.name].push(counter);
1128 | |                 return acc;
1129 | |             }, {});
1130 | |
1131 | |             let html = '';
1132 | |             for (const name in groupedCounters) {
1133 | |                 const group = groupedCounters[name];
1134 | |                 if (group.length > 1) { // Multiple counters with the same name
1135 | |                     html += `<details class="compact-counter-group"><summary>${name} (${group.length} instances)</summary>`;
1136 | |                     group.forEach(counter => html += renderSingleCounterCard(counter));
1137 | |                     html += `</details>`;
1138 | |                 } else { // Single counter
1139 | |                     html += renderSingleCounterCard(group[0]);
1140 | |                 }
1141 | |             }
1142 | |             elements.countersListDiv.innerHTML = html;
1143 | |
1144 | |             // Add event listeners using event delegation on countersListDiv
1145 | |             elements.countersListDiv.removeEventListener('click', handleCounterCardActions); // Remove old if any
1146 | |             elements.countersListDiv.addEventListener('click', handleCounterCardActions);
1147 | |         }
1148 | |
1149 | |         function handleCounterCardActions(event) {
1150 | |             const target = event.target;
1151 | |             const counterCard = target.closest('.counter-card');
1152 | |             if (!counterCard) return;
1153 | |
1154 | |             const counterId = counterCard.dataset.id;
1155 | |             const counterName = counterCard.dataset.name; // Store name on card for modals
1156 | |
1157 | |             if (target.classList.contains('increment-btn')) {
1158 | |                 openIncrementModal(counterId, counterName);
1159 | |             } else if (target.classList.contains('edit-btn')) {
1160 | |                 openCounterModal(counterId);
1161 | |             } else if (target.classList.contains('delete-btn')) {
1162 | |                 handleDeleteCounter(counterId);
1163 | |             } else if (target.classList.contains('view-stats-btn')) {
1164 | |                 openStatsModal(counterId, counterName);
1165 | |             }
1166 | |         }
1167 | |
1168 | |         function renderSingleCounterCard(counter) {
1169 | |             const progressPercent = counter.target_count > 0 ? Math.min(100, (counter.current_count_in_period / counter.target_count…
1170 | |             const statusText = counter.is_active ? (counter.status || "In Progress") : "Inactive"; // Assuming 'status' is on counte…
1171 | |             return `
1172 | |                 <div class="counter-card" data-id="${counter.id}" data-name="${counter.name}">
1173 | |                     <h3>${counter.name} <small>(${counter.unit_name})</small></h3>
1174 | |                     ${counter.description ? `<p>${counter.description}</p>` : ''}
1175 | |                     <p>
1176 | |                         Target: ${counter.current_count_in_period} / ${counter.target_count} ${counter.unit_name}
1177 | |                         (${counter.frequency.replace('_', ' ')}${counter.frequency !== 'one_time' ? 'ly' : ''})
1178 | |                         ${counter.series_end_date ? ` (Ends: ${formatJustDate(counter.series_end_date)})` : ''}
1179 | |                     </p>
1180 | |                     <div class="progress-bar-container">
1181 | |                         <div class="progress-bar" style="width: ${progressPercent}%;">
1182 | |                             ${Math.round(progressPercent)}%
1183 | |                         </div>
1184 | |                     </div>
1185 | |                     <p>Status: <span class="status-badge">${statusText}</span></p>
1186 | |                     ${counter.current_period_start_utc ? `<p><small>Current Period: ${formatDate(counter.current_period_start_utc)} …
1187 | |                     <p><small>Total Accumulated: ${counter.total_accumulated_count} ${counter.unit_name}</small></p>
1188 | |
1189 | |                     <div class="actions">
1190 | |                         <button class="btn btn-primary increment-btn">Increment</button>
1191 | |                         <button class="btn btn-secondary edit-btn">Edit</button>
1192 | |                         <button class="btn view-stats-btn">Stats</button>
1193 | |                         <button class="btn delete-btn" style="background-color:#e74c3c; color:white;">Delete</button>
1194 | |                     </div>
1195 | |                 </div>
1196 | |             `;
1197 | |         }
1198 | |
1199 | |         function openCounterModal(counterIdToEdit = null) {
1200 | |             const isEdit = !!counterIdToEdit;
1201 | |             let counter = null;
1202 | |             if (isEdit) {
1203 | |                 counter = currentCounters.find(c => c.id === counterIdToEdit);
1204 | |                 if (!counter) {
1205 | |                     if(window.TB?.ui?.Toast) TB.ui.Toast.showError("Counter not found for editing.");
1206 | |                     return;
1207 | |                 }
1208 | |             }
1209 | |
1210 | |             const formId = "counterFormInstance"; // Unique ID for the form
1211 | |             const content = `
1212 | |                 <form id="${formId}">
1213 | |                     <input type="hidden" id="modalCounterId" value="${isEdit ? counter.id : ''}">
1214 | |                     <div><label for="modalCounterName">Name:</label><input type="text" id="modalCounterName" value="${isEdit ? count…
1215 | |                     <div><label for="modalCounterDescription">Description:</label><textarea id="modalCounterDescription">${isEdit &&…
1216 | |                     <div><label for="modalCounterUnitName">Unit Name:</label><input type="text" id="modalCounterUnitName" value="${i…
1217 | |                     <div><label for="modalCounterTargetCount">Target Count:</label><input type="number" id="modalCounterTargetCount"…
1218 | |                     <div>
1219 | |                         <label for="modalCounterFrequency">Frequency:</label>
1220 | |                         <select id="modalCounterFrequency">
1221 | |                             <option value="one_time" ${isEdit && counter.frequency === 'one_time' ? 'selected' : ''}>One Time</optio…
1222 | |                             <option value="daily" ${(!isEdit || (isEdit && counter.frequency === 'daily')) ? 'selected' : ''}>Daily<…
1223 | |                             <option value="weekly" ${isEdit && counter.frequency === 'weekly' ? 'selected' : ''}>Weekly</option>
1224 | |                             <option value="monthly" ${isEdit && counter.frequency === 'monthly' ? 'selected' : ''}>Monthly</option>
1225 | |                         </select>
1226 | |                     </div>
1227 | |                     <div><label for="modalCounterSeriesEndDate">End Date (Optional):</label><input type="date" id="modalCounterSerie…
1228 | |                     <div><label for="modalCounterIsActive">Is Active:</label><input type="checkbox" id="modalCounterIsActive" ${(!is…
1229 | |                 </form>`;
1230 | |
1231 | |             TB.ui.Modal.show({
1232 | |                 title: isEdit ? "Edit Counter" : "Add New Counter",
1233 | |                 content: content,
1234 | |                 buttons: [
1235 | |                     { text: "Cancel", variant: "secondary", action: modal => modal.close() },
1236 | |                     { text: isEdit ? "Save Changes" : "Create Counter", variant: "primary",
1237 | |                       action: async (modal) => {
1238 | |                         const formElement = document.getElementById(formId);
1239 | |                         if (formElement && formElement.checkValidity()) {
1240 | |                             await handleSaveCounterFromModal(formElement, modal);
1241 | |                         } else {
1242 | |                             if(window.TB?.ui?.Toast) TB.ui.Toast.showError("Please fill all required fields.");
1243 | |                             formElement.reportValidity(); // Show native browser validation
1244 | |                         }
1245 | |                       }
1246 | |                     }
1247 | |                 ]
1248 | |             });
1249 | |         }
1250 | |
1251 | |         async function handleSaveCounterFromModal(formElement, modalInstance) {
1252 | |             const id = formElement.querySelector('#modalCounterId').value;
1253 | |             const counterData = {
1254 | |                 name: formElement.querySelector('#modalCounterName').value,
1255 | |                 description: formElement.querySelector('#modalCounterDescription').value,
1256 | |                 unit_name: formElement.querySelector('#modalCounterUnitName').value,
1257 | |                 target_count: parseInt(formElement.querySelector('#modalCounterTargetCount').value),
1258 | |                 frequency: formElement.querySelector('#modalCounterFrequency').value,
1259 | |                 series_end_date: formElement.querySelector('#modalCounterSeriesEndDate').value || null,
1260 | |                 is_active: formElement.querySelector('#modalCounterIsActive').checked
1261 | |             };
1262 | |
1263 | |             let response;
1264 | |             if (id) {
1265 | |                 response = await apiRequest(`update-counter?counter_id=${id}`, counterData, 'POST');
1266 | |             } else {
1267 | |                 response = await apiRequest('create-counter', counterData, 'POST');
1268 | |             }
1269 | |
1270 | |             if (!response.error) {
1271 | |                 if(window.TB?.ui?.Toast) TB.ui.Toast.showSuccess(`Counter ${id ? 'updated' : 'created'}!`);
1272 | |                 modalInstance.close();
1273 | |                 await loadCounters();
1274 | |             }
1275 | |         }
1276 | |
1277 | |         async function handleDeleteCounter(counterId) {
1278 | |             const confirmed = await TB.ui.Modal.confirm({
1279 | |                 title: "Delete Counter",
1280 | |                 content: "Are you sure you want to delete this counter and all its entries? This cannot be undone.",
1281 | |                 confirmButtonText: "Delete",
1282 | |                 confirmButtonVariant: "danger"
1283 | |             });
1284 | |             if (!confirmed) return;
1285 | |
1286 | |             const response = await apiRequest(`delete-counter?counter_id=${counterId}`, null, 'POST');
1287 | |             if (!response.error) {
1288 | |                 if(window.TB?.ui?.Toast) TB.ui.Toast.showSuccess("Counter deleted.");
1289 | |                 await loadCounters();
1290 | |             }
1291 | |         }
1292 | |
1293 | |         function openIncrementModal(counterId, counterName) {
1294 | |             const formId = "incrementFormInstance";
1295 | |             const content = `
1296 | |                 <form id="${formId}">
1297 | |                     <input type="hidden" id="modalIncrementCounterId" value="${counterId}">
1298 | |                     <p>Incrementing: <strong>${counterName}</strong></p>
1299 | |                     <div><label for="modalIncrementAmount">Amount:</label><input type="number" id="modalIncrementAmount" value="1" m…
1300 | |                     <div><label for="modalIncrementNotes">Notes (Optional):</label><textarea id="modalIncrementNotes"></textarea></d…
1301 | |                 </form>`;
1302 | |
1303 | |             TB.ui.Modal.show({
1304 | |                 title: `Increment ${counterName}`,
1305 | |                 content: content,
1306 | |                 onOpen: () => document.getElementById('modalIncrementAmount').focus(),
1307 | |                 buttons: [
1308 | |                     { text: "Cancel", variant: "secondary", action: modal => modal.close() },
1309 | |                     { text: "Log Increment", variant: "primary",
1310 | |                       action: async (modal) => {
1311 | |                         const formElement = document.getElementById(formId);
1312 | |                         const amount = parseInt(formElement.querySelector('#modalIncrementAmount').value);
1313 | |                         const notes = formElement.querySelector('#modalIncrementNotes').value;
1314 | |                         if (isNaN(amount) || amount < 1) {
1315 | |                             if(window.TB?.ui?.Toast) TB.ui.Toast.showError("Invalid amount."); return;
1316 | |                         }
1317 | |                         const response = await apiRequest(`increment-counter?counter_id=${counterId}`, { amount, notes }, 'POST');
1318 | |                         if (!response.error) {
1319 | |                             if(window.TB?.ui?.Toast) TB.ui.Toast.showSuccess("Increment logged!");
1320 | |                             modal.close();
1321 | |                             await loadCounters();
1322 | |                         }
1323 | |                       }
1324 | |                     }
1325 | |                 ]
1326 | |             });
1327 | |         }
1328 | |
1329 | |         async function openStatsModal(counterId, counterName) {
1330 | |             if (statsChartInstance) statsChartInstance.destroy();
1331 | |
1332 | |             const content = `
1333 | |                 <div id="modalStatsViewDetails"><p>Loading stats...</p></div>
1334 | |                 <div><canvas id="modalStatsViewPlotCanvas" style="min-height: 250px; width:100%;"></canvas></div>
1335 | |                 <div id="modalStatsViewEntries"><h3>Recent Entries:</h3><ul id="modalStatsEntriesListUl"></ul></div>`;
1336 | |
1337 | |             const modal = TB.ui.Modal.show({
1338 | |                 title: `Stats for: ${counterName}`,
1339 | |                 content: content,
1340 | |                 maxWidth: 'max-w-2xl', // Wider modal for stats
1341 | |                 buttons: [{ text: "Close", variant: "secondary", action: m => m.close() }],
1342 | |                 onClose: () => { if (statsChartInstance) statsChartInstance.destroy(); statsChartInstance = null; }
1343 | |             });
1344 | |
1345 | |             // Wait for modal to be in DOM to get canvas context
1346 | |             await new Promise(resolve => setTimeout(resolve, 50));
1347 | |
1348 | |             const statsDetailsDiv = document.getElementById('modalStatsViewDetails');
1349 | |             const statsEntriesListUl = document.getElementById('modalStatsEntriesListUl');
1350 | |             const statsPlotCanvas = document.getElementById('modalStatsViewPlotCanvas');
1351 | |
1352 | |             const statsResponse = await apiRequest(`get-counter-stats?counter_id=${counterId}`);
1353 | |             if (!statsResponse.error && statsResponse.data) {
1354 | |                 renderCounterStatsDetails(statsResponse.data, statsDetailsDiv, statsPlotCanvas);
1355 | |             } else {
1356 | |                 statsDetailsDiv.innerHTML = "<p>Could not load stats.</p>";
1357 | |             }
1358 | |
1359 | |             const entriesResponse = await apiRequest(`get-counter-entries?counter_id=${counterId}&limit=10`);
1360 | |             if (!entriesResponse.error && entriesResponse.data) {
1361 | |                 renderStatsEntries(entriesResponse.data, statsEntriesListUl, counterName);
1362 | |             }
1363 | |         }
1364 | |
1365 | |         function renderCounterStatsDetails(statsData, detailsDiv, plotCanvas) {
1366 | |             const counterObj = statsData.counter_info;
1367 | |             detailsDiv.innerHTML = `
1368 | |                 <p><strong>Status:</strong> ${statsData.current_status}</p>
1369 | |                 <p><strong>Current Period Progress:</strong> ${statsData.current_period_progress_percent}% (${counterObj.current_cou…
1370 | |                 <p><strong>Total Entries Logged:</strong> ${statsData.total_entries_logged} ${counterObj.unit_name}</p>
1371 | |                 <p><strong>Frequency:</strong> ${counterObj.frequency.replace('_', ' ')}${counterObj.frequency !== 'one_time' ? 'ly'…
1372 | |                 ${counterObj.current_period_start_utc ? `<p><strong>Current Period:</strong> ${formatDate(counterObj.current_period_…
1373 | |                 ${counterObj.series_end_date ? `<p><strong>Series Ends:</strong> ${formatJustDate(counterObj.series_end_date)}</p>` …
1374 | |             `;
1375 | |             renderStatsChart(plotCanvas, counterObj);
1376 | |         }
1377 | |
1378 | |         function renderStatsEntries(entries, entriesUl, counterName) {
1379 | |             if (entries.length === 0) {
1380 | |                 entriesUl.innerHTML = "<li>No recent entries.</li>"; return;
1381 | |             }
1382 | |             const unitName = currentCounters.find(c => c.name === counterName)?.unit_name || 'unit(s)';
1383 | |             entriesUl.innerHTML = entries.map(entry => `
1384 | |                 <li><strong>${entry.amount} ${unitName}</strong> on ${formatDate(entry.timestamp)}
1385 | |                     ${entry.notes ? ` <small>- <em>${entry.notes}</em></small>` : ''}</li>`).join('');
1386 | |         }
1387 | |
1388 | |         function renderStatsChart(canvasElement, counterData) {
1389 | |             if (statsChartInstance) statsChartInstance.destroy();
1390 | |             const ctx = canvasElement.getContext('2d');
1391 | |             statsChartInstance = new Chart(ctx, {
1392 | |                 type: 'bar',
1393 | |                 data: {
1394 | |                     labels: ['Current Progress'],
1395 | |                     datasets: [{
1396 | |                         label: 'Count ('+counterData.unit_name+')', data: [counterData.current_count_in_period],
1397 | |                         backgroundColor: 'rgba(75, 192, 192, 0.6)', borderColor: 'rgba(75, 192, 192, 1)', borderWidth: 1
1398 | |                     }, {
1399 | |                         label: 'Target ('+counterData.unit_name+')', data: [counterData.target_count],
1400 | |                         backgroundColor: 'rgba(255, 99, 132, 0.2)', borderColor: 'rgba(255, 99, 132, 1)', borderWidth: 1
1401 | |                     }]
1402 | |                 },
1403 | |                 options: {
1404 | |                     scales: { y: { beginAtZero: true, suggestedMax: counterData.target_count * 1.2 } },
1405 | |                     responsive: true, maintainAspectRatio: false
1406 | |                 }
1407 | |             });
1408 | |         }
1409 | |
1410 | |         // --- Overall Stats Tab ---
1411 | |         async function loadOverallStats() {
1412 | |             // For overall stats, we might need a new backend endpoint or aggregate client-side from list-counters
1413 | |             // For simplicity, let's aggregate client-side from `currentCounters`
1414 | |             // This will be called after `loadCounters` ensures `currentCounters` is fresh if needed.
1415 | |             if (!currentCounters || currentCounters.length === 0) {
1416 | |                 elements.overallStatsSummaryDiv.innerHTML = "<p>No counters available to show overall stats.</p>";
1417 | |                 elements.topCountersListUl.innerHTML = "";
1418 | |                 if(overallStatsChartInstance) overallStatsChartInstance.destroy();
1419 | |                 return;
1420 | |             }
1421 | |
1422 | |             const totalCounters = currentCounters.length;
1423 | |             const activeCounters = currentCounters.filter(c => c.is_active).length;
1424 | |             const totalAccumulatedAll = currentCounters.reduce((sum, c) => sum + c.total_accumulated_count, 0);
1425 | |
1426 | |             elements.overallStatsSummaryDiv.innerHTML = `
1427 | |                 <p><strong>Total Counters:</strong> ${totalCounters}</p>
1428 | |                 <p><strong>Active Counters:</strong> ${activeCounters}</p>
1429 | |                 <p><strong>Total Accumulated (all counters, all time):</strong> ${totalAccumulatedAll}</p>
1430 | |             `;
1431 | |
1432 | |             const topFiveCounters = [...currentCounters]
1433 | |                 .sort((a,b) => b.total_accumulated_count - a.total_accumulated_count)
1434 | |                 .slice(0, 5);
1435 | |
1436 | |             elements.topCountersListUl.innerHTML = topFiveCounters.map(c =>
1437 | |                 `<li>${c.name}: ${c.total_accumulated_count} ${c.unit_name} (total)</li>`
1438 | |             ).join('');
1439 | |
1440 | |             renderOverallStatsChart(topFiveCounters);
1441 | |         }
1442 | |
1443 | |         function renderOverallStatsChart(topCounters) {
1444 | |             if (overallStatsChartInstance) overallStatsChartInstance.destroy();
1445 | |             const ctx = elements.overallStatsViewPlotCanvas.getContext('2d');
1446 | |
1447 | |             overallStatsChartInstance = new Chart(ctx, {
1448 | |                 type: 'pie', // Or 'doughnut'
1449 | |                 data: {
1450 | |                     labels: topCounters.map(c => `${c.name} (${c.unit_name})`),
1451 | |                     datasets: [{
1452 | |                         label: 'Total Accumulated Count',
1453 | |                         data: topCounters.map(c => c.total_accumulated_count),
1454 | |                         backgroundColor: [ // Add more colors if more than 5 top counters shown
1455 | |                             'rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)',
1456 | |                             'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)',
1457 | |                             'rgba(153, 102, 255, 0.7)'
1458 | |                         ],
1459 | |                         hoverOffset: 4
1460 | |                     }]
1461 | |                 },
1462 | |                 options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top' } } }
1463 | |             });
1464 | |         }
1465 | |
1466 | |         if (window.TB?.events) {
1467 | |             if (window.TB.config?.get('appRootId') || window.TB._isInitialized === true) initializeApp();
1468 | |             else window.TB.events.on('tbjs:initialized', initializeApp, { once: true });
1469 | |         } else {
1470 | |             console.warn("Toolbox not fully loaded, attempting init on DOMContentLoaded.");
1471 | |             document.addEventListener('DOMContentLoaded', () => {
1472 | |                 if (window.TB?.events?.on) window.TB.events.on('tbjs:initialized', initializeApp, { once: true });
1473 | |                 else if (window.TB?._isInitialized) initializeApp();
1474 | |                 else console.error("CRITICAL: TB not available after DOMContentLoaded for CounterTracker.");
1475 | |             });
1476 | |         }
1477 | |     }
1478 | |
1479 | |     // Wait for tbjs to be initialized
1480 | | if (window.TB?.events) {
1481 | |     if (window.TB.config?.get('appRootId')) { // A sign that TB.init might have run
1482 | |          init();
1483 | |     } else {
1484 | |         window.TB.events.on('tbjs:initialized', init, { once: true });
1485 | |     }
1486 | | } else {
1487 | |     // Fallback if TB is not even an object yet, very early load
1488 | |     document.addEventListener('tbjs:initialized', init, { once: true }); // Custom event dispatch from TB.init
1489 | | }
1490 | |
1491 | |     </script>""" + """
1492 | | </body>
1493 | | </html>"""
     | |__________^
1494 |       return Result.html(app_instance.web_context() + html_content)
     |

UP045 Use `X | None` for type annotations
  --> toolboxv2\mods\DB\tb_adapter.py:91:25
   |
89 |         self.encoding = 'utf-8'
90 |
91 |         self.data_base: Optional[MiniRedis , MiniDictDB , DB , None] = None
   |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
92 |         self.mode = DatabaseModes.crate(
93 |             os.getenv("DB_MODE_KEY", "LC") if 'test' not in get_app("DB_MODE_KEY").id else os.getenv("DB_MODE_KEY_TEST",
   |
help: Convert to `X | None`

SIM105 Use `contextlib.suppress(UnicodeDecodeError)` instead of `try`-`except`-`pass`
  --> toolboxv2\mods\DB\ui.py:14:9
   |
12 |       """Helper to unwrap data if it's in a single-element list."""
13 |       if isinstance(data, bytes):
14 | /         try:
15 | |             data = data.decode('utf-8')
16 | |         except UnicodeDecodeError:
17 | |             pass
   | |________________^
18 |       if isinstance(data, list) and len(data) == 1:
19 |           return data[0]
   |
help: Replace with `contextlib.suppress(UnicodeDecodeError)`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\FastApi\fast_api_install.py:378:13
    |
377 |         except Exception as e:
378 |             raise HTTPException(status_code=500, detail=f"There was an error uploading the file: {str(e)}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
379 |
380 |         finally:
    |

F811 Redefinition of unused `index` from line 689
   --> toolboxv2\mods\FastApi\fast_api_main.py:704:11
    |
703 | @app.get("/tauri")
704 | async def index():
    |           ^^^^^ `index` redefined here
705 |     return serve_app_func("/web/assets/widgetControllerLogin.html")
    |
   ::: toolboxv2\mods\FastApi\fast_api_main.py:689:11
    |
688 | @app.get("/")
689 | async def index():
    |           ----- previous definition of `index` here
690 |     return RedirectResponse(url="/web/")
    |
help: Remove definition: `index`

F811 Redefinition of unused `index` from line 704
   --> toolboxv2\mods\FastApi\fast_api_main.py:709:11
    |
708 | @app.get("/favicon.ico")
709 | async def index():
    |           ^^^^^ `index` redefined here
710 |     return serve_app_func('/web/favicon.ico')
711 |     # return "Willkommen bei Simple V0 powered by ToolBoxV2-0.0.3"
    |
   ::: toolboxv2\mods\FastApi\fast_api_main.py:704:11
    |
703 | @app.get("/tauri")
704 | async def index():
    |           ----- previous definition of `index` here
705 |     return serve_app_func("/web/assets/widgetControllerLogin.html")
    |
help: Remove definition: `index`

F811 Redefinition of unused `login_page` from line 800
   --> toolboxv2\mods\FastApi\fast_api_main.py:805:11
    |
804 | @app.get("/web/logout")
805 | async def login_page(access_allowed: bool = Depends(lambda: check_access_level(0))):
    |           ^^^^^^^^^^ `login_page` redefined here
806 |     return serve_app_func('web/assets/logout.html')
    |
   ::: toolboxv2\mods\FastApi\fast_api_main.py:800:11
    |
799 | @app.get("/web/login")
800 | async def login_page(access_allowed: bool = Depends(lambda: check_access_level(0))):
    |           ---------- previous definition of `login_page` here
801 |     return serve_app_func('web/assets/login.html')
    |
help: Remove definition: `login_page`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\FastApi\fast_api_main.py:862:17
    |
860 |               except httpx.RequestError as e:
861 |                   # More specific error handling for network-related issues
862 | /                 raise HTTPException(
863 | |                     status_code=500,
864 | |                     detail=f"Request failed: {str(e)}"
865 | |                 )
    | |_________________^
866 |               except httpx.HTTPStatusError as e:
867 |                   # Handle HTTP error status codes
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\FastApi\fast_api_main.py:868:17
    |
866 |               except httpx.HTTPStatusError as e:
867 |                   # Handle HTTP error status codes
868 | /                 raise HTTPException(
869 | |                     status_code=e.response.status_code,
870 | |                     detail=f"HTTP error: {str(e)}"
871 | |                 )
    | |_________________^
872 |
873 |       except Exception as e:
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\FastApi\fast_api_main.py:875:9
    |
873 |       except Exception as e:
874 |           # Catch-all error handling with more detailed logging
875 | /         raise HTTPException(
876 | |             status_code=500,
877 | |             detail=f"Unexpected error in webhook forwarding: {str(e)}"
878 | |         )
    | |_________^
879 |
880 |   @app.api_route("/whatsappHook/{port}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])
    |

F811 Redefinition of unused `startup_event` from line 788
   --> toolboxv2\mods\FastApi\fast_api_main.py:885:11
    |
884 | @app.on_event("startup")
885 | async def startup_event():
    |           ^^^^^^^^^^^^^ `startup_event` redefined here
886 |     print('Server started :', __name__, datetime.now())
    |
   ::: toolboxv2\mods\FastApi\fast_api_main.py:788:11
    |
786 | # Example WebSocket message handler registration
787 | @app.on_event("startup")
788 | async def startup_event():
    |           ------------- previous definition of `startup_event` here
789 |     pass
790 |     #async def handle_data_update(session_id: str, message: dict):
    |
help: Remove definition: `startup_event`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\mods\FastApi\fast_api_main.py:1055:17
     |
1054 |             except fastapi.exceptions.FastAPIError as e:
1055 |                 raise SyntaxError(f"fuction '{function_name}' prove the signature error {e}")
     |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1056 |         if add:
1057 |             app.include_router(router)
     |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\FastApi\fast_lit.py:109:17
    |
107 |                 headers['Authorization'] = f'Bearer {session_token}'
108 |             except jwt.InvalidTokenError:
109 |                 raise ValueError("Invalid session token")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
110 |
111 |         async with httpx.AsyncClient() as client:
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\FastApi\fast_nice.py:348:13
    |
346 |                   await self.handle_ws_message(session_id, gui_id, data)
347 |           except WebSocketDisconnect:
348 | /             if session_id in self.ws_connections:
349 | |                 if gui_id in self.ws_connections[session_id]:
    | |_____________________________________________________________^
350 |                       del self.ws_connections[session_id][gui_id]
    |
help: Combine `if` statements using `and`

B007 Loop control variable `i` not used within loop body
   --> toolboxv2\mods\MinimalHtml.py:117:17
    |
115 |             template = string.Template(template_content)
116 |             html_element = '<h1> invalid Template </h1>'
117 |             for i in range(len(template_content)):
    |                 ^
118 |                 try:
119 |                     html_element = template.substitute(**element['kwargs'])
    |
help: Rename unused `i` to `_i`

SIM103 Return the negated condition directly
   --> toolboxv2\mods\P2PRPCServer.py:125:9
    |
123 |           allowed_functions_for_module = self.function_access_config[module]
124 |
125 | /         if function not in allowed_functions_for_module:
126 | |             return False
127 | |
128 | |         # If the function is whitelisted, and there's a specific identification part,
129 | |         # you might want to add more granular control here.
130 | |         # For now, if it's in the whitelist, it's allowed for any identified client.
131 | |         # You could extend function_access_config to be:
132 | |         # {"ModuleName": {"function1": ["id1", "id2"], "function2": ["id3"]}}
133 | |         # For simplicity, current implementation assumes if module.function is in whitelist,
134 | |         # it's generally allowed for any authenticated client.
135 | |         return True
    | |___________________^
136 |
137 |       def format_error(self, call_id, code, message, details=None) -> dict:
    |
help: Inline condition

SIM105 Use `contextlib.suppress(ValueError)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\POA\module.py:167:17
    |
165 |           for field_name, value in json_data.items():
166 |               if field_name == 'item_type' and isinstance(value, str):
167 | /                 try:
168 | |                     json_data[field_name] = ItemType(value)
169 | |                 except ValueError:
170 | |                     pass
    | |________________________^
171 |               elif field_name == 'frequency' and isinstance(value, str):
172 |                   try:
    |
help: Replace with `contextlib.suppress(ValueError)`

SIM105 Use `contextlib.suppress(ValueError)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\POA\module.py:172:17
    |
170 |                       pass
171 |               elif field_name == 'frequency' and isinstance(value, str):
172 | /                 try:
173 | |                     json_data[field_name] = Frequency(value)
174 | |                 except ValueError:
175 | |                     pass
    | |________________________^
176 |               elif field_name == 'status' and isinstance(value, str):
177 |                   try:
    |
help: Replace with `contextlib.suppress(ValueError)`

SIM105 Use `contextlib.suppress(ValueError)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\POA\module.py:177:17
    |
175 |                       pass
176 |               elif field_name == 'status' and isinstance(value, str):
177 | /                 try:
178 | |                     json_data[field_name] = ActionStatus(value)
179 | |                 except ValueError:
180 | |                     pass
    | |________________________^
181 |
182 |           instance = cls.model_validate(json_data)
    |
help: Replace with `contextlib.suppress(ValueError)`

B007 Loop control variable `item_id` not used within loop body
   --> toolboxv2\mods\POA\module.py:679:13
    |
677 |         root_items_temp = []
678 |
679 |         for item_id, item_dict in item_map.items():
    |             ^^^^^^^
680 |             parent_id = item_dict.get("parent_id")
681 |             if parent_id and parent_id in item_map:
    |
help: Rename unused `item_id` to `_item_id`

SIM108 Use ternary operator `uid = str(uuid.uuid4()) if not uid else uid.to_ical().decode('utf-8')` instead of `if`-`else`-block
   --> toolboxv2\mods\POA\module.py:889:21
    |
887 |                   if component.name == "VEVENT":
888 |                       uid = component.get('uid')
889 | /                     if not uid:
890 | |                         uid = str(uuid.uuid4())  # Generate a UID if missing
891 | |                     else:
892 | |                         uid = uid.to_ical().decode('utf-8')
    | |___________________________________________________________^
893 |
894 |                       summary = component.get('summary', 'Untitled Event').to_ical().decode('utf-8')
    |
help: Replace `if`-`else`-block with `uid = str(uuid.uuid4()) if not uid else uid.to_ical().decode('utf-8')`

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\mods\ProcessManager.py:20:23
   |
18 |         self.processes = []
19 |         for command in commands:
20 |             process = subprocess.Popen(command, shell=True)
   |                       ^^^^^^^^^^^^^^^^
21 |             self.processes.append(process)
   |

S113 Probable use of `requests` call without timeout
   --> toolboxv2\mods\SocketManager.py:105:20
    |
103 | def get_public_ip():
104 |     try:
105 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^
106 |         ip_address = response.json()['ip']
107 |         return ip_address
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\SocketManager.py:438:9
    |
436 |               f" max:{self.sockets[name]['max_connections']} connect on :{endpoint}")
437 |           self.sockets[name]["client_sockets_dict"][endpoint[0] + str(endpoint[1])] = client_socket
438 | /         if self.sockets[name]['max_connections'] != -1:
439 | |             if self.sockets[name]["connections"] >= self.sockets[name]['max_connections']:
    | |__________________________________________________________________________________________^
440 |                   self.sockets[name]["running_dict"]["server_receiver"].set()
    |
help: Combine `if` statements using `and`

B030 `except` handlers should only be exception classes or tuples of exception classes
   --> toolboxv2\mods\SocketManager.py:587:16
    |
585 |                 except Exception as e:
586 |                     return Result.custom_error(data=str(e), data_info="Connection down and closed")
587 |         except ConnectionResetError and ConnectionAbortedError as e:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
588 |             self.print(f"Closing Receiver {name}:{identifier} {str(e)}")
589 |             self.sockets[name]["running_dict"]["receive"][identifier].set()
    |

B030 `except` handlers should only be exception classes or tuples of exception classes
   --> toolboxv2\mods\SocketManager.py:612:16
    |
610 |             self.logger.info(f"{name} -- received JSON -- {msg['identifier']}")
611 |             return msg
612 |         except json.JSONDecodeError and UnicodeDecodeError as e:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
613 |             self.logger.error(f"JSON decode error: {e}")
    |

F811 Redefinition of unused `TextSplitter` from line 16
  --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:23:28
   |
21 | from .research_processor import ResearchProcessor
22 | from .sources.arxiv_source import Paper, search_papers
23 | from .text_splitter import TextSplitter
   |                            ^^^^^^^^^^^^ `TextSplitter` redefined here
   |
  ::: toolboxv2\mods\TruthSeeker\arXivCrawler.py:16:52
   |
15 | from toolboxv2 import get_app
16 | from toolboxv2.mods.isaa.base.KnowledgeBase import TextSplitter
   |                                                    ------------ previous definition of `TextSplitter` here
17 |
18 | from .pdf_processor import RobustPDFDownloader
   |
help: Remove definition: `TextSplitter`

F811 Redefinition of unused `RobustPDFDownloader` from line 18
  --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:26:7
   |
26 | class RobustPDFDownloader:
   |       ^^^^^^^^^^^^^^^^^^^ `RobustPDFDownloader` redefined here
27 |     def __init__(self, max_retries=5, backoff_factor=0.3,
28 |                  download_dir='downloads',
   |
  ::: toolboxv2\mods\TruthSeeker\arXivCrawler.py:18:28
   |
16 | from toolboxv2.mods.isaa.base.KnowledgeBase import TextSplitter
17 |
18 | from .pdf_processor import RobustPDFDownloader
   |                            ------------------- previous definition of `RobustPDFDownloader` here
19 |
20 | # Import the new modular components
   |
help: Remove definition: `RobustPDFDownloader`

F821 Undefined name `HTTPAdapter`
  --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:61:24
   |
59 |             backoff_factor=backoff_factor
60 |         )
61 |         self.adapter = HTTPAdapter(max_retries=self.retry_strategy)
   |                        ^^^^^^^^^^^
62 |
63 |         # Create session with retry mechanism
   |

F821 Undefined name `requests`
  --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:64:24
   |
63 |         # Create session with retry mechanism
64 |         self.session = requests.Session()
   |                        ^^^^^^^^
65 |         self.session.mount("https://", self.adapter)
66 |         self.session.mount("http://", self.adapter)
   |

F821 Undefined name `requests`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:99:16
    |
 97 |             return file_path
 98 |
 99 |         except requests.exceptions.RequestException as e:
    |                ^^^^^^^^
100 |             self.logger.error(f"Download failed for {url}: {e}")
101 |             raise
    |

F821 Undefined name `PyPDF2`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:116:26
    |
114 |             page_texts = []
115 |             with open(pdf_path, 'rb') as file:
116 |                 reader = PyPDF2.PdfReader(file)
    |                          ^^^^^^
117 |                 for page_num, page in enumerate(reader.pages, 1):
118 |                     text = page.extract_text()
    |

F821 Undefined name `PyPDF2`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:143:26
    |
141 |         try:
142 |             with open(pdf_path, 'rb') as file:
143 |                 reader = PyPDF2.PdfReader(file)
    |                          ^^^^^^
144 |
145 |                 for page_num, page in enumerate(reader.pages, 1):
    |

F821 Undefined name `Image`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:149:35
    |
147 |                         for img_index, image in enumerate(page.images):
148 |                             img_data = image.data
149 |                             img = Image.open(io.BytesIO(img_data))
    |                                   ^^^^^
150 |
151 |                             img_filename = f'page_{page_num}_img_{img_index}.png'
    |

F821 Undefined name `io`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:149:46
    |
147 |                         for img_index, image in enumerate(page.images):
148 |                             img_data = image.data
149 |                             img = Image.open(io.BytesIO(img_data))
    |                                              ^^
150 |
151 |                             img_filename = f'page_{page_num}_img_{img_index}.png'
    |

F811 Redefinition of unused `Paper` from line 22
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:178:7
    |
176 |     relevance_score: float = 0.0
177 |
178 | class Paper(BaseModel):
    |       ^^^^^ `Paper` redefined here
179 |     title: str
180 |     summary: str
    |
   ::: toolboxv2\mods\TruthSeeker\arXivCrawler.py:22:35
    |
 20 | # Import the new modular components
 21 | from .research_processor import ResearchProcessor
 22 | from .sources.arxiv_source import Paper, search_papers
    |                                   ----- previous definition of `Paper` here
 23 | from .text_splitter import TextSplitter
    |
help: Remove definition: `Paper`

F811 Redefinition of unused `search_papers` from line 22
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:190:5
    |
188 |     key_sections: list[str] = Field(default_factory=list)
189 |
190 | def search_papers(query: str, max_results=10) -> list[Paper]:
    |     ^^^^^^^^^^^^^ `search_papers` redefined here
191 |     search = arxiv.Search(
192 |         query=query,
    |
   ::: toolboxv2\mods\TruthSeeker\arXivCrawler.py:22:42
    |
 20 | # Import the new modular components
 21 | from .research_processor import ResearchProcessor
 22 | from .sources.arxiv_source import Paper, search_papers
    |                                          ------------- previous definition of `search_papers` here
 23 | from .text_splitter import TextSplitter
    |
help: Remove definition: `search_papers`

F821 Undefined name `arxiv`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:191:14
    |
190 | def search_papers(query: str, max_results=10) -> list[Paper]:
191 |     search = arxiv.Search(
    |              ^^^^^
192 |         query=query,
193 |         max_results=max_results,
    |

F821 Undefined name `arxiv`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:194:17
    |
192 |         query=query,
193 |         max_results=max_results,
194 |         sort_by=arxiv.SortCriterion.Relevance
    |                 ^^^^^
195 |     )
    |

F821 Undefined name `arxiv`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:198:19
    |
197 |     results = []
198 |     for result in arxiv.Client().results(search):
    |                   ^^^^^
199 |         paper = Paper(
200 |             title=result.title,
    |

F821 Undefined name `Spinner`
   --> toolboxv2\mods\TruthSeeker\arXivCrawler.py:453:10
    |
451 | async def main(query: str = "Beste strategien in bretspielen sitler von katar"):
452 |     """Main execution function"""
453 |     with Spinner("Init Isaa"):
    |          ^^^^^^^
454 |         tools = get_app("ArXivPDFProcessor", name=None).get_mod("isaa")
455 |         tools.init_isaa(build=True)
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\TruthSeeker\module.py:228:5
    |
226 |       if abut:
227 |           return HTMLResponse(content=abut_content)
228 | /     if hasattr(request, 'row'):
229 | |         if sid := request.row.query_params.get('session_id'):
    | |_____________________________________________________________^
230 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
231 |       if hasattr(request, 'query_params'):
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\TruthSeeker\module.py:231:5
    |
229 |           if sid := request.row.query_params.get('session_id'):
230 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
231 | /     if hasattr(request, 'query_params'):
232 | |         if sid := request.query_params.get('session_id'):
    | |_________________________________________________________^
233 |               return RedirectResponse(url=f"/gui/open-Seeker.seek?session_id={sid}")
234 |       return RedirectResponse(url="/gui/open-Seeker")
    |
help: Combine `if` statements using `and`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\TruthSeeker\one.py:131:13
    |
129 |             import torchaudio
130 |         except ImportError:
131 |             raise ValueError("Couldn't load audio install torchaudio'")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
132 |         # Handle different audio input types
133 |         if isinstance(audio_data, str):
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\TruthSeeker\one.py:315:29
    |
313 |                     except Exception:
314 |                         if len(chunk_embeddings) == 0 and chunks.index(chunk) == len(chunks)-1:
315 |                             raise e
    |                             ^^^^^^^
    |

SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
   --> toolboxv2\mods\TruthSeeker\tests.py:124:9
    |
123 |           # Mock PDF download and processing
124 | /         with patch.object(RobustPDFDownloader, 'download_pdf') as mock_download:
125 | |             with patch.object(RobustPDFDownloader, 'extract_text_from_pdf') as mock_extract:
    | |____________________________________________________________________________________________^
126 |                   mock_download.return_value = "test.pdf"
127 |                   mock_extract.return_value = [{'page_number': 1, 'text': 'test content'}]
    |
help: Combine `with` statements

SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
   --> toolboxv2\mods\TruthSeeker\tests.py:171:9
    |
169 |       def test_process_empty_results(self):
170 |           # Test processing with no results
171 | /         with patch.object(ArXivPDFProcessor, 'generate_queries') as mock_generate:
172 | |             with patch.object(ArXivPDFProcessor, 'search_and_process_papers') as mock_search:
    | |_____________________________________________________________________________________________^
173 |                   mock_generate.return_value = ["test query"]
174 |                   mock_search.return_value = []
    |
help: Combine `with` statements

SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
   --> toolboxv2\mods\TruthSeeker\tests.py:194:9
    |
192 |           mock_search.return_value.results.return_value = [mock_result]
193 |
194 | /         with patch.object(RobustPDFDownloader, 'download_pdf') as mock_download:
195 | |             with patch.object(RobustPDFDownloader, 'extract_text_from_pdf') as mock_extract:
    | |____________________________________________________________________________________________^
196 |                   with patch.object(self.mock_tools, 'format_class') as mock_format_class:
197 |                       mock_download.return_value = "test.pdf"
    |
help: Combine `with` statements

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\UltimateTTT.py:870:17
    |
868 |                       return threats  # Max threat, no need to check further
869 |
870 | /                 if not threats['can_zwick']:  # Only check Zwick if not already found for this board
871 | |                     if _count_potential_wins_on_board(temp_board_opp_plays, opponent_symbol_cell, zwick_eval_size) >= 2:
    | |________________________________________________________________________________________________________________________^
872 |                           threats['can_zwick'] = True
873 |                           # Continue checking other opponent moves; one might be an immediate win.
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\UltimateTTT.py:1404:9
     |
1402 |               if np.sum(line == player_int_symbol) == size - 1 and np.sum(line == EMPTY_INT) == 1:
1403 |                   count += 1
1404 | /         elif check_type == "actual_win":
1405 | |             if np.all(line == player_int_symbol):  # If all elements in the line match player symbol
     | |_________________________________________________^
1406 |                   # For actual_win, we usually just need to know if one exists, not count them.
1407 |                   # But to fit the "count" structure, we'll count. If used for win check, >0 means win.
     |
help: Combine `if` statements using `and`

SIM108 Use ternary operator `data = result.get()[0] if isinstance(result.get(), list) else result.get()` instead of `if`-`else`-block
    --> toolboxv2\mods\UltimateTTT.py:1530:13
     |
1528 |       if result.is_data() and result.get():
1529 |           try:
1530 | /             if isinstance(result.get(), list):
1531 | |                 data = result.get()[0]
1532 | |             else:
1533 | |                 data = result.get()
     | |___________________________________^
1534 |               return GameState.model_validate_json(data)
1535 |           except Exception as e:
     |
help: Replace `if`-`else`-block with `data = result.get()[0] if isinstance(result.get(), list) else result.get()`

SIM108 Use ternary operator `data = result.get()[0] if isinstance(result.get(), list) else result.get()` instead of `if`-`else`-block
    --> toolboxv2\mods\UltimateTTT.py:1546:13
     |
1544 |       if result.is_data() and result.get():
1545 |           try:
1546 | /             if isinstance(result.get(), list):
1547 | |                 data = result.get()[0]
1548 | |             else:
1549 | |                 data = result.get()
     | |___________________________________^
1550 |               return UserSessionStats.model_validate_json(data)
1551 |           except Exception:
     |
help: Replace `if`-`else`-block with `data = result.get()[0] if isinstance(result.get(), list) else result.get()`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\UltimateTTT.py:1817:9
     |
1815 |   …     # Validate human move first
1816 |   …     current_player_info = game_state.get_current_player_info()
1817 | / …     if not current_player_info or current_player_info.is_npc:
1818 | | …         # This should not happen if UI prevents human from moving for NPC
1819 | | …         # Or, if it's an NPC's turn initiated by server after a human move.
1820 | | …         # For now, assume make_move is always initiated by a human player's action.
1821 | | …         if current_player_info and current_player_info.is_npc:
     | |________________________________________________________________^
1822 |   …             app.logger.warning(
1823 |   …                 f"make_move API called but current player {current_player_info.id} is NPC. Game: {game_id}. This implies server-si…
     |
help: Combine `if` statements using `and`

SIM105 Use `contextlib.suppress(BaseException)` instead of `try`-`except`-`pass`
    --> toolboxv2\mods\UltimateTTT.py:1910:13
     |
1908 |           if game_state:
1909 |               game_state.last_error_message = "Internal server error during move processing."
1910 | /             try:
1911 | |                 await save_game_to_db_final(app, game_state)  # Attempt to save error state
1912 | |             except:
1913 | |                 pass
     | |____________________^
1914 |               return Result.default_internal_error("Could not process move.", data=game_state.model_dump_for_api())
1915 |           return Result.default_internal_error("Could not process move.")
     |
help: Replace with `contextlib.suppress(BaseException)`

S105 Possible hardcoded password assigned to: "token"
   --> toolboxv2\mods\WebSocketManager.py:317:49
    |
315 |                         return '{"res": "No User Instance Found"}'
316 |
317 |                     if data['data']['token'] == "**SelfAuth**":
    |                                                 ^^^^^^^^^^^^^^
318 |                         data['data']['token'] = user_instance['token']
    |

S105 Possible hardcoded password assigned to: "token"
   --> toolboxv2\mods\WebSocketManager.py:392:49
    |
390 |                         return '{"res": "No User Instance Found pleas log in"}'
391 |
392 |                     if data['data']['token'] == "**SelfAuth**":
    |                                                 ^^^^^^^^^^^^^^
393 |                         data['data']['token'] = user_instance['token']
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\WebSocketManager.py:432:21
    |
430 |                           res = "Mod Error " + str(e)
431 |
432 | /                     if type(res) == str:
433 | |                         if (res.startswith('{') or res.startswith('[')) or res.startswith('"[') or res.startswith('"{') \
434 | |                             or res.startswith('\"[') or res.startswith('\"{') or res.startswith(
435 | |                             'b"[') or res.startswith('b"{'): \
    | |____________________________________________________________^
436 |                               res = eval(res)
437 |                       if not isinstance(res, dict):
    |
help: Combine `if` statements using `and`

F821 Undefined name `Agent`
   --> toolboxv2\mods\WhatsAppTb\client.py:146:22
    |
144 |     whc: WhClient
145 |     isaa: 'Tools'
146 |     agent: Optional['Agent'] = None
    |                      ^^^^^
147 |     credentials: Credentials | None = None
148 |     state: AssistantState = AssistantState.OFFLINE
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:211:22
    |
209 | …     """Interactive instance control card"""
210 | …     config = self.instances[instance_id]
211 | …     with ui.card().classes('w-full p-4 mb-4 bg-gray-50 dark:bg-gray-800').style("background-color: var(--background-color) !importa…
    |            ^^
212 | …         # Header Section
213 | …         with ui.row().classes('w-full justify-between items-center'):
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:213:26
    |
211 | …     with ui.card().classes('w-full p-4 mb-4 bg-gray-50 dark:bg-gray-800').style("background-color: var(--background-color) !importa…
212 | …         # Header Section
213 | …         with ui.row().classes('w-full justify-between items-center'):
    |                ^^
214 | …             ui.label(f'📱 {instance_id}').classes('text-xl font-bold')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:214:25
    |
212 |                     # Header Section
213 |                     with ui.row().classes('w-full justify-between items-center'):
214 |                         ui.label(f'📱 {instance_id}').classes('text-xl font-bold')
    |                         ^^
215 |
216 |                         # Status Indicator
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:217:25
    |
216 |                         # Status Indicator
217 |                         ui.label().bind_text_from(
    |                         ^^
218 |                             self.threads, instance_id,
219 |                             lambda x: 'Running' if x and x.is_alive() else 'Stopped'
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:223:26
    |
222 |                     # Configuration Display
223 |                     with ui.grid(columns=2).classes('w-full mt-4 gap-2'):
    |                          ^^
224 |
225 |                         ui.label('port:').classes('font-bold')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:225:25
    |
223 |                     with ui.grid(columns=2).classes('w-full mt-4 gap-2'):
224 |
225 |                         ui.label('port:').classes('font-bold')
    |                         ^^
226 |                         ui.label(config['port'])
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:226:25
    |
225 |                         ui.label('port:').classes('font-bold')
226 |                         ui.label(config['port'])
    |                         ^^
227 |
228 |                         ui.label('Last Activity:').classes('font-bold')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:228:25
    |
226 |                         ui.label(config['port'])
227 |
228 |                         ui.label('Last Activity:').classes('font-bold')
    |                         ^^
229 |                         ui.label().bind_text_from(
230 |                             self.last_messages, instance_id,
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:229:25
    |
228 |                         ui.label('Last Activity:').classes('font-bold')
229 |                         ui.label().bind_text_from(
    |                         ^^
230 |                             self.last_messages, instance_id,
231 |                             lambda x: x.strftime("%Y-%m-%d %H:%M:%S") if x else 'Never'
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:235:26
    |
234 |                     # Action Controls
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
    |                          ^^
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
237 |                             ui.tooltip('Configure')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:236:30
    |
234 |                     # Action Controls
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
    |                              ^^
237 |                             ui.tooltip('Configure')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:237:29
    |
235 |                     with ui.row().classes('w-full mt-4 gap-2'):
236 |                         with ui.button(icon='settings', on_click=lambda: edit_dialog.open()).props('flat'):
237 |                             ui.tooltip('Configure')
    |                             ^^
238 |
239 |                         with ui.button(icon='refresh', color='orange',
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:239:30
    |
237 |                             ui.tooltip('Configure')
238 |
239 |                         with ui.button(icon='refresh', color='orange',
    |                              ^^
240 |                                        on_click=lambda: self.restart_instance(instance_id)):
241 |                             ui.tooltip('Restart')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:241:29
    |
239 |                         with ui.button(icon='refresh', color='orange',
240 |                                        on_click=lambda: self.restart_instance(instance_id)):
241 |                             ui.tooltip('Restart')
    |                             ^^
242 |
243 |                         with ui.button(icon='stop', color='red',
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:243:30
    |
241 |                             ui.tooltip('Restart')
242 |
243 |                         with ui.button(icon='stop', color='red',
    |                              ^^
244 |                                        on_click=lambda: self.stop_instance(instance_id)):
245 |                             ui.tooltip('Stop')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:245:29
    |
243 |                         with ui.button(icon='stop', color='red',
244 |                                        on_click=lambda: self.stop_instance(instance_id)):
245 |                             ui.tooltip('Stop')
    |                             ^^
246 |
247 |                     # Edit Configuration Dialog
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:248:26
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
    |                          ^^
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:248:54
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
    |                                                      ^^
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:249:35
    |
247 |                     # Edit Configuration Dialog
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
    |                                   ^^
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:250:38
    |
248 |                     with ui.dialog() as edit_dialog, ui.card().classes('p-4 gap-4'):
249 |                         new_key = ui.input('API Key', value=config['phone_number_id'].get('key', ''))
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
    |                                      ^^
251 |
252 |                         with ui.row().classes('w-full justify-end'):
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:252:30
    |
250 |                         new_number = ui.input('Phone Number', value=config['phone_number_id'].get('number', ''))
251 |
252 |                         with ui.row().classes('w-full justify-end'):
    |                              ^^
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
254 |                             ui.button('Save', color='primary', on_click=lambda: (
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:253:29
    |
252 |                         with ui.row().classes('w-full justify-end'):
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
    |                             ^^
254 |                             ui.button('Save', color='primary', on_click=lambda: (
255 |                                 self.update_instance_config(
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:254:29
    |
252 |                         with ui.row().classes('w-full justify-end'):
253 |                             ui.button('Cancel', on_click=edit_dialog.close)
254 |                             ui.button('Save', color='primary', on_click=lambda: (
    |                             ^^
255 |                                 self.update_instance_config(
256 |                                     instance_id,
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:264:18
    |
263 |             # Main UI Layout
264 |             with ui.column().classes('w-full max-w-4xl mx-auto p-4'):
    |                  ^^
265 |                 ui.label('WhatsApp Instance Manager').classes('text-2xl font-bold mb-6')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:265:17
    |
263 |             # Main UI Layout
264 |             with ui.column().classes('w-full max-w-4xl mx-auto p-4'):
265 |                 ui.label('WhatsApp Instance Manager').classes('text-2xl font-bold mb-6')
    |                 ^^
266 |
267 |                 # Add Instance Section
    |

SIM117 Use a single `with` statement with multiple contexts instead of nested `with` statements
   --> toolboxv2\mods\WhatsAppTb\server.py:268:17
    |
267 |                   # Add Instance Section
268 | /                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 | |                     with ui.card().classes('w-full p-4 mt-2'):
    | |______________________________________________________________^
270 |                           instance_id = ui.input('Instance ID').classes('w-full')
271 |                           token = ui.input('API Token').classes('w-full')
    |
help: Combine `with` statements

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:268:22
    |
267 |                 # Add Instance Section
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
    |                      ^^
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:269:26
    |
267 |                 # Add Instance Section
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 |                     with ui.card().classes('w-full p-4 mt-2'):
    |                          ^^
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:270:39
    |
268 |                 with ui.expansion('➕ Add New Instance', icon='add').classes('w-full'):
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
    |                                       ^^
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:271:33
    |
269 |                     with ui.card().classes('w-full p-4 mt-2'):
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
    |                                 ^^
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:272:37
    |
270 |                         instance_id = ui.input('Instance ID').classes('w-full')
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
    |                                     ^^
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:273:40
    |
271 |                         token = ui.input('API Token').classes('w-full')
272 |                         phone_key = ui.input('Phone Number Key').classes('w-full')
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
    |                                        ^^
274 |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:275:30
    |
273 |                         phone_number = ui.input('Phone Number').classes('w-full')
274 |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
    |                              ^^
276 |                             ui.button('Clear', on_click=lambda: (
277 |                                 instance_id.set_value(''),
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:276:29
    |
275 |                         with ui.row().classes('w-full justify-end gap-2'):
276 |                             ui.button('Clear', on_click=lambda: (
    |                             ^^
277 |                                 instance_id.set_value(''),
278 |                                 token.set_value(''),
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:282:29
    |
280 | …                         phone_number.set_value('')
281 | …                     ))
282 | …                     ui.button('Create', color='positive', on_click=lambda: (
    |                       ^^
283 | …                         self.add_update_instance(
284 | …                             instance_id.value,
    |

F821 Undefined name `ui`
   --> toolboxv2\mods\WhatsAppTb\server.py:293:39
    |
292 |                 # Instances Display
293 |                 instances_container = ui.column().classes('w-full')
    |                                       ^^
294 |                 with instances_container:
295 |                     for instance_id in self.instances:
    |

B030 `except` handlers should only be exception classes or tuples of exception classes
  --> toolboxv2\mods\cli_functions.py:16:8
   |
14 |     READCHAR = True
15 |     READCHAR_error = None
16 | except ImportError and ModuleNotFoundError:
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
17 |     READCHAR = False
   |

B030 `except` handlers should only be exception classes or tuples of exception classes
  --> toolboxv2\mods\cli_functions.py:35:8
   |
33 |     PROMPT_TOOLKIT = True
34 |     PROMPT_TOOLKIT_error = None
35 | except ImportError and ModuleNotFoundError:
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
36 |     PROMPT_TOOLKIT = False
   |

SIM113 Use `enumerate()` for index variable `i` in `for` loop
   --> toolboxv2\mods\cli_functions.py:200:9
    |
198 |             else:
199 |                 as_list[i] = replacements[key]
200 |         i += 1
    |         ^^^^^^
201 |     if not inlist:
202 |         return text
    |

F811 Redefinition of unused `run_in_shell` from line 414
   --> toolboxv2\mods\cli_functions.py:428:9
    |
427 |     @bindings.add('c-up')
428 |     def run_in_shell(event):
    |         ^^^^^^^^^^^^ `run_in_shell` redefined here
429 |         buff = event.st_router.current_buffer.text
    |
   ::: toolboxv2\mods\cli_functions.py:414:9
    |
413 |     @bindings.add('s-up')
414 |     def run_in_shell(event):
    |         ------------ previous definition of `run_in_shell` here
415 |         buff = event.st_router.current_buffer.text
    |
help: Remove definition: `run_in_shell`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\CodingAgent\live.py:569:17
    |
567 |                 subprocess.run([sys.executable, "-m", "venv", str(self._venv_path)], check=True)
568 |             except subprocess.CalledProcessError as e:
569 |                 raise RuntimeError(f"Failed to create virtual environment: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
570 |
571 |     def _virtual_open(self, filepath, mode='r', *args, **kwargs):
    |

B005 Using `.strip()` with multi-character strings is misleading
   --> toolboxv2\mods\isaa\CodingAgent\live.py:919:33
    |
917 |                 if code.startswith('"""') and code.endswith('"""'):
918 |                     # Handle function definitions
919 |                     func_code = code.strip('"""')
    |                                 ^^^^^^^^^^^^^^^^^
920 |                     func_ast = ast.parse(func_code).body[0]
921 |                     func_name = func_ast.name
    |

B005 Using `.strip()` with multi-character strings is misleading
   --> toolboxv2\mods\isaa\CodingAgent\live.py:953:39
    |
952 |                     if code.startswith('"""') and code.endswith('"""'):
953 |                         method_code = code.strip('"""')
    |                                       ^^^^^^^^^^^^^^^^^
954 |
955 |                         # Use ast to parse the file and find the method to replace
    |

B005 Using `.strip()` with multi-character strings is misleading
   --> toolboxv2\mods\isaa\CodingAgent\live.py:979:37
    |
977 |                     if code.startswith('"""') and code.endswith('"""'):
978 |                         # Handle function updates
979 |                         func_code = code.strip('"""')
    |                                     ^^^^^^^^^^^^^^^^^
980 |                         func_pattern = fr"def {clean_object_name}.*?:(.*?)(?=\n\w|\Z)"
981 |                         func_match = re.search(func_pattern, original_content, re.DOTALL)
    |

SIM105 Use `contextlib.suppress(BaseException)` instead of `try`-`except`-`pass`
    --> toolboxv2\mods\isaa\CodingAgent\live.py:1307:25
     |
1305 |                   for key, value in self.ipython.user_ns.items():
1306 |                       if not key.startswith('_') and key not in ['__name__', '__builtins__']:
1307 | /                         try:
1308 | |                             self.variable_manager.set(f"python.{key}", value)
1309 | |                         except:
1310 | |                             pass  # Ignore non-serializable variables
     | |________________________________^
1311 |
1312 |               self._execution_history.append(('python', code, result))
     |
help: Replace with `contextlib.suppress(BaseException)`

S113 Probable use of `requests` call without timeout
  --> toolboxv2\mods\isaa\base\AgentUtils.py:75:16
   |
74 | def get_ip():
75 |     response = requests.get('https://api64.ipify.org?format=json').json()
   |                ^^^^^^^^^^^^
76 |     return response["ip"]
   |

S113 Probable use of `requests` call without timeout
  --> toolboxv2\mods\isaa\base\AgentUtils.py:81:16
   |
79 | def get_location():
80 |     ip_address = get_ip()
81 |     response = requests.get(f'https://ipapi.co/{ip_address}/json/').json()
   |                ^^^^^^^^^^^^
82 |     location_data = f"city: {response.get('city')},region: {response.get('region')},country: {response.get('country_name')},"
   |

SIM108 Use ternary operator `node_info = node_dict if index == 'question' else node_dict[index]` instead of `if`-`else`-block
   --> toolboxv2\mods\isaa\base\AgentUtils.py:439:13
    |
438 |               index = list(node_dict.keys())[0]  # Get the node's index.
439 | /             if index == 'question':
440 | |                 node_info = node_dict
441 | |             else:
442 | |                 node_info = node_dict[index]  # Get the node's info.
    | |____________________________________________^
443 |               return IsaaQuestionNode(
444 |                   node_info['question'],
    |
help: Replace `if`-`else`-block with `node_info = node_dict if index == 'question' else node_dict[index]`

S314 Using `xml` to parse untrusted data is known to be vulnerable to XML attacks; use `defusedxml` equivalents
   --> toolboxv2\mods\isaa\base\AgentUtils.py:518:28
    |
516 |                 if body_path in zf.namelist():
517 |                     xml_content = zf.read(body_path)
518 |                     tree = ET.fromstring(xml_content)
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^
519 |                     for para in tree.iter(f"{namespace}p"):
520 |                         texts_in_para = [node.text for node in para.iter(f"{namespace}t") if node.text]
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\AgentUtils.py:550:13
    |
548 |             return "\n".join(text_parts)
549 |         except Exception as e:
550 |             raise ValueError(f"PDF-Verarbeitung mit PyPDF2 fehlgeschlagen: {e}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
551 |
552 |     # 3. Fallback auf reinen Text (TXT)
    |

SIM222 Use `True` instead of `... or True`
   --> toolboxv2\mods\isaa\base\AgentUtils.py:553:8
    |
552 |     # 3. Fallback auf reinen Text (TXT)
553 |     if file_ext == 'txt' or True:
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^
554 |         try:
555 |             return data.decode('utf-8')
    |
help: Replace with `True`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\AgentUtils.py:560:17
    |
558 |                 return data.decode('latin-1')
559 |             except Exception as e:
560 |                 raise ValueError(f"Text-Dekodierung fehlgeschlagen: {e}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
561 |
562 |     # 4. Nicht unterstützte Formate
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\AgentUtils.py:697:17
    |
695 |                 texts = [text.replace('\\t', '').replace('\t', '')]
696 |             except Exception as e:
697 |                 raise ValueError(f"File processing failed: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
698 |         elif isinstance(data, str):
699 |             texts = [data.replace('\\t', '').replace('\t', '')]
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\AgentUtils.py:715:13
    |
713 |             import traceback
714 |             print(traceback.format_exc())
715 |             raise RuntimeError(f"Data addition failed: {str(e)}")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
716 |
717 |     def get(self, names):
    |

F403 `from toolboxv2.mods.isaa.base.Agent.types import *` used; unable to detect undefined names
  --> toolboxv2\mods\isaa\base\Agent\agent.py:67:1
   |
66 | from toolboxv2 import get_logger
67 | from toolboxv2.mods.isaa.base.Agent.types import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
68 |
69 | logger = get_logger()
   |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:119:47
    |
117 |             timer_key = f"{node_name}_total"
118 |             progress_tracker.start_timer(timer_key)
119 |             await progress_tracker.emit_event(ProgressEvent(
    |                                               ^^^^^^^^^^^^^
120 |                 event_type="node_enter",
121 |                 timestamp=time.time(),
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:125:52
    |
123 | …     session_id=shared.get("session_id"),
124 | …     task_id=shared.get("current_task_id"),
125 | …     plan_id=shared.get("current_plan", TaskPlan(id="none", name="none", description="none")).id if shared.get("current_plan") else …
    |                                          ^^^^^^^^
126 | …     status=NodeStatus.RUNNING,
127 | …     success=None
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:126:24
    |
124 | …         task_id=shared.get("current_task_id"),
125 | …         plan_id=shared.get("current_plan", TaskPlan(id="none", name="none", description="none")).id if shared.get("current_plan") e…
126 | …         status=NodeStatus.RUNNING,
    |                  ^^^^^^^^^^
127 | …         success=None
128 | …     ))
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:135:51
    |
134 |                 total_duration = progress_tracker.end_timer(timer_key)
135 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
136 |                     event_type="node_exit",
137 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:139:28
    |
137 |                     timestamp=time.time(),
138 |                     node_name=node_name,
139 |                     status=NodeStatus.COMPLETED,
    |                            ^^^^^^^^^^
140 |                     success=True,
141 |                     node_duration=total_duration,
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:151:51
    |
149 |             except Exception as e:
150 |                 total_duration = progress_tracker.end_timer(timer_key)
151 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
152 |                     event_type="error",
153 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:155:28
    |
153 |                     timestamp=time.time(),
154 |                     node_name=node_name,
155 |                     status=NodeStatus.FAILED,
    |                            ^^^^^^^^^^
156 |                     success=False,
157 |                     node_duration=total_duration,
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:179:47
    |
177 |             timer_key = f"{node_name}_prep"
178 |             progress_tracker.start_timer(timer_key)
179 |             await progress_tracker.emit_event(ProgressEvent(
    |                                               ^^^^^^^^^^^^^
180 |                 event_type="node_phase",
181 |                 timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:183:24
    |
181 |                 timestamp=time.time(),
182 |                 node_name=node_name,
183 |                 status=NodeStatus.STARTING,
    |                        ^^^^^^^^^^
184 |                 node_phase="prep",
185 |                 session_id=shared.get("session_id")
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:192:51
    |
191 |                 prep_duration = progress_tracker.end_timer(timer_key)
192 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
193 |                     event_type="node_phase",
194 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:195:28
    |
193 |                     event_type="node_phase",
194 |                     timestamp=time.time(),
195 |                     status=NodeStatus.RUNNING,
    |                            ^^^^^^^^^^
196 |                     success=True,
197 |                     node_name=node_name,
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:205:51
    |
203 |             except Exception as e:
204 |                 progress_tracker.end_timer(timer_key)
205 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
206 |                     event_type="error",
207 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:209:28
    |
207 |                     timestamp=time.time(),
208 |                     node_name=node_name,
209 |                     status=NodeStatus.FAILED,
    |                            ^^^^^^^^^^
210 |                     success=False,
211 |                     metadata={"error": str(e), "error_type": type(e).__name__},
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:232:47
    |
230 |             timer_key = f"{node_name}_exec"
231 |             progress_tracker.start_timer(timer_key)
232 |             await progress_tracker.emit_event(ProgressEvent(
    |                                               ^^^^^^^^^^^^^
233 |                 event_type="node_phase",
234 |                 timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:236:24
    |
234 |                 timestamp=time.time(),
235 |                 node_name=node_name,
236 |                 status=NodeStatus.RUNNING,
    |                        ^^^^^^^^^^
237 |                 node_phase="exec",
238 |                 session_id=prep_res.get("session_id") if isinstance(prep_res, dict) else None
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:245:47
    |
244 |             exec_duration = progress_tracker.end_timer(timer_key)
245 |             await progress_tracker.emit_event(ProgressEvent(
    |                                               ^^^^^^^^^^^^^
246 |                 event_type="node_phase",
247 |                 timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:249:24
    |
247 |                 timestamp=time.time(),
248 |                 node_name=node_name,
249 |                 status=NodeStatus.RUNNING,
    |                        ^^^^^^^^^^
250 |                 success=True,
251 |                 node_phase="exec_complete",
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:274:47
    |
272 |             timer_key_post = f"{node_name}_post"
273 |             progress_tracker.start_timer(timer_key_post)
274 |             await progress_tracker.emit_event(ProgressEvent(
    |                                               ^^^^^^^^^^^^^
275 |                 event_type="node_phase",
276 |                 timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:278:24
    |
276 |                 timestamp=time.time(),
277 |                 node_name=node_name,
278 |                 status=NodeStatus.COMPLETING,  # Neue Phase "completing"
    |                        ^^^^^^^^^^
279 |                 node_phase="post",
280 |                 session_id=shared.get("session_id")
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:291:51
    |
290 |                 # Sende das entscheidende "node_exit" Event nach erfolgreicher post-Phase
291 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
292 |                     event_type="node_exit",
293 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:295:28
    |
293 |                     timestamp=time.time(),
294 |                     node_name=node_name,
295 |                     status=NodeStatus.COMPLETED,
    |                            ^^^^^^^^^^
296 |                     success=True,
297 |                     node_duration=total_duration,
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:313:51
    |
311 |                 post_duration = progress_tracker.end_timer(timer_key_post)
312 |                 total_duration = progress_tracker.end_timer(f"{node_name}_total")
313 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
314 |                     event_type="error",
315 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:317:28
    |
315 |                     timestamp=time.time(),
316 |                     node_name=node_name,
317 |                     status=NodeStatus.FAILED,
    |                            ^^^^^^^^^^
318 |                     success=False,
319 |                     node_duration=total_duration,
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:338:51
    |
336 |                 timer_key = f"{node_name}_exec"
337 |                 exec_duration = progress_tracker.end_timer(timer_key)
338 |                 await progress_tracker.emit_event(ProgressEvent(
    |                                                   ^^^^^^^^^^^^^
339 |                     event_type="node_phase",
340 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:344:28
    |
342 |                     node_phase="exec_fallback",
343 |                     node_duration=exec_duration,
344 |                     status=NodeStatus.FAILED,
    |                            ^^^^^^^^^^
345 |                     success=False,
346 |                     session_id=prep_res.get("session_id") if isinstance(prep_res, dict) else None,
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:416:33
    |
414 |             return "planning_failed"
415 |
416 |         if isinstance(exec_res, TaskPlan):
    |                                 ^^^^^^^^
417 |
418 |             progress_tracker = shared.get("progress_tracker")
    |

F405 `ProgressEvent` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:420:21
    |
418 |             progress_tracker = shared.get("progress_tracker")
419 |             if progress_tracker:
420 |                 e = ProgressEvent(
    |                     ^^^^^^^^^^^^^
421 |                     event_type="plan_created",
422 |                     timestamp=time.time(),
    |

F405 `NodeStatus` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:423:28
    |
421 |                     event_type="plan_created",
422 |                     timestamp=time.time(),
423 |                     status=NodeStatus.COMPLETING,
    |                            ^^^^^^^^^^
424 |                     node_name="TaskPlannerNode",
425 |                     node_phase="plan_created",
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:458:54
    |
456 |             return "planning_failed"
457 |
458 |     async def _create_simple_plan(self, prep_res) -> TaskPlan:
    |                                                      ^^^^^^^^
459 |         """Fast lightweight planning for direct or simple multi-step queries."""
460 |         taw = self._build_tool_intelligence(prep_res)
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:547:20
    |
545 |             plan_data = yaml.safe_load(yaml_content)
546 |             # print("Simple", json.dumps(plan_data, indent=2))
547 |             return TaskPlan(
    |                    ^^^^^^^^
548 |                 id=str(uuid.uuid4()),
549 |                 name=plan_data.get("plan_name", "Generated Plan"),
    |

F405 `LLMTask` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:552:22
    |
550 |                 description=plan_data.get("description", f"Plan for: {prep_res['query']}"),
551 |                 tasks=[
552 |                     [LLMTask, ToolTask, DecisionTask, Task][["LLMTask", "ToolTask", "DecisionTask", "Task"].index(t.get("type"))](**t)
    |                      ^^^^^^^
553 |                     for t in plan_data.get("tasks", [])
554 |                 ],
    |

F405 `ToolTask` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:552:31
    |
550 |                 description=plan_data.get("description", f"Plan for: {prep_res['query']}"),
551 |                 tasks=[
552 |                     [LLMTask, ToolTask, DecisionTask, Task][["LLMTask", "ToolTask", "DecisionTask", "Task"].index(t.get("type"))](**t)
    |                               ^^^^^^^^
553 |                     for t in plan_data.get("tasks", [])
554 |                 ],
    |

F405 `DecisionTask` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:552:41
    |
550 |                 description=plan_data.get("description", f"Plan for: {prep_res['query']}"),
551 |                 tasks=[
552 |                     [LLMTask, ToolTask, DecisionTask, Task][["LLMTask", "ToolTask", "DecisionTask", "Task"].index(t.get("type"))](**t)
    |                                         ^^^^^^^^^^^^
553 |                     for t in plan_data.get("tasks", [])
554 |                 ],
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:552:55
    |
550 |                 description=plan_data.get("description", f"Plan for: {prep_res['query']}"),
551 |                 tasks=[
552 |                     [LLMTask, ToolTask, DecisionTask, Task][["LLMTask", "ToolTask", "DecisionTask", "Task"].index(t.get("type"))](**t)
    |                                                       ^^^^
553 |                     for t in plan_data.get("tasks", [])
554 |                 ],
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:562:20
    |
560 |             import traceback
561 |             print(traceback.format_exc())
562 |             return TaskPlan(
    |                    ^^^^^^^^
563 |                 id=str(uuid.uuid4()),
564 |                 name="Fallback Plan",
    |

F405 `LLMTask` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:567:21
    |
565 |                 description="Direct response only",
566 |                 tasks=[
567 |                     LLMTask(
    |                     ^^^^^^^
568 |                         id="fast_simple_planning",
569 |                         type="LLMTask",
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:579:62
    |
577 |             )
578 |
579 |     async def _advanced_llm_decomposition(self, prep_res) -> TaskPlan:
    |                                                              ^^^^^^^^
580 |         """Enhanced LLM-based decomposition with goals-based planning support"""
    |

F405 `create_task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:727:24
    |
725 |             for task_data in plan_data.get("tasks", []):
726 |                 task_type = task_data.pop("type", "generic")
727 |                 task = create_task(task_type, **task_data)
    |                        ^^^^^^^^^^^
728 |                 tasks.append(task)
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:730:20
    |
728 |                 tasks.append(task)
729 |
730 |             plan = TaskPlan(
    |                    ^^^^^^^^
731 |                 id=str(uuid.uuid4()),
732 |                 name=plan_data.get("plan_name", "Generated Plan"),
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:882:39
    |
880 |         }
881 |
882 |     def _find_ready_tasks(self, plan: TaskPlan, all_tasks: dict[str, Task]) -> list[Task]:
    |                                       ^^^^^^^^
883 |         """Finde Tasks die zur Ausführung bereit sind"""
884 |         ready = []
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:882:70
    |
880 |         }
881 |
882 |     def _find_ready_tasks(self, plan: TaskPlan, all_tasks: dict[str, Task]) -> list[Task]:
    |                                                                      ^^^^
883 |         """Finde Tasks die zur Ausführung bereit sind"""
884 |         ready = []
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:882:85
    |
880 |         }
881 |
882 |     def _find_ready_tasks(self, plan: TaskPlan, all_tasks: dict[str, Task]) -> list[Task]:
    |                                                                                     ^^^^
883 |         """Finde Tasks die zur Ausführung bereit sind"""
884 |         ready = []
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:890:41
    |
888 |         return ready
889 |
890 |     def _find_blocked_tasks(self, plan: TaskPlan, all_tasks: dict[str, Task]) -> list[Task]:
    |                                         ^^^^^^^^
891 |         """Finde blockierte Tasks für Analyse"""
892 |         blocked = []
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:890:72
    |
888 |         return ready
889 |
890 |     def _find_blocked_tasks(self, plan: TaskPlan, all_tasks: dict[str, Task]) -> list[Task]:
    |                                                                        ^^^^
891 |         """Finde blockierte Tasks für Analyse"""
892 |         blocked = []
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:890:87
    |
888 |         return ready
889 |
890 |     def _find_blocked_tasks(self, plan: TaskPlan, all_tasks: dict[str, Task]) -> list[Task]:
    |                                                                                       ^^^^
891 |         """Finde blockierte Tasks für Analyse"""
892 |         blocked = []
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:898:45
    |
896 |         return blocked
897 |
898 |     def _dependencies_satisfied(self, task: Task, all_tasks: dict[str, Task]) -> bool:
    |                                             ^^^^
899 |         """Prüfe ob alle Dependencies erfüllt sind"""
900 |         for dep_id in task.dependencies:
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:898:72
    |
896 |         return blocked
897 |
898 |     def _dependencies_satisfied(self, task: Task, all_tasks: dict[str, Task]) -> bool:
    |                                                                        ^^^^
899 |         """Prüfe ob alle Dependencies erfüllt sind"""
900 |         for dep_id in task.dependencies:
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:913:27
    |
911 |     async def _create_intelligent_execution_plan(
912 |         self,
913 |         ready_tasks: list[Task],
    |                           ^^^^
914 |         blocked_tasks: list[Task],
915 |         plan: TaskPlan,
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:914:29
    |
912 |         self,
913 |         ready_tasks: list[Task],
914 |         blocked_tasks: list[Task],
    |                             ^^^^
915 |         plan: TaskPlan,
916 |         shared: dict
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:915:15
    |
913 |         ready_tasks: list[Task],
914 |         blocked_tasks: list[Task],
915 |         plan: TaskPlan,
    |               ^^^^^^^^
916 |         shared: dict
917 |     ) -> dict[str, Any]:
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:935:63
    |
933 |         return await self._llm_execution_planning(ready_tasks, blocked_tasks, plan, shared)
934 |
935 |     def _create_simple_execution_plan(self, ready_tasks: list[Task], plan: TaskPlan) -> dict[str, Any]:
    |                                                               ^^^^
936 |         """Einfache heuristische Ausführungsplanung"""
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:935:76
    |
933 |         return await self._llm_execution_planning(ready_tasks, blocked_tasks, plan, shared)
934 |
935 |     def _create_simple_execution_plan(self, ready_tasks: list[Task], plan: TaskPlan) -> dict[str, Any]:
    |                                                                            ^^^^^^^^
936 |         """Einfache heuristische Ausführungsplanung"""
    |

F405 `ToolTask` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:947:33
    |
945 |         for task in sorted_tasks:
946 |             # ToolTasks können oft parallel laufen
947 |             if isinstance(task, ToolTask) and len(current_group) < self.max_parallel:
    |                                 ^^^^^^^^
948 |                 current_group.append(task)
949 |             else:
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:970:27
    |
968 |     async def _llm_execution_planning(
969 |         self,
970 |         ready_tasks: list[Task],
    |                           ^^^^
971 |         blocked_tasks: list[Task],
972 |         plan: TaskPlan,
    |

F405 `Task` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:971:29
    |
969 |         self,
970 |         ready_tasks: list[Task],
971 |         blocked_tasks: list[Task],
    |                             ^^^^
972 |         plan: TaskPlan,
973 |         shared: dict
    |

F405 `TaskPlan` may be undefined, or defined from star imports
   --> toolboxv2\mods\isaa\base\Agent\agent.py:972:15
    |
970 |         ready_tasks: list[Task],
971 |         blocked_tasks: list[Task],
972 |         plan: TaskPlan,
    |               ^^^^^^^^
973 |         shared: dict
974 |     ) -> dict[str, Any]:
    |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1066:56
     |
1064 |             return self._create_simple_execution_plan(ready_tasks, plan)
1065 |
1066 |     def _analyze_tasks_for_llm(self, ready_tasks: list[Task], blocked_tasks: list[Task]) -> dict[str, str]:
     |                                                        ^^^^
1067 |         """Analysiere Tasks für LLM-Prompt"""
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1066:83
     |
1064 |             return self._create_simple_execution_plan(ready_tasks, plan)
1065 |
1066 |     def _analyze_tasks_for_llm(self, ready_tasks: list[Task], blocked_tasks: list[Task]) -> dict[str, str]:
     |                                                                                   ^^^^
1067 |         """Analysiere Tasks für LLM-Prompt"""
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1074:33
     |
1072 |             if hasattr(task, 'priority'):
1073 |                 task_info += f" [Priority: {task.priority}]"
1074 |             if isinstance(task, ToolTask):
     |                                 ^^^^^^^^
1075 |                 task_info += f" [Tool: {task.tool_name}]"
1076 |                 if task.arguments:
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1115:70
     |
1113 |         return "\n".join(context_parts) if context_parts else "No previous execution history"
1114 |
1115 |     def _validate_execution_plan(self, plan: dict, ready_tasks: list[Task]) -> dict:
     |                                                                      ^^^^
1116 |         """Validiere und korrigiere LLM-generierten Ausführungsplan"""
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1156:46
     |
1154 |         return validated
1155 |
1156 |     def _estimate_duration(self, tasks: list[Task]) -> int:
     |                                              ^^^^
1157 |         """Schätze Ausführungsdauer in Sekunden"""
1158 |         duration = 0
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1160:33
     |
1158 |         duration = 0
1159 |         for task in tasks:
1160 |             if isinstance(task, ToolTask):
     |                                 ^^^^^^^^
1161 |                 duration += 10  # Tool calls meist schneller
1162 |             elif isinstance(task, LLMTask):
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1162:35
     |
1160 |             if isinstance(task, ToolTask):
1161 |                 duration += 10  # Tool calls meist schneller
1162 |             elif isinstance(task, LLMTask):
     |                                   ^^^^^^^
1163 |                 duration += 20  # LLM calls brauchen länger
1164 |             else:
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1312:78
     |
1310 |         return all_results
1311 |
1312 |     def _get_tasks_by_ids(self, task_ids: list[str], prep_res: dict) -> list[Task]:
     |                                                                              ^^^^
1313 |         """Hole Task-Objekte basierend auf IDs"""
1314 |         all_tasks = prep_res["all_tasks"]
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1324:57
     |
1322 |         return getattr(task, 'critical', False) or task.priority == 1
1323 |
1324 |     async def _execute_parallel_batch(self, tasks: list[Task]) -> list[dict]:
     |                                                         ^^^^
1325 |         """Führe Tasks parallel aus"""
1326 |         if not tasks:
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1357:59
     |
1355 |         return all_results
1356 |
1357 |     async def _execute_sequential_batch(self, tasks: list[Task]) -> list[dict]:
     |                                                           ^^^^
1358 |         """Führe Tasks sequenziell aus"""
1359 |         results = []
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1384:48
     |
1382 |         return results
1383 |
1384 |     async def _execute_single_task(self, task: Task) -> dict:
     |                                                ^^^^
1385 |         """Enhanced task execution with unified LLMToolNode usage"""
1386 |         if self.progress_tracker:
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1387:52
     |
1385 |         """Enhanced task execution with unified LLMToolNode usage"""
1386 |         if self.progress_tracker:
1387 |             await self.progress_tracker.emit_event(ProgressEvent(
     |                                                    ^^^^^^^^^^^^^
1388 |                 event_type="task_start",
1389 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1391:24
     |
1389 |                 timestamp=time.time(),
1390 |                 node_name="TaskExecutorNode",
1391 |                 status=NodeStatus.RUNNING,
     |                        ^^^^^^^^^^
1392 |                 task_id=task.id,
1393 |                 metadata={
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1412:33
     |
1411 |             # Pre-process task with variable resolution
1412 |             if isinstance(task, ToolTask):
     |                                 ^^^^^^^^
1413 |                 resolved_args = self._resolve_task_variables(task.arguments)
1414 |                 result = await self._execute_tool_task_with_validation(task, resolved_args)
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1415:35
     |
1413 |                 resolved_args = self._resolve_task_variables(task.arguments)
1414 |                 result = await self._execute_tool_task_with_validation(task, resolved_args)
1415 |             elif isinstance(task, LLMTask):
     |                                   ^^^^^^^
1416 |                 # Use LLMToolNode for LLM tasks instead of direct execution
1417 |                 result = await self._execute_llm_via_llmtool(task)
     |

F405 `DecisionTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1418:35
     |
1416 |                 # Use LLMToolNode for LLM tasks instead of direct execution
1417 |                 result = await self._execute_llm_via_llmtool(task)
1418 |             elif isinstance(task, DecisionTask):
     |                                   ^^^^^^^^^^^^
1419 |                 # Enhanced decision task with context awareness
1420 |                 result = await self._execute_decision_task_enhanced(task)
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1435:56
     |
1434 |             if self.progress_tracker:
1435 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
1436 |                     event_type="task_complete",
1437 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1440:28
     |
1438 |                     node_name="TaskExecutorNode",
1439 |                     task_id=task.id,
1440 |                     status=NodeStatus.COMPLETED,
     |                            ^^^^^^^^^^
1441 |                     success=True,
1442 |                     node_duration=task_duration,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1467:56
     |
1466 |             if self.progress_tracker:
1467 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
1468 |                     event_type="task_error",
1469 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1472:28
     |
1470 |                     node_name="TaskExecutorNode",
1471 |                     task_id=task.id,
1472 |                     status=NodeStatus.FAILED,
     |                            ^^^^^^^^^^
1473 |                     success=False,
1474 |                     node_duration=task_duration,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1485:56
     |
1483 |                 ))
1484 |
1485 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
1486 |                     event_type="error",
1487 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1490:28
     |
1488 |                     node_name="TaskExecutorNode",
1489 |                     task_id=task.id,
1490 |                     status=NodeStatus.FAILED,
     |                            ^^^^^^^^^^
1491 |                     success=False,
1492 |                     node_duration=task_duration,
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1530:62
     |
1528 |         return resolved
1529 |
1530 |     async def _execute_tool_task_with_validation(self, task: ToolTask, resolved_args: dict[str, Any]) -> Any:
     |                                                              ^^^^^^^^
1531 |         """Tool execution with improved error detection and validation"""
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1544:52
     |
1542 |         # Track tool call start
1543 |         if self.progress_tracker:
1544 |             await self.progress_tracker.emit_event(ProgressEvent(
     |                                                    ^^^^^^^^^^^^^
1545 |                 event_type="tool_call",
1546 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1548:24
     |
1546 |                 timestamp=time.time(),
1547 |                 node_name="TaskExecutorNode",
1548 |                 status=NodeStatus.RUNNING,
     |                        ^^^^^^^^^^
1549 |                 task_id=task.id,
1550 |                 tool_name=task.tool_name,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1575:56
     |
1573 |             # Track successful tool call
1574 |             if self.progress_tracker:
1575 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
1576 |                     event_type="tool_call",
1577 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1580:28
     |
1578 |                     node_name="TaskExecutorNode",
1579 |                     task_id=task.id,
1580 |                     status=NodeStatus.COMPLETED,
     |                            ^^^^^^^^^^
1581 |                     tool_name=task.tool_name,
1582 |                     tool_args=resolved_args,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1608:56
     |
1606 |             # Detailed error tracking
1607 |             if self.progress_tracker:
1608 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
1609 |                     event_type="tool_call",
1610 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1613:28
     |
1611 |                     node_name="TaskExecutorNode",
1612 |                     task_id=task.id,
1613 |                     status=NodeStatus.FAILED,
     |                            ^^^^^^^^^^
1614 |                     tool_name=task.tool_name,
1615 |                     tool_args=resolved_args,
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1628:52
     |
1626 |             eprint(f"Tool execution failed for {task.tool_name}: {e}")
1627 |             raise
1628 |     async def _execute_llm_via_llmtool(self, task: LLMTask) -> Any:
     |                                                    ^^^^^^^
1629 |         """Execute LLM task via LLMToolNode for consistency"""
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1668:54
     |
1666 |             return await self._execute_llm_task_enhanced(task)
1667 |
1668 |     async def _execute_llm_task_enhanced(self, task: LLMTask) -> Any:
     |                                                      ^^^^^^^
1669 |         """Enhanced LLM task execution with unified variable system"""
1670 |         if not LITELLM_AVAILABLE:
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1746:56
     |
1745 |             if self.progress_tracker:
1746 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
1747 |                     event_type="error",
1748 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1749:28
     |
1747 |                     event_type="error",
1748 |                     timestamp=time.time(),
1749 |                     status=NodeStatus.FAILED,
     |                            ^^^^^^^^^^
1750 |                     node_name="TaskExecutorNode",
1751 |                     task_id=task.id,
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1763:56
     |
1761 |             raise
1762 |
1763 |     async def _execute_generic_via_llmtool(self, task: Task) -> Any:
     |                                                        ^^^^
1764 |         """
1765 |         Execute a generic task by treating its description as a query for the LLMToolNode.
     |

F405 `DecisionTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1814:59
     |
1812 |             raise
1813 |
1814 |     async def _execute_decision_task_enhanced(self, task: DecisionTask) -> str:
     |                                                           ^^^^^^^^^^^^
1815 |         """Enhanced DecisionTask with intelligent replan assessment"""
     |

SIM118 Use `key in dict` instead of `key in dict.keys()`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:1861:17
     |
1859 |             # Find matching key (case-insensitive)
1860 |             matched_key = None
1861 |             for key in task.routing_map.keys():
     |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1862 |                 if key.lower() == decision:
1863 |                     matched_key = key
     |
help: Remove `.keys()`

F405 `DecisionTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2083:45
     |
2081 |             self.variable_manager.set(f"results.{task_id}.error", error)
2082 |
2083 |     def _build_decision_context(self, task: DecisionTask) -> str:
     |                                             ^^^^^^^^^^^^
2084 |         """Build comprehensive context for decision making"""
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2164:56
     |
2162 |             raise RuntimeError(f"Tool {tool_name} failed without exception")
2163 |
2164 |     def _validate_tool_result(self, result: Any, task: ToolTask) -> bool:
     |                                                        ^^^^^^^^
2165 |         """Validate tool result to prevent false failures"""
     |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2203:12
     |
2201 |             return result
2202 |
2203 |         if isinstance(result, (list, dict)):
     |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2204 |             return len(result) > 0
     |
help: Convert to `X | Y`

B007 Loop control variable `task_id` not used within loop body
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2250:13
     |
2249 |         # Analyze failed tasks
2250 |         for task_id, result_data in self.results_store.items():
     |             ^^^^^^^
2251 |             if not result_data.get("metadata", {}).get("success", True):
2252 |                 error = result_data.get("error", "")
     |
help: Rename unused `task_id` to `_task_id`

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2341:61
     |
2340 |         if shared.get("progress_tracker"):
2341 |             await shared.get("progress_tracker").emit_event(ProgressEvent(
     |                                                             ^^^^^^^^^^^^^
2342 |                 event_type="llm_call",
2343 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2345:24
     |
2343 |                 timestamp=time.time(),
2344 |                 node_name="LLMToolNode",
2345 |                 status=NodeStatus.STARTING,
     |                        ^^^^^^^^^^
2346 |                 llm_model="auto",
2347 |                 session_id=shared.get("session_id"),
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2444:55
     |
2443 |                 if progress_tracker:
2444 |                     await progress_tracker.emit_event(ProgressEvent(
     |                                                       ^^^^^^^^^^^^^
2445 |                         event_type="error",
2446 |                         timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2448:32
     |
2446 |                         timestamp=time.time(),
2447 |                         node_name="LLMToolNode",
2448 |                         status=NodeStatus.FAILED,
     |                                ^^^^^^^^^^
2449 |                         llm_model=model_to_use,
2450 |                         llm_duration=llm_duration,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2463:47
     |
2461 |         # Apply persona if needed
2462 |         if progress_tracker:
2463 |             await progress_tracker.emit_event(ProgressEvent(
     |                                               ^^^^^^^^^^^^^
2464 |                 event_type="success",
2465 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2467:24
     |
2465 |                 timestamp=time.time(),
2466 |                 node_name="LLMToolNode",
2467 |                 status=NodeStatus.COMPLETED,
     |                        ^^^^^^^^^^
2468 |                 success=True,
2469 |                 llm_model="auto",
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2719:51
     |
2718 |             if progress_tracker:
2719 |                 await progress_tracker.emit_event(ProgressEvent(
     |                                                   ^^^^^^^^^^^^^
2720 |                     event_type="tool_call",
2721 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2722:28
     |
2720 |                     event_type="tool_call",
2721 |                     timestamp=time.time(),
2722 |                     status=NodeStatus.RUNNING,
     |                            ^^^^^^^^^^
2723 |                     node_name="LLMToolNode",
2724 |                     tool_name=tool_name,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2747:55
     |
2745 |                 variable_manager.set(f"results.{tool_name}.data", result)
2746 |                 if progress_tracker:
2747 |                     await progress_tracker.emit_event(ProgressEvent(
     |                                                       ^^^^^^^^^^^^^
2748 |                         event_type="tool_call",
2749 |                         timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2751:32
     |
2749 |                         timestamp=time.time(),
2750 |                         node_name="LLMToolNode",
2751 |                         status=NodeStatus.COMPLETED,
     |                                ^^^^^^^^^^
2752 |                         tool_name=tool_name,
2753 |                         tool_args=resolved_args,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2779:55
     |
2778 |                 if progress_tracker:
2779 |                     await progress_tracker.emit_event(ProgressEvent(
     |                                                       ^^^^^^^^^^^^^
2780 |                         event_type="tool_call",
2781 |                         timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2783:32
     |
2781 |                         timestamp=time.time(),
2782 |                         node_name="LLMToolNode",
2783 |                         status=NodeStatus.FAILED,
     |                                ^^^^^^^^^^
2784 |                         tool_name=tool_name,
2785 |                         tool_args=arguments,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2798:55
     |
2797 |                     # FIXED: Also send generic error event for error log
2798 |                     await progress_tracker.emit_event(ProgressEvent(
     |                                                       ^^^^^^^^^^^^^
2799 |                         event_type="error",
2800 |                         timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:2802:32
     |
2800 |                         timestamp=time.time(),
2801 |                         node_name="LLMToolNode",
2802 |                         status=NodeStatus.FAILED,
     |                                ^^^^^^^^^^
2803 |                         success=False,
2804 |                         tool_name=tool_name,
     |

B007 Loop control variable `task_id` not used within loop body
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3488:13
     |
3486 |     def _format_successful_results(self, results: dict) -> str:
3487 |         formatted = []
3488 |         for task_id, result_info in results.items():
     |             ^^^^^^^
3489 |             formatted.append(f"- {result_info['task_description']}: {str(result_info['result'])[:20000]}...")
3490 |         return "\n".join(formatted) if formatted else "No successful results to report."
     |
help: Rename unused `task_id` to `_task_id`

B007 Loop control variable `task_id` not used within loop body
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3519:17
     |
3517 |         if context.get("successful_results"):
3518 |             response_parts.append("\nDetailed results:")
3519 |             for task_id, result in list(context["successful_results"].items())[:2]:  # Top 2
     |                 ^^^^^^^
3520 |                 response_parts.append(f"- {result['task_description']}: {str(result['result'])[:150]}")
     |
help: Rename unused `task_id` to `_task_id`

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3620:45
     |
3618 |         }
3619 |
3620 |     def _get_format_config(self, shared) -> FormatConfig | None:
     |                                             ^^^^^^^^^^^^
3621 |         """Extrahiere Format-Konfiguration"""
3622 |         persona = shared.get("persona_config")
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3675:78
     |
3673 |         }
3674 |
3675 |     async def _evaluate_format_adherence(self, response: str, format_config: FormatConfig | None) -> float:
     |                                                                              ^^^^^^^^^^^^
3676 |         """Bewerte Format-Einhaltung"""
3677 |         if not format_config:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3684:27
     |
3683 |         # Format-spezifische Checks
3684 |         if format_type == ResponseFormat.WITH_TABLES:
     |                           ^^^^^^^^^^^^^^
3685 |             if '|' in response or 'Table:' in response or '| ' in response:
3686 |                 score += 0.4
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3688:29
     |
3686 |                 score += 0.4
3687 |
3688 |         elif format_type == ResponseFormat.WITH_BULLET_POINTS:
     |                             ^^^^^^^^^^^^^^
3689 |             bullet_count = response.count('•') + response.count('-') + response.count('*')
3690 |             if bullet_count >= 2:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3695:29
     |
3693 |                 score += 0.2
3694 |
3695 |         elif format_type == ResponseFormat.WITH_LISTS:
     |                             ^^^^^^^^^^^^^^
3696 |             list_patterns = ['1.', '2.', '3.', 'a)', 'b)', 'c)']
3697 |             list_score = sum(1 for pattern in list_patterns if pattern in response)
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3700:29
     |
3698 |             score += min(0.4, list_score * 0.1)
3699 |
3700 |         elif format_type == ResponseFormat.MD_TEXT:
     |                             ^^^^^^^^^^^^^^
3701 |             md_elements = ['#', '**', '*', '`', '```', '[', ']', '(', ')']
3702 |             md_score = sum(1 for element in md_elements if element in response)
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3705:29
     |
3703 |             score += min(0.4, md_score * 0.05)
3704 |
3705 |         elif format_type == ResponseFormat.YAML_TEXT:
     |                             ^^^^^^^^^^^^^^
3706 |             if response.strip().startswith(('```yaml', '---')) or ': ' in response:
3707 |                 score += 0.4
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3709:29
     |
3707 |                 score += 0.4
3708 |
3709 |         elif format_type == ResponseFormat.JSON_TEXT:
     |                             ^^^^^^^^^^^^^^
3710 |             if response.strip().startswith(('{', '[')):
3711 |                 try:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3717:29
     |
3715 |                     score += 0.1  # Partial credit for JSON-like structure
3716 |
3717 |         elif format_type == ResponseFormat.TEXT_ONLY:
     |                             ^^^^^^^^^^^^^^
3718 |             # Penalize if formatting elements are present
3719 |             format_elements = ['#', '*', '|', '```', '1.', '•', '-']
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3723:29
     |
3721 |             score += max(0.1, 0.5 - format_count * 0.05)
3722 |
3723 |         elif format_type == ResponseFormat.PSEUDO_CODE:
     |                             ^^^^^^^^^^^^^^
3724 |             code_indicators = ['if ', 'for ', 'while ', 'def ', 'return ', 'function', 'BEGIN', 'END']
3725 |             code_score = sum(1 for indicator in code_indicators if indicator in response)
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3730:72
     |
3728 |         return max(0.0, min(1.0, score))
3729 |
3730 |     def _evaluate_length_adherence(self, response: str, format_config: FormatConfig | None) -> float:
     |                                                                        ^^^^^^^^^^^^
3731 |         """Bewerte Längen-Einhaltung"""
3732 |         if not format_config:
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3753:24
     |
3751 |         response: str,
3752 |         query: str,
3753 |         format_config: FormatConfig | None,
     |                        ^^^^^^^^^^^^
3754 |         prep_res: dict
3755 |     ) -> float:
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3802:24
     |
3800 |         score: float,
3801 |         response: str,
3802 |         format_config: FormatConfig | None,
     |                        ^^^^^^^^^^^^
3803 |         quality_details: dict
3804 |     ) -> list[str]:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3815:31
     |
3813 |             format_type = format_config.response_format
3814 |
3815 |             if format_type == ResponseFormat.WITH_TABLES:
     |                               ^^^^^^^^^^^^^^
3816 |                 suggestions.append("Add tables using markdown format (| Column | Column |)")
3817 |             elif format_type == ResponseFormat.WITH_BULLET_POINTS:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3817:33
     |
3815 |             if format_type == ResponseFormat.WITH_TABLES:
3816 |                 suggestions.append("Add tables using markdown format (| Column | Column |)")
3817 |             elif format_type == ResponseFormat.WITH_BULLET_POINTS:
     |                                 ^^^^^^^^^^^^^^
3818 |                 suggestions.append("Use bullet points (•, -, *) to structure information")
3819 |             elif format_type == ResponseFormat.MD_TEXT:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3819:33
     |
3817 |             elif format_type == ResponseFormat.WITH_BULLET_POINTS:
3818 |                 suggestions.append("Use bullet points (•, -, *) to structure information")
3819 |             elif format_type == ResponseFormat.MD_TEXT:
     |                                 ^^^^^^^^^^^^^^
3820 |                 suggestions.append("Use markdown formatting (headers, bold, code blocks)")
3821 |             elif format_type == ResponseFormat.YAML_TEXT:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3821:33
     |
3819 |             elif format_type == ResponseFormat.MD_TEXT:
3820 |                 suggestions.append("Use markdown formatting (headers, bold, code blocks)")
3821 |             elif format_type == ResponseFormat.YAML_TEXT:
     |                                 ^^^^^^^^^^^^^^
3822 |                 suggestions.append("Format response as valid YAML structure")
3823 |             elif format_type == ResponseFormat.JSON_TEXT:
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:3823:33
     |
3821 |             elif format_type == ResponseFormat.YAML_TEXT:
3822 |                 suggestions.append("Format response as valid YAML structure")
3823 |             elif format_type == ResponseFormat.JSON_TEXT:
     |                                 ^^^^^^^^^^^^^^
3824 |                 suggestions.append("Format response as valid JSON")
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:4155:51
     |
4153 |             # Progress tracking
4154 |             if progress_tracker:
4155 |                 await progress_tracker.emit_event(ProgressEvent(
     |                                                   ^^^^^^^^^^^^^
4156 |                     event_type="reasoning_loop",
4157 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:4159:28
     |
4157 |                     timestamp=time.time(),
4158 |                     node_name="LLMReasonerNode",
4159 |                     status=NodeStatus.RUNNING,
     |                            ^^^^^^^^^^
4160 |                     metadata={
4161 |                         "loop_number": self.current_loop_count,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:4496:71
     |
4495 |             if self.agent_instance and self.agent_instance.progress_tracker:
4496 |                 await self.agent_instance.progress_tracker.emit_event(ProgressEvent(
     |                                                                       ^^^^^^^^^^^^^
4497 |                     event_type="llm_call",
4498 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:4500:28
     |
4498 |                     timestamp=time.time(),
4499 |                     node_name="LLMReasonerNode",
4500 |                     status=NodeStatus.COMPLETED,
     |                            ^^^^^^^^^^
4501 |                     task_id="create_initial_outline",
4502 |                     metadata={"outline": outline}
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5502:47
     |
5500 |         if not matches and progress_tracker:
5501 |             # No meta-tools found in response
5502 |             await progress_tracker.emit_event(ProgressEvent(
     |                                               ^^^^^^^^^^^^^
5503 |                 event_type="meta_tool_analysis",
5504 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5506:24
     |
5504 |                 timestamp=time.time(),
5505 |                 node_name="LLMReasonerNode",
5506 |                 status=NodeStatus.COMPLETED,
     |                        ^^^^^^^^^^
5507 |                 session_id=session_id,
5508 |                 metadata={
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5535:51
     |
5534 |             if progress_tracker:
5535 |                 await progress_tracker.emit_event(ProgressEvent(
     |                                                   ^^^^^^^^^^^^^
5536 |                     event_type="meta_tool_call",
5537 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5539:28
     |
5537 |                     timestamp=time.time(),
5538 |                     node_name="LLMReasonerNode",
5539 |                     status=NodeStatus.STARTING,
     |                            ^^^^^^^^^^
5540 |                     session_id=session_id,
5541 |                     task_id=f"meta_tool_{tool_name}_{i + 1}",
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5705:59
     |
5703 |                         execution_details["execution_success"] = True
5704 |
5705 |                         await progress_tracker.emit_event(ProgressEvent(
     |                                                           ^^^^^^^^^^^^^
5706 |                             event_type="meta_tool_call",
5707 |                             timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5709:36
     |
5707 | …                     timestamp=time.time(),
5708 | …                     node_name="LLMReasonerNode",
5709 | …                     status=NodeStatus.COMPLETED,
     |                              ^^^^^^^^^^
5710 | …                     session_id=session_id,
5711 | …                     task_id=f"meta_tool_{tool_name}_{i + 1}",
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5746:59
     |
5744 |                     if progress_tracker:
5745 |                         meta_tool_duration = time.perf_counter() - meta_tool_start
5746 |                         await progress_tracker.emit_event(ProgressEvent(
     |                                                           ^^^^^^^^^^^^^
5747 |                             event_type="meta_tool_call",
5748 |                             timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5750:36
     |
5748 | …                     timestamp=time.time(),
5749 | …                     node_name="LLMReasonerNode",
5750 | …                     status=NodeStatus.FAILED,
     |                              ^^^^^^^^^^
5751 | …                     session_id=session_id,
5752 | …                     task_id=f"meta_tool_{tool_name}_{i + 1}",
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5784:55
     |
5782 |                 # Emit success event
5783 |                 if progress_tracker:
5784 |                     await progress_tracker.emit_event(ProgressEvent(
     |                                                       ^^^^^^^^^^^^^
5785 |                         event_type="meta_tool_call",
5786 |                         timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5788:32
     |
5786 |                         timestamp=time.time(),
5787 |                         node_name="LLMReasonerNode",
5788 |                         status=NodeStatus.COMPLETED,
     |                                ^^^^^^^^^^
5789 |                         session_id=session_id,
5790 |                         task_id=f"meta_tool_{tool_name}_{i + 1}",
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5817:55
     |
5816 |                 if progress_tracker:
5817 |                     await progress_tracker.emit_event(ProgressEvent(
     |                                                       ^^^^^^^^^^^^^
5818 |                         event_type="meta_tool_call",
5819 |                         timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5821:32
     |
5819 |                         timestamp=time.time(),
5820 |                         node_name="LLMReasonerNode",
5821 |                         status=NodeStatus.FAILED,
     |                                ^^^^^^^^^^
5822 |                         session_id=session_id,
5823 |                         task_id=f"meta_tool_{tool_name}_{i + 1}",
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5839:47
     |
5837 |             reasoning_progress = self._assess_reasoning_progress()
5838 |
5839 |             await progress_tracker.emit_event(ProgressEvent(
     |                                               ^^^^^^^^^^^^^
5840 |                 event_type="meta_tool_batch_complete",
5841 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:5843:24
     |
5841 |                 timestamp=time.time(),
5842 |                 node_name="LLMReasonerNode",
5843 |                 status=NodeStatus.COMPLETED,
     |                        ^^^^^^^^^^
5844 |                 session_id=session_id,
5845 |                 metadata={
     |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:6175:12
     |
6173 |     def _format_variable_value(self, value: any) -> str:
6174 |         """Format variable value for display with intelligent truncation"""
6175 |         if isinstance(value, (dict, list)):
     |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
6176 |             value_str = json.dumps(value, default=str, indent=2)
6177 |         else:
     |
help: Convert to `X | Y`

SIM118 Use `key in dict` instead of `key in dict.keys()`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:6206:21
     |
6204 |             if latest.get('results'):
6205 |                 discovery_msg += "\n\n🔍 Available result keys:"
6206 |                 for result_id in latest['results'].keys():
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
6207 |                     discovery_msg += f"\n• results.{result_id}.data"
     |
help: Remove `.keys()`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7196:21
     |
7194 |                     current = current[index]
7195 |                 except (ValueError, IndexError):
7196 |                     raise KeyError(f"Invalid list index '{part}' in path '{path}'")
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7197 |             elif isinstance(current, dict):
7198 |                 try:
     |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7202:21
     |
7200 |                     current = current[part]
7201 |                 except KeyError:
7202 |                     raise KeyError(f"Key '{part}' not found in path '{path}'")
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7203 |             else:
7204 |                 # We've hit a non-collection type (int, str, etc.) but the path continues
     |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7271:21
     |
7269 |                 key = part
7270 |                 if not isinstance(current, dict):
7271 |                     raise TypeError(f"Attempted to use string key '{key}' on non-dict for path '{path}'")
     |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7272 |
7273 |                 if key not in current:
     |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7380:14
     |
7378 |         if isinstance(value, str):
7379 |             return value
7380 |         elif isinstance(value, (dict, list)):
     |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7381 |             return json.dumps(value, default=str)
7382 |         else:
     |
help: Convert to `X | Y`

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7426:47
     |
7424 |         return info
7425 |
7426 |     def _validate_task_references(self, task: Task) -> dict[str, Any]:
     |                                               ^^^^
7427 |         """Validate all variable references in a task"""
7428 |         validation_results = {
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7435:29
     |
7434 |         # Check different task types
7435 |         if isinstance(task, LLMTask):
     |                             ^^^^^^^
7436 |             if task.prompt_template:
7437 |                 refs = self.validate_references(task.prompt_template)
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7443:31
     |
7441 |                         validation_results['valid'] = False
7442 |
7443 |         elif isinstance(task, ToolTask):
     |                               ^^^^^^^^
7444 |             for key, value in task.arguments.items():
7445 |                 if isinstance(value, str):
     |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7502:20
     |
7501 |                 # Recurse into nested structures
7502 |                 if isinstance(value, (dict, list)):
     |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7503 |                     self._document_structure(value, current_path, docs)
     |
help: Convert to `X | Y`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7526:20
     |
7525 |                 # Recurse into nested structures
7526 |                 if isinstance(item, (dict, list)):
     |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7527 |                     self._document_structure(item, current_path, docs)
     |
help: Convert to `X | Y`

SIM101 Multiple `isinstance` calls for `scope_data`, merge into a single call
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7547:18
     |
7545 |             elif isinstance(scope_data, list):
7546 |                 preview = f"List with {len(scope_data)} items"
7547 |             elif isinstance(scope_data, str) or isinstance(scope_data, int):
     |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7548 |                 preview = str(scope_data)
7549 |             else:
     |
help: Merge `isinstance` calls for `scope_data`

SIM118 Use `key in dict` instead of `key in dict.keys()`
    --> toolboxv2\mods\isaa\base\Agent\agent.py:7979:37
     |
7977 |         if session_id:
7978 |             # Remove all cache entries for this session
7979 |             keys_to_remove = [k for k in self._context_cache.keys() if session_id in k]
     |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
7980 |             for key in keys_to_remove:
7981 |                 del self._context_cache[key]
     |
help: Remove `.keys()`

F405 `AgentModelData` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8071:14
     |
8069 |     def __init__(
8070 |         self,
8071 |         amd: AgentModelData,
     |              ^^^^^^^^^^^^^^
8072 |         world_model: dict[str, Any] = None,
8073 |         verbose: bool = False,
     |

F405 `ProgressTracker` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8086:33
     |
8084 |         self.checkpoint_interval = checkpoint_interval
8085 |         self.max_parallel_tasks = max_parallel_tasks
8086 |         self.progress_tracker = ProgressTracker(progress_callback, agent_name=amd.name)
     |                                 ^^^^^^^^^^^^^^^
8087 |
8088 |         # Core state
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8162:52
     |
8161 |         if self.progress_tracker:
8162 |             await self.progress_tracker.emit_event(ProgressEvent(
     |                                                    ^^^^^^^^^^^^^
8163 |                 event_type="llm_call",
8164 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8165:24
     |
8163 |                 event_type="llm_call",
8164 |                 timestamp=time.time(),
8165 |                 status=NodeStatus.RUNNING,
     |                        ^^^^^^^^^^
8166 |                 node_name=node_name,
8167 |                 task_id=task_id,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8221:56
     |
8220 |             if self.progress_tracker:
8221 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
8222 |                     event_type="llm_call",
8223 |                     timestamp=time.time(),
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8247:56
     |
8246 |             if self.progress_tracker:
8247 |                 await self.progress_tracker.emit_event(ProgressEvent(
     |                                                        ^^^^^^^^^^^^^
8248 |                     event_type="error",
8249 |                     timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8250:28
     |
8248 |                     event_type="error",
8249 |                     timestamp=time.time(),
8250 |                     status=NodeStatus.FAILED,
     |                            ^^^^^^^^^^
8251 |                     node_name=node_name,
8252 |                     task_id=task_id,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8278:48
     |
8276 |         self.active_session = session_id
8277 |         result = None
8278 |         await self.progress_tracker.emit_event(ProgressEvent(
     |                                                ^^^^^^^^^^^^^
8279 |             event_type="execution_start",
8280 |             timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8281:20
     |
8279 |             event_type="execution_start",
8280 |             timestamp=time.time(),
8281 |             status=NodeStatus.RUNNING,
     |                    ^^^^^^^^^^
8282 |             node_name="FlowAgent",
8283 |             session_id=session_id,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8354:52
     |
8352 |             total_duration = self.progress_tracker.end_timer("total_execution")
8353 |
8354 |             await self.progress_tracker.emit_event(ProgressEvent(
     |                                                    ^^^^^^^^^^^^^
8355 |                 event_type="execution_complete",
8356 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8358:24
     |
8356 |                 timestamp=time.time(),
8357 |                 node_name="FlowAgent",
8358 |                 status=NodeStatus.COMPLETED,
     |                        ^^^^^^^^^^
8359 |                 node_duration=total_duration,
8360 |                 session_id=session_id,
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8396:52
     |
8394 |             total_duration = self.progress_tracker.end_timer("total_execution")
8395 |
8396 |             await self.progress_tracker.emit_event(ProgressEvent(
     |                                                    ^^^^^^^^^^^^^
8397 |                 event_type="error",
8398 |                 timestamp=time.time(),
     |

F405 `NodeStatus` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8400:24
     |
8398 |                 timestamp=time.time(),
8399 |                 node_name="FlowAgent",
8400 |                 status=NodeStatus.FAILED,
     |                        ^^^^^^^^^^
8401 |                 node_duration=total_duration,
8402 |                 session_id=session_id,
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8424:13
     |
8422 |         # Validiere Eingaben
8423 |         try:
8424 |             ResponseFormat(response_format)
     |             ^^^^^^^^^^^^^^
8425 |             TextLength(text_length)
8426 |         except ValueError:
     |

F405 `TextLength` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8425:13
     |
8423 |         try:
8424 |             ResponseFormat(response_format)
8425 |             TextLength(text_length)
     |             ^^^^^^^^^^
8426 |         except ValueError:
8427 |             available_formats = [f.value for f in ResponseFormat]
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8427:51
     |
8425 |             TextLength(text_length)
8426 |         except ValueError:
8427 |             available_formats = [f.value for f in ResponseFormat]
     |                                                   ^^^^^^^^^^^^^^
8428 |             available_lengths = [l.value for l in TextLength]
8429 |             raise ValueError(
     |

F405 `TextLength` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8428:51
     |
8426 |         except ValueError:
8427 |             available_formats = [f.value for f in ResponseFormat]
8428 |             available_lengths = [l.value for l in TextLength]
     |                                                   ^^^^^^^^^^
8429 |             raise ValueError(
8430 |                 f"Invalid format or length. "
     |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8429:13
     |
8427 |               available_formats = [f.value for f in ResponseFormat]
8428 |               available_lengths = [l.value for l in TextLength]
8429 | /             raise ValueError(
8430 | |                 f"Invalid format or length. "
8431 | |                 f"Available formats: {available_formats}. "
8432 | |                 f"Available lengths: {available_lengths}"
8433 | |             )
     | |_____________^
8434 |
8435 |           # Erstelle oder aktualisiere Persona
     |

F405 `PersonaConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8437:32
     |
8435 |         # Erstelle oder aktualisiere Persona
8436 |         if not self.amd.persona:
8437 |             self.amd.persona = PersonaConfig(name="Assistant")
     |                                ^^^^^^^^^^^^^
8438 |
8439 |         # Erstelle Format-Konfiguration
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8440:25
     |
8439 |         # Erstelle Format-Konfiguration
8440 |         format_config = FormatConfig(
     |                         ^^^^^^^^^^^^
8441 |             response_format=ResponseFormat(response_format),
8442 |             text_length=TextLength(text_length),
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8441:29
     |
8439 |         # Erstelle Format-Konfiguration
8440 |         format_config = FormatConfig(
8441 |             response_format=ResponseFormat(response_format),
     |                             ^^^^^^^^^^^^^^
8442 |             text_length=TextLength(text_length),
8443 |             custom_instructions=custom_instructions,
     |

F405 `TextLength` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8442:25
     |
8440 |         format_config = FormatConfig(
8441 |             response_format=ResponseFormat(response_format),
8442 |             text_length=TextLength(text_length),
     |                         ^^^^^^^^^^
8443 |             custom_instructions=custom_instructions,
8444 |             quality_threshold=quality_threshold
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8505:42
     |
8503 |         """Erhalte verfügbare Format- und Längen-Optionen"""
8504 |         return {
8505 |             "formats": [f.value for f in ResponseFormat],
     |                                          ^^^^^^^^^^^^^^
8506 |             "lengths": [l.value for l in TextLength],
8507 |             "format_descriptions": {
     |

F405 `TextLength` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8506:42
     |
8504 |         return {
8505 |             "formats": [f.value for f in ResponseFormat],
8506 |             "lengths": [l.value for l in TextLength],
     |                                          ^^^^^^^^^^
8507 |             "format_descriptions": {
8508 |                 f.value: FormatConfig(response_format=f).get_format_instructions()
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8508:26
     |
8506 |             "lengths": [l.value for l in TextLength],
8507 |             "format_descriptions": {
8508 |                 f.value: FormatConfig(response_format=f).get_format_instructions()
     |                          ^^^^^^^^^^^^
8509 |                 for f in ResponseFormat
8510 |             },
     |

F405 `ResponseFormat` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8509:26
     |
8507 |             "format_descriptions": {
8508 |                 f.value: FormatConfig(response_format=f).get_format_instructions()
8509 |                 for f in ResponseFormat
     |                          ^^^^^^^^^^^^^^
8510 |             },
8511 |             "length_descriptions": {
     |

F405 `FormatConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8512:26
     |
8510 |             },
8511 |             "length_descriptions": {
8512 |                 l.value: FormatConfig(text_length=l).get_length_instructions()
     |                          ^^^^^^^^^^^^
8513 |                 for l in TextLength
8514 |             }
     |

F405 `TextLength` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8513:26
     |
8511 |             "length_descriptions": {
8512 |                 l.value: FormatConfig(text_length=l).get_length_instructions()
8513 |                 for l in TextLength
     |                          ^^^^^^^^^^
8514 |             }
8515 |         }
     |

B007 Loop control variable `var_name` not used within loop body
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8586:17
     |
8584 |         for scope_name, scope_vars in variables.items():
8585 |             docs.append(f"\n### {scope_name}:")
8586 |             for var_name, var_info in scope_vars.items():
     |                 ^^^^^^^^
8587 |                 docs.append(f"- `{var_info['path']}`: {var_info['preview']} ({var_info['type']})")
     |
help: Rename unused `var_name` to `_var_name`

F405 `PersonaConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8817:28
     |
8815 |             personality_traits = ["helpful", "concise"]
8816 |
8817 |         self.amd.persona = PersonaConfig(
     |                            ^^^^^^^^^^^^^
8818 |             name=name,
8819 |             style=style,
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:8966:37
     |
8964 |             if task.status == "completed":
8965 |                 summary["completed_tasks"].append(task_info)
8966 |                 if isinstance(task, ToolTask):
     |                                     ^^^^^^^^
8967 |                     summary["tools_used"].append(task.tool_name)
8968 |             elif task.status == "failed":
     |

F405 `AgentCheckpoint` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9061:43
     |
9059 |     # ===== CHECKPOINT MANAGEMENT =====
9060 |
9061 |     async def _create_checkpoint(self) -> AgentCheckpoint:
     |                                           ^^^^^^^^^^^^^^^
9062 |         """Vereinfachte Checkpoint-Erstellung - fokussiert auf wesentliche Daten"""
9063 |         try:
     |

F405 `AgentCheckpoint` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9121:26
     |
9120 |             # Erstelle konsolidierten Checkpoint
9121 |             checkpoint = AgentCheckpoint(
     |                          ^^^^^^^^^^^^^^^
9122 |                 timestamp=datetime.now(),
9123 |                 agent_state={
     |

F405 `AgentCheckpoint` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9158:50
     |
9156 |             raise
9157 |
9158 |     async def _save_checkpoint(self, checkpoint: AgentCheckpoint, filepath: str = None):
     |                                                  ^^^^^^^^^^^^^^^
9159 |         """Vereinfachtes Checkpoint-Speichern - alles in eine Datei"""
9160 |         try:
     |

F405 `AgentCheckpoint` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9245:29
     |
9244 |             with open(latest_checkpoint_path, 'rb') as f:
9245 |                 checkpoint: AgentCheckpoint = pickle.load(f)
     |                             ^^^^^^^^^^^^^^^
9246 |
9247 |             # Stelle Agent-Status wieder her
     |

F405 `AgentCheckpoint` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9268:69
     |
9266 |             return {"success": False, "error": str(e)}
9267 |
9268 |     async def _restore_from_checkpoint_simplified(self, checkpoint: AgentCheckpoint, auto_restore_history: bool) -> \
     |                                                                     ^^^^^^^^^^^^^^^
9269 |     dict[
9270 |         str, Any]:
     |

F405 `PersonaConfig` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9303:52
     |
9301 |                             persona_data = amd_data["persona"]
9302 |                             if isinstance(persona_data, dict):
9303 |                                 self.amd.persona = PersonaConfig(**persona_data)
     |                                                    ^^^^^^^^^^^^^
9304 |                         except Exception as e:
9305 |                             restore_stats["errors"].append(f"Persona restore failed: {e}")
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9345:55
     |
9343 |                         task_type = task_data.get("type", "generic")
9344 |                         if task_type == "LLMTask":
9345 |                             restored_tasks[task_id] = LLMTask(**task_data)
     |                                                       ^^^^^^^
9346 |                         elif task_type == "ToolTask":
9347 |                             restored_tasks[task_id] = ToolTask(**task_data)
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9347:55
     |
9345 |                             restored_tasks[task_id] = LLMTask(**task_data)
9346 |                         elif task_type == "ToolTask":
9347 |                             restored_tasks[task_id] = ToolTask(**task_data)
     |                                                       ^^^^^^^^
9348 |                         elif task_type == "DecisionTask":
9349 |                             restored_tasks[task_id] = DecisionTask(**task_data)
     |

F405 `DecisionTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9349:55
     |
9347 |                             restored_tasks[task_id] = ToolTask(**task_data)
9348 |                         elif task_type == "DecisionTask":
9349 |                             restored_tasks[task_id] = DecisionTask(**task_data)
     |                                                       ^^^^^^^^^^^^
9350 |                         else:
9351 |                             restored_tasks[task_id] = Task(**task_data)
     |

F405 `Task` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9351:55
     |
9349 |                             restored_tasks[task_id] = DecisionTask(**task_data)
9350 |                         else:
9351 |                             restored_tasks[task_id] = Task(**task_data)
     |                                                       ^^^^
9352 |
9353 |                         restore_stats["tasks_restored"] += 1
     |

F405 `AgentCheckpoint` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9388:79
     |
9386 |             return restore_stats
9387 |
9388 |     async def _restore_sessions_and_conversation_simplified(self, checkpoint: AgentCheckpoint, restore_stats: dict):
     |                                                                               ^^^^^^^^^^^^^^^
9389 |         """Vereinfachte Session- und Conversation-Wiederherstellung"""
9390 |         try:
     |

F405 `ToolAnalysis` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9898:17
     |
9896 |             try:
9897 |                 # Validate cached data against the Pydantic model
9898 |                 ToolAnalysis.model_validate(existing_analysis[tool_name])
     |                 ^^^^^^^^^^^^
9899 |                 self._tool_capabilities[tool_name] = existing_analysis[tool_name]
9900 |                 rprint(f"Loaded and validated cached analysis for {tool_name}")
     |

F405 `ToolAnalysis` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9941:13
     |
9939 | Rule! no additional comments or text in the format !
9940 | schema:
9941 |  {yaml.dump(ToolAnalysis.model_json_schema())}
     |             ^^^^^^^^^^^^
9942 |
9943 | Respond in YAML format:
     |

F405 `ToolAnalysis` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\base\Agent\agent.py:9996:38
     |
9994 |                 await self._save_tool_analysis()
9995 |
9996 |                 validated_analysis = ToolAnalysis.model_validate(analysis)
     |                                      ^^^^^^^^^^^^
9997 |                 rprint(f"Generated intelligent analysis for {tool_name}")
9998 |                 break
     |

F405 `ProgressEvent` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:10082:56
      |
10081 |             if self.progress_tracker:
10082 |                 await self.progress_tracker.emit_event(ProgressEvent(
      |                                                        ^^^^^^^^^^^^^
10083 |                     event_type="function_call",
10084 |                     timestamp=time.time(),
      |

F405 `NodeStatus` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:10086:28
      |
10084 |                     timestamp=time.time(),
10085 |                     node_name="FlowAgent",
10086 |                     status=NodeStatus.COMPLETED,
      |                            ^^^^^^^^^^
10087 |                     tool_name=function_name,
10088 |                     tool_args=kwargs,
      |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     --> toolboxv2\mods\isaa\base\Agent\agent.py:10200:21
      |
10198 |                     parsed_data = yaml.safe_load(yaml_content)
10199 |                 except yaml.YAMLError as e:
10200 |                     raise ValueError(f"Invalid YAML syntax: {e}")
      |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
10201 |
10202 |                 if not isinstance(parsed_data, dict):
      |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
     --> toolboxv2\mods\isaa\base\Agent\agent.py:10220:21
      |
10219 |                     error_msg = "Validation failed:\n" + "\n".join(detailed_errors)
10220 |                     raise ValueError(error_msg)
      |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
10221 |
10222 |             except Exception as e:
      |

B007 Loop control variable `session_id` not used within loop body
     --> toolboxv2\mods\isaa\base\Agent\agent.py:10389:21
      |
10387 |             else:
10388 |                 # Clear all sessions
10389 |                 for session_id, session in self.context_manager.session_managers.items():
      |                     ^^^^^^^^^^
10390 |                     if hasattr(session, 'history'):
10391 |                         session.history = []
      |
help: Rename unused `session_id` to `_session_id`

B007 Loop control variable `manager_name` not used within loop body
     --> toolboxv2\mods\isaa\base\Agent\agent.py:10458:21
      |
10456 |             session_managers = self.shared.get("session_managers", {})
10457 |             if session_managers:
10458 |                 for manager_name, manager in session_managers.items():
      |                     ^^^^^^^^^^^^
10459 |                     if hasattr(manager, 'clear_all_history'):
10460 |                         await manager.clear_all_history()
      |
help: Rename unused `manager_name` to `_manager_name`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11081:10
      |
11079 |         return {k: process_nested(v, max_depth - 1) for k, v in data.items()}
11080 |
11081 |     elif isinstance(data, (list, tuple)):
      |          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
11082 |         processed = [process_nested(item, max_depth - 1) for item in data]
11083 |         return type(data)(processed)
      |
help: Convert to `X | Y`

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11528:25
      |
11527 |     print("=== Testing Basic Chain ===")
11528 |     agent_a = FlowAgent(AgentModelData(name="A"))
      |                         ^^^^^^^^^^^^^^
11529 |     agent_b = FlowAgent(AgentModelData(name="B"))
11530 |     agent_c = FlowAgent(AgentModelData(name="C"))
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11529:25
      |
11527 |     print("=== Testing Basic Chain ===")
11528 |     agent_a = FlowAgent(AgentModelData(name="A"))
11529 |     agent_b = FlowAgent(AgentModelData(name="B"))
      |                         ^^^^^^^^^^^^^^
11530 |     agent_c = FlowAgent(AgentModelData(name="C"))
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11530:25
      |
11528 |     agent_a = FlowAgent(AgentModelData(name="A"))
11529 |     agent_b = FlowAgent(AgentModelData(name="B"))
11530 |     agent_c = FlowAgent(AgentModelData(name="C"))
      |                         ^^^^^^^^^^^^^^
11531 |
11532 |     async def a_run(self, query: str):
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11694:34
      |
11692 |         value: str
11693 |     # (Wir gehen davon aus, dass die Agenten wie im Beispiel-Code definiert sind)
11694 |     supervisor_agent = FlowAgent(AgentModelData(name="Supervisor"))
      |                                  ^^^^^^^^^^^^^^
11695 |     writer_agent = FlowAgent(AgentModelData(name="Writer"))
11696 |     reviewer_agent = FlowAgent(AgentModelData(name="Reviewer"))
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11695:30
      |
11693 |     # (Wir gehen davon aus, dass die Agenten wie im Beispiel-Code definiert sind)
11694 |     supervisor_agent = FlowAgent(AgentModelData(name="Supervisor"))
11695 |     writer_agent = FlowAgent(AgentModelData(name="Writer"))
      |                              ^^^^^^^^^^^^^^
11696 |     reviewer_agent = FlowAgent(AgentModelData(name="Reviewer"))
11697 |     notifier_agent = FlowAgent(AgentModelData(name="Notifier"))
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11696:32
      |
11694 |     supervisor_agent = FlowAgent(AgentModelData(name="Supervisor"))
11695 |     writer_agent = FlowAgent(AgentModelData(name="Writer"))
11696 |     reviewer_agent = FlowAgent(AgentModelData(name="Reviewer"))
      |                                ^^^^^^^^^^^^^^
11697 |     notifier_agent = FlowAgent(AgentModelData(name="Notifier"))
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11697:32
      |
11695 |     writer_agent = FlowAgent(AgentModelData(name="Writer"))
11696 |     reviewer_agent = FlowAgent(AgentModelData(name="Reviewer"))
11697 |     notifier_agent = FlowAgent(AgentModelData(name="Notifier"))
      |                                ^^^^^^^^^^^^^^
11698 |
11699 |     # Weisen Sie den Agenten die beispielhaften a_run und a_format_class Methoden zu
      |

F405 `AgentModelData` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11744:15
      |
11742 |     # Simple test
11743 |     async def _agent():
11744 |         amd = AgentModelData(
      |               ^^^^^^^^^^^^^^
11745 |         name="TestAgent",
11746 |         fast_llm_model="groq/llama-3.3-70b-versatile",
      |

F405 `PersonaConfig` may be undefined, or defined from star imports
     --> toolboxv2\mods\isaa\base\Agent\agent.py:11748:17
      |
11746 |         fast_llm_model="groq/llama-3.3-70b-versatile",
11747 |         complex_llm_model="openrouter/qwen/qwen3-coder",
11748 |         persona=PersonaConfig(
      |                 ^^^^^^^^^^^^^
11749 |             name="Isaa",
11750 |             style="light and perishes",
      |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\Agent\builder.py:501:17
    |
499 |             except Exception as e:
500 |                 eprint(f"MCP tool {server_name}.{tool_name} failed: {e}")
501 |                 raise RuntimeError(f"Error executing {tool_name}: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
502 |
503 |         # Set dynamic signature
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\Agent\builder.py:544:17
    |
542 |             except Exception as e:
543 |                 eprint(f"MCP resource {resource_uri} failed: {e}")
544 |                 raise RuntimeError(f"Error reading resource: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
545 |
546 |         # Set signature and metadata
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\Agent\builder.py:610:17
    |
608 |             except Exception as e:
609 |                 eprint(f"MCP resource template {template_uri} failed: {e}")
610 |                 raise RuntimeError(f"Error accessing resource template: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
611 |
612 |         # Set dynamic signature
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\Agent\builder.py:682:17
    |
680 |             except Exception as e:
681 |                 eprint(f"MCP prompt {prompt_name} failed: {e}")
682 |                 raise RuntimeError(f"Error executing prompt: {str(e)}")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
683 |
684 |         # Set dynamic signature
    |

F821 Undefined name `ProgressTracker`
  --> toolboxv2\mods\isaa\base\Agent\chain.py:79:56
   |
77 |         return ConditionalChain(None, self, other)
78 |
79 |     def set_progress_callback(self, progress_tracker: 'ProgressTracker'):
   |                                                        ^^^^^^^^^^^^^^^
80 |         """Recursively sets the progress callback for all tasks in the chain."""
81 |         tasks_to_process = []
   |

F821 Undefined name `FlowAgent`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:116:44
    |
114 |     """Handles parallel execution of multiple agents or chains."""
115 |
116 |     def __init__(self, agents: list[Union['FlowAgent', ChainBase]]):
    |                                            ^^^^^^^^^
117 |         self.agents = agents
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\base\Agent\chain.py:143:9
    |
141 |           """Executes the true or false branch based on the condition."""
142 |           condition_met = False
143 | /         if isinstance(self.condition, IS) and isinstance(data, dict):
144 | |             if data.get(self.condition.key) == self.condition.expected_value:
    | |_____________________________________________________________________________^
145 |                   condition_met = True
    |
help: Combine `if` statements using `and`

F821 Undefined name `FlowAgent`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:173:32
    |
171 |     """The main class for creating and executing sequential chains of tasks."""
172 |
173 |     def __init__(self, agent: 'FlowAgent' = None):
    |                                ^^^^^^^^^
174 |         self.tasks: list[Any] = [agent] if agent else []
175 |         self.progress_tracker: ProgressTracker | None = None
    |

F821 Undefined name `ProgressTracker`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:175:32
    |
173 |     def __init__(self, agent: 'FlowAgent' = None):
174 |         self.tasks: list[Any] = [agent] if agent else []
175 |         self.progress_tracker: ProgressTracker | None = None
    |                                ^^^^^^^^^^^^^^^
176 |
177 |     @classmethod
    |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:251:18
    |
249 |                     pass
250 |
251 |             elif isinstance(task, (ParallelChain, ConditionalChain, ErrorHandlingChain)):
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
252 |                 current_data = await task.a_run(current_data, **kwargs)
    |
help: Convert to `X | Y`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:346:44
    |
345 |             # Parallel chain detection
346 |             if hasattr(comp, 'agents') and isinstance(comp.agents, (list, tuple)):
    |                                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
347 |                 branches = []
348 |                 for i, agent in enumerate(comp.agents):
    |
help: Convert to `X | Y`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:398:43
    |
397 |             # Regular chain detection
398 |             if hasattr(comp, 'tasks') and isinstance(comp.tasks, (list, tuple)):
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
399 |                 tasks = []
400 |                 for i, task in enumerate(comp.tasks):
    |
help: Convert to `X | Y`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> toolboxv2\mods\isaa\base\Agent\chain.py:683:28
    |
681 |                     val = getattr(self, attr)
682 |                     if val is not None:
683 |                         if isinstance(val, (list, tuple)):
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
684 |                             attrs.append(f"{attr}: {len(val)} items")
685 |                         else:
    |
help: Convert to `X | Y`

F821 Undefined name `AgentConfig`
   --> toolboxv2\mods\isaa\base\Agent\executors.py:340:32
    |
339 | # --- Factory function ---
340 | def get_code_executor(config: 'AgentConfig') -> RestrictedPythonExecutor | DockerCodeExecutor | BaseCodeExecutor | None:
    |                                ^^^^^^^^^^^
341 |     """Creates a code executor instance based on configuration."""
342 |     executor_type = config.code_executor_type
    |

F402 Import `field` from line 5 shadowed by loop variable
   --> toolboxv2\mods\isaa\base\Agent\types.py:124:13
    |
123 |         # Get all fields from the dataclass
124 |         for field in fields(self):
    |             ^^^^^
125 |             value = getattr(self, field.name)
    |

SIM101 Multiple `isinstance` calls for `value`, merge into a single call
   --> toolboxv2\mods\isaa\base\Agent\types.py:133:16
    |
132 |             # Handle NodeStatus enum
133 |             if isinstance(value, NodeStatus) or isinstance(value, Enum):
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
134 |                 result[field.name] = value.value
135 |             # Handle dataclass objects
    |
help: Merge `isinstance` calls for `value`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\base\Agent\types.py:189:9
    |
188 |           # Handle NodeStatus enum conversion from string back to enum
189 | /         if 'status' in data_copy and data_copy['status'] is not None:
190 | |             if isinstance(data_copy['status'], str):
    | |____________________________________________________^
191 |                   try:
192 |                       data_copy['status'] = NodeStatus(data_copy['status'])
    |
help: Combine `if` statements using `and`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\mods\isaa\base\Agent\types.py:666:13
    |
665 |         except ValueError:
666 |             raise ValueError(f"Invalid format '{response_format}' or length '{text_length}'")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
667 |
668 |     def should_post_process(self) -> bool:
    |

UP045 [*] Use `X | None` for type annotations
   --> toolboxv2\mods\isaa\base\Agent\types.py:680:14
    |
678 |     max_tokens: int = 2048
679 |     max_input_tokens: int = 32768
680 |     api_key: Optional[str]  = None
    |              ^^^^^^^^^^^^^
681 |     api_base: Optional[str]  = None
682 |     budget_manager: Any  = None
    |
help: Convert to `X | None`

UP045 [*] Use `X | None` for type annotations
   --> toolboxv2\mods\isaa\base\Agent\types.py:681:15
    |
679 |     max_input_tokens: int = 32768
680 |     api_key: Optional[str]  = None
681 |     api_base: Optional[str]  = None
    |               ^^^^^^^^^^^^^
682 |     budget_manager: Any  = None
683 |     caching: bool = True
    |
help: Convert to `X | None`

UP045 [*] Use `X | None` for type annotations
   --> toolboxv2\mods\isaa\base\Agent\types.py:684:14
    |
682 |     budget_manager: Any  = None
683 |     caching: bool = True
684 |     persona: Optional[PersonaConfig] = True
    |              ^^^^^^^^^^^^^^^^^^^^^^^
685 |     use_fast_response: bool = True
    |
help: Convert to `X | None`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\base\KnowledgeBase.py:850:9
    |
848 |               raise ValueError("Length of texts and metadata must match")
849 |
850 | /         if not direct:
851 | |             if len(texts) == 1 and len(texts[0]) < 10_000:
    | |__________________________________________________________^
852 |                   if len(self.sto) < self.batch_size and len(texts) == 1:
853 |                       self.sto.append((texts[0], metadata[0]))
    |
help: Combine `if` statements using `and`

SIM105 Use `contextlib.suppress(Exception)` instead of `try`-`except`-`pass`
    --> toolboxv2\mods\isaa\base\KnowledgeBase.py:1698:21
     |
1696 |   …         # Aufräumen falls tmp noch existiert (bei Fehlern)
1697 |   …         if tmp.exists():
1698 | / …             try:
1699 | | …                 tmp.unlink()
1700 | | …             except Exception:
1701 | | …                 pass
     | |______________________^
1702 |   …     return None
1703 |   …     # print(f"Knowledge base successfully saved to {path} with {len(self.concept_extractor.concept_graph.concepts.items())} concep…
     |
help: Replace with `contextlib.suppress(Exception)`

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\mods\isaa\base\KnowledgeBase.py:1722:16
     |
1720 |         """
1721 |         try:
1722 |             if isinstance(path, (bytes, bytearray, memoryview)):
     |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1723 |                 data_bytes = bytes(path)
1724 |                 try:
     |
help: Convert to `X | Y`

F811 Redefinition of unused `math` from line 4
    --> toolboxv2\mods\isaa\base\KnowledgeBase.py:1924:11
     |
1922 | text = "test 123".encode("utf-8", errors="replace").decode("utf-8")
1923 |
1924 | async def math():
     |           ^^^^ `math` redefined here
1925 |     kb = KnowledgeBase(n_clusters=3, model_name="openrouter/mistralai/mistral-7b-instruct", requests_per_second=10, batch_size=20, c…
     |
    ::: toolboxv2\mods\isaa\base\KnowledgeBase.py:4:8
     |
   2 | import hashlib
   3 | import json
   4 | import math
     |        ---- previous definition of `math` here
   5 | import os
   6 | import pickle
     |
help: Remove definition: `math`

SIM105 Use `contextlib.suppress(Exception)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\isaa\extras\mcp_session_manager.py:136:17
    |
134 |               # Cleanup on failure
135 |               if server_name in self.connections:
136 | /                 try:
137 | |                     await self.connections[server_name].__aexit__(None, None, None)
138 | |                 except Exception:
139 | |                     pass
    | |________________________^
140 |                   del self.connections[server_name]
141 |               return None
    |
help: Replace with `contextlib.suppress(Exception)`

I001 [*] Import block is un-sorted or un-formatted
  --> toolboxv2\mods\isaa\extras\terminal_progress.py:23:1
   |
21 |     print("Warning: Rich not available. Install with: pip install rich")
22 |
23 | from toolboxv2.mods.isaa.base.Agent.types import ProgressEvent, NodeStatus, TaskPlan, LLMTask, ToolTask
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: Organize imports

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\extras\terminal_progress.py:131:9
    |
130 |           # Check for explicit completion signals from flows or the entire execution
131 | /         if event.event_type in ["node_exit", "execution_complete", "task_complete"] or event.success:
132 | |             # This logic correctly handles the completion of Flows (like TaskManagementFlow)
133 | |             if event.node_duration:
    | |___________________________________^
134 |                   self.duration = event.node_duration
135 |                   self.end_time = event.timestamp
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\extras\terminal_progress.py:333:13
    |
332 |               # Update current node tracking
333 | /             if event.event_type in ["node_enter", "llm_call", "tool_call"]:
334 | |                 if self.current_node != node_name:
    | |__________________________________________________^
335 |                       self.current_node = node_name
336 |                       if node_name not in self.execution_flow:
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:1501:13
     |
1500 |               # Clear terminal for a clean live update (except for the very first print)
1501 | /             if self._print_counter > 1 and self._display_active:
1502 | |                 if self.mode == VerbosityMode.REALTIME:
     | |_______________________________________________________^
1503 |                       self.console.clear()
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:1552:17
     |
1550 |                   self._layout_integration_attempted = True
1551 |                   integration_success = self._integrate_with_application_layout(integration_info)
1552 | /                 if not integration_success:
1553 | |                     if self.mode == VerbosityMode.DEBUG:
     | |________________________________________________________^
1554 |                           print("⚠️ Layout integration failed, using fallback display")
     |
help: Combine `if` statements using `and`

B007 Loop control variable `i` not used within loop body
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:2710:17
     |
2708 |             active_nodes = set(summary["execution_flow"]["active_nodes"])
2709 |
2710 |             for i, node_name in enumerate(execution_flow[-5:], 1):  # Show last 5 nodes
     |                 ^
2711 |                 if node_name not in self.tree_builder.nodes:
2712 |                     continue
     |
help: Rename unused `i` to `_i`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:4557:13
     |
4555 |                   self.console.print(batch_panel)
4556 |
4557 | /             elif self.mode == VerbosityMode.REALTIME:
4558 | |                 if not self.realtime_minimal and total_meta_tools > 1:
     | |______________________________________________________________________^
4559 |                       self.console.print(f"🔧 {total_meta_tools} tools completed", style="purple dim")
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:4572:9
     |
4571 |           # Only show analysis in verbose/debug modes
4572 | /         if self.mode in [VerbosityMode.VERBOSE, VerbosityMode.DEBUG]:
4573 | |             if analysis_result == "no_meta_tools_detected":
     | |___________________________________________________________^
4574 |                   analysis_text = f"🔍 Loop {reasoning_loop}: No meta-tools in LLM response ({llm_response_length} chars)"
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:5491:21
     |
5489 |                       elif meta_tool_name == "delegate_to_llm_tool_node":
5490 |                           print(f"\r✅ [{time_str}] Task completed                    ")
5491 | /                     elif meta_tool_name == "advance_outline_step":
5492 | |                         if metadata.get("step_completed", False):
     | |_________________________________________________________________^
5493 |                               print(f"\r➡️ [{time_str}] Step advanced                    ")
5494 |                   else:
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:5522:9
     |
5520 |                                              style=f"{color} dim")
5521 |
5522 | /         elif not event.success:
5523 | |             # Error messages with timestamps - show in all modes except minimal realtime
5524 | |             if not (self.mode == VerbosityMode.REALTIME and self.realtime_minimal):
     | |___________________________________________________________________________________^
5525 |                   error_message = metadata.get("error_message", "Unknown error")
5526 |                   outline_info = f" (step {outline_step})" if outline_step > 0 else ""
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:5635:21
     |
5633 |                           goals_count = metadata.get("goals_count", 0)
5634 |                           print(f"✅ [{time_str}] Plan executed ({goals_count} goals){duration_str}")
5635 | /                     elif meta_tool_name == "advance_outline_step":
5636 | |                         if metadata.get("step_completed", False):
     | |_________________________________________________________________^
5637 |                               print(f"➡️ [{time_str}] Step advanced{duration_str}")
5638 |                   else:
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:5747:13
     |
5745 |                   self._print_detailed_task_update(event, task_dict, status_icon, status_color)
5746 |
5747 | /             elif self.mode == VerbosityMode.REALTIME:
5748 | |                 if not self.realtime_minimal:
     | |_____________________________________________^
5749 |                       self._print_realtime_task_update(event, task_dict, status_icon)
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress.py:6050:21
     |
6049 |                       # Check if currently active (last 30 seconds)
6050 | /                     if tool_event.timestamp > time.time() - 30:
6051 | |                         if tool_event.tool_success is None:  # Still running
     | |___________________________________________________________^
6052 |                               tool_usage['tools_active'].add(tool_name)
6053 |                               tool_usage['current_tool_operation'] = f"Using {tool_name}"
     |
help: Combine `if` statements using `and`

F403 `from toolboxv2.mods.isaa.base.Agent.types import *` used; unable to detect undefined names
  --> toolboxv2\mods\isaa\extras\terminal_progress2.py:23:1
   |
21 |     print("Warning: Rich not available. Install with: pip install rich")
22 |
23 | from toolboxv2.mods.isaa.base.Agent.types import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\extras\terminal_progress2.py:208:9
    |
207 |           # Check for explicit completion signals from flows or the entire execution
208 | /         if event.event_type in ["node_exit", "execution_complete", "task_complete"] or event.success:
209 | |             # This logic correctly handles the completion of Flows (like TaskManagementFlow)
210 | |             if event.node_duration:
    | |___________________________________^
211 |                   self.duration = event.node_duration
212 |                   self.end_time = event.timestamp
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\mods\isaa\extras\terminal_progress2.py:410:13
    |
409 |               # Update current node tracking
410 | /             if event.event_type in ["node_enter", "llm_call", "tool_call"]:
411 | |                 if self.current_node != node_name:
    | |__________________________________________________^
412 |                       self.current_node = node_name
413 |                       if node_name not in self.execution_flow:
    |
help: Combine `if` statements using `and`

B007 Loop control variable `i` not used within loop body
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:1942:13
     |
1940 |         # Show recent routing decisions
1941 |         recent_routes = self.tree_builder.routing_history[-5:]  # Last 5
1942 |         for i, route in enumerate(recent_routes):
     |             ^
1943 |             timestamp = datetime.fromtimestamp(route["timestamp"]).strftime("%H:%M:%S")
1944 |             route_text = f"[{timestamp}] {route['from']} → {route['to']}"
     |
help: Rename unused `i` to `_i`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:2282:13
     |
2280 |                   self.console.print(batch_panel)
2281 |
2282 | /             elif self.mode == VerbosityMode.REALTIME:
2283 | |                 if not self.realtime_minimal and total_meta_tools > 1:
     | |______________________________________________________________________^
2284 |                       self.console.print(f"🔧 {total_meta_tools} tools completed", style="purple dim")
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:2297:9
     |
2296 |           # Only show analysis in verbose/debug modes
2297 | /         if self.mode in [VerbosityMode.VERBOSE, VerbosityMode.DEBUG]:
2298 | |             if analysis_result == "no_meta_tools_detected":
     | |___________________________________________________________^
2299 |                   analysis_text = f"🔍 Loop {reasoning_loop}: No meta-tools in LLM response ({llm_response_length} chars)"
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:3252:21
     |
3250 |                       elif meta_tool_name == "delegate_to_llm_tool_node":
3251 |                           print(f"\r✅ [{time_str}] Task completed                    ")
3252 | /                     elif meta_tool_name == "advance_outline_step":
3253 | |                         if metadata.get("step_completed", False):
     | |_________________________________________________________________^
3254 |                               print(f"\r➡️ [{time_str}] Step advanced                    ")
3255 |                   else:
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:3283:9
     |
3281 |                                              style=f"{color} dim")
3282 |
3283 | /         elif not event.success:
3284 | |             # Error messages with timestamps - show in all modes except minimal realtime
3285 | |             if not (self.mode == VerbosityMode.REALTIME and self.realtime_minimal):
     | |___________________________________________________________________________________^
3286 |                   error_message = metadata.get("error_message", "Unknown error")
3287 |                   outline_info = f" (step {outline_step})" if outline_step > 0 else ""
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:3396:21
     |
3394 |                           goals_count = metadata.get("goals_count", 0)
3395 |                           print(f"✅ [{time_str}] Plan executed ({goals_count} goals){duration_str}")
3396 | /                     elif meta_tool_name == "advance_outline_step":
3397 | |                         if metadata.get("step_completed", False):
     | |_________________________________________________________________^
3398 |                               print(f"➡️ [{time_str}] Step advanced{duration_str}")
3399 |                   else:
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:3508:13
     |
3506 |                   self._print_detailed_task_update(event, task_dict, status_icon, status_color)
3507 |
3508 | /             elif self.mode == VerbosityMode.REALTIME:
3509 | |                 if not self.realtime_minimal:
     | |_____________________________________________^
3510 |                       self._print_realtime_task_update(event, task_dict, status_icon)
     |
help: Combine `if` statements using `and`

F405 `TaskPlan` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:4321:75
     |
4319 |         status=NodeStatus.COMPLETED,
4320 |         success=True,
4321 |         metadata={"plan_name": "Demo Plan", "task_count": 3, "full_plan": TaskPlan(id='bf5053ad-1eae-4dd2-9c08-0c7fab49f80d', name='…
     |                                                                           ^^^^^^^^
4322 |     ))
     |

F405 `LLMTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:4321:241
     |
4319 | …
4320 | …
4321 | …_on_bike.py and execution_summary.json if they exist', tasks=[LLMTask(id='analyze_files', type='LLMTask', description='Analyze the …
     |                                                                ^^^^^^^
4322 | …
     |

F405 `ToolTask` may be undefined, or defined from star imports
    --> toolboxv2\mods\isaa\extras\terminal_progress2.py:4321:567
     |
4319 | …
4320 | …
4321 | …8, 726320), started_at=None, completed_at=None, metadata={}),ToolTask(id='remove_files', type='ToolTask', description='Delete turtl…
     |                                                               ^^^^^^^^
4322 | …
     |

B007 Loop control variable `i` not used within loop body
   --> toolboxv2\mods\isaa\extras\verbose_output.py:258:13
    |
256 |         """Helper method to print a table row"""
257 |         formatted_cells = []
258 |         for i, (cell, width) in enumerate(zip(row, widths, strict=False)):
    |             ^
259 |             cell_str = str(cell)
260 |             if len(cell_str) > width:
    |
help: Rename unused `i` to `_i`

SIM108 Use ternary operator `spinner_symbols = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏" if self._terminal_width < 60 else "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"` instead of `if`-`else`-block
   --> toolboxv2\mods\isaa\extras\verbose_output.py:279:9
    |
277 |           self._terminal_width = self._get_terminal_width()
278 |
279 | /         if self._terminal_width < 60:
280 | |             # Simple spinner for small screens
281 | |             spinner_symbols = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
282 | |         else:
283 | |             # Detailed spinner for larger screens
284 | |             spinner_symbols = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    | |__________________________________________^
285 |
286 |           # Truncate message if too long
    |
help: Replace `if`-`else`-block with `spinner_symbols = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏" if self._terminal_width < 60 else "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"`

SIM108 Use ternary operator `timing = f" ({elapsed / 60:.1f}m)" if elapsed > 60 else f" ({elapsed:.1f}s)"` instead of `if`-`else`-block
   --> toolboxv2\mods\isaa\extras\verbose_output.py:424:9
    |
423 |           elapsed = time.time() - self._start_time
424 | /         if elapsed > 60:
425 | |             timing = f" ({elapsed / 60:.1f}m)"
426 | |         else:
427 | |             timing = f" ({elapsed:.1f}s)"
    | |_________________________________________^
428 |
429 |           self.formatter.print_header(f"{text}{timing}")
    |
help: Replace `if`-`else`-block with `timing = f" ({elapsed / 60:.1f}m)" if elapsed > 60 else f" ({elapsed:.1f}s)"`

S113 Probable use of `requests` call without timeout
  --> toolboxv2\mods\isaa\extras\web_search.py:29:20
   |
27 |         }
28 |
29 |         response = requests.get(url, params=params)
   |                    ^^^^^^^^^^^^
30 |         response.raise_for_status()
31 |         data = response.json()
   |

S113 Probable use of `requests` call without timeout
  --> toolboxv2\mods\isaa\extras\web_search.py:82:20
   |
80 |         }
81 |
82 |         response = requests.get(url, headers=headers, params=params)
   |                    ^^^^^^^^^^^^
83 |         response.raise_for_status()
84 |         data = response.json()
   |

F601 Dictionary key literal `'â€"'` repeated
   --> toolboxv2\mods\isaa\extras\web_search.py:423:21
    |
421 |         '�': '',
422 |         'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€¦': '...',
423 |         'â€"': '-', 'â€"': '--', 'Â': ' ',
    |                     ^^^^^
424 |         'Ã¡': 'á', 'Ã©': 'é', 'Ã­': 'í', 'Ã³': 'ó', 'Ãº': 'ú',
425 |         'â€¢': '•', 'Â·': '·', 'Â«': '«', 'Â»': '»'
    |
help: Remove repeated key literal `'â€"'`

S113 Probable use of `requests` call without timeout
  --> toolboxv2\mods\isaa\module.py:92:16
   |
91 | def get_ip():
92 |     response = requests.get('https://api64.ipify.org?format=json').json()
   |                ^^^^^^^^^^^^
93 |     return response["ip"]
   |

S113 Probable use of `requests` call without timeout
   --> toolboxv2\mods\isaa\module.py:98:16
    |
 96 | def get_location():
 97 |     ip_address = get_ip()
 98 |     response = requests.get(f'https://ipapi.co/{ip_address}/json/').json()
    |                ^^^^^^^^^^^^
 99 |     location_data = f"city: {response.get('city')},region: {response.get('region')},country: {response.get('country_name')},"
100 |     return location_data
    |

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\module.py:1077:13
     |
1075 |           for _agent_name in self.config["agents-name-list"]:
1076 |               _instance_key = f'agent-instance-{_agent_name}'
1077 | /             if _instance_key not in self.config:
1078 | |                 if agent_name != "self" and _agent_name == "self":
     | |__________________________________________________________________^
1079 |                       await self.get_agent("self")
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\module.py:1486:9
     |
1485 |           # Stop by agent_id
1486 | /         if agent_id:
1487 | |             if hasattr(self, '_hosted_agents') and agent_id in self._hosted_agents:
     | |___________________________________________________________________________________^
1488 |                   agent_info = self._hosted_agents[agent_id]
1489 |                   agent_port = agent_info.get('port')
     |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
    --> toolboxv2\mods\isaa\module.py:1506:9
     |
1505 |           # Stop by port
1506 | /         if port:
1507 | |             if hasattr(self, '_standalone_servers') and port in self._standalone_servers:
     | |_________________________________________________________________________________________^
1508 |                   server_info = self._standalone_servers[port]
1509 |                   try:
     |
help: Combine `if` statements using `and`

F821 Undefined name `e_outer`
   --> toolboxv2\mods\isaa\ui.py:129:62
    |
127 |         # For setup errors, we also need to yield through an async generator for Result.sse
128 |         async def error_event_generator():
129 |             yield {'event': 'error', 'data': {'message': str(e_outer)}}
    |                                                              ^^^^^^^
130 |
131 |         return Result.sse(stream_generator=error_event_generator())
    |

SIM105 Use `contextlib.suppress(BaseException)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\registry\client.py:376:13
    |
374 |           self.is_connected = False
375 |           if self.ws:
376 | /             try:
377 | |                 await self.ws.close()
378 | |             except:
379 | |                 pass
    | |____________________^
380 |               self.ws = None
    |
help: Replace with `contextlib.suppress(BaseException)`

SIM105 Use `contextlib.suppress(asyncio.CancelledError)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\registry\client.py:388:13
    |
386 |           if self.connection_task:
387 |               self.connection_task.cancel()
388 | /             try:
389 | |                 await self.connection_task
390 | |             except asyncio.CancelledError:
391 | |                 pass
    | |____________________^
392 |               self.connection_task = None
    |
help: Replace with `contextlib.suppress(asyncio.CancelledError)`

SIM105 Use `contextlib.suppress(BaseException)` instead of `try`-`except`-`pass`
   --> toolboxv2\mods\registry\client.py:395:13
    |
394 |           if self.ws:
395 | /             try:
396 | |                 await self.ws.close()
397 | |             except:
398 | |                 pass
    | |____________________^
399 |               self.ws = None
    |
help: Replace with `contextlib.suppress(BaseException)`

SIM102 Use a single `if` statement instead of nested `if` statements
  --> toolboxv2\setup_helper.py:66:5
   |
64 |       print("🔧 Installiere Dev-Tools...")
65 |       d = ["cargo", "node"]
66 | /     if a := input("With docker (N/y)"):
67 | |         if a.lower() == 'y':
   | |____________________________^
68 |               d.append("docker")
69 |       for _d in d.copy():
   |
help: Combine `if` statements using `and`

S602 `subprocess` call with `shell=True` identified, security issue
   --> toolboxv2\setup_helper.py:152:9
    |
150 |         cwd = _cwd
151 |     try:
152 |         subprocess.run(command, cwd=cwd, shell=True, check=True,
    |         ^^^^^^^^^^^^^^
153 |                        stdout=subprocess.PIPE if silent else None)
154 |         return True
    |

B017 Do not assert blind exception: `Exception`
  --> toolboxv2\tests\test_utils\test_daemon\test.py:18:14
   |
16 |         self.assertFalse(daemon_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
19 |             await DaemonUtil(class_instance=None, host='0.0.0.0', port=6582, t=False,
20 |                              app=None, peer=False, name='daemonApp-server',
   |

B017 Do not assert blind exception: `Exception`
  --> toolboxv2\tests\test_utils\test_proxy\test.py:18:14
   |
16 |         self.assertFalse(proxy_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
19 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
20 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

B017 Do not assert blind exception: `Exception`
  --> toolboxv2\tests\test_utils\test_proxy\test.py:23:14
   |
21 |                                          unix_socket=False)
22 |
23 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
24 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
25 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
  --> toolboxv2\tests\test_utils\test_system\test_conda_runner.py:63:22
   |
61 |         # Check if the environment exists
62 |         try:
63 |             output = subprocess.check_output("conda env list", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^
64 |             self.assertIn(self.test_env_name, output)
65 |         except subprocess.CalledProcessError:
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\tests\test_utils\test_system\test_conda_runner.py:81:13
   |
79 |         # Check that the environment no longer exists
80 |         with self.assertRaises(subprocess.CalledProcessError):
81 |             subprocess.check_output(f"conda env list | grep {self.test_env_name}", shell=True, text=True)
   |             ^^^^^^^^^^^^^^^^^^^^^^^
82 |
83 |     def test_add_dependency(self):
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\tests\test_utils\test_system\test_conda_runner.py:96:22
   |
94 |         # Verify the dependency was added by checking conda list
95 |         try:
96 |             output = subprocess.check_output(f"conda list -n {self.test_env_name} numpy", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^
97 |             self.assertIn("numpy", output)
98 |         except subprocess.CalledProcessError:
   |

B017 Do not assert blind exception: `Exception`
   --> toolboxv2\tests\test_utils\test_system\test_conda_runner.py:148:14
    |
146 |         if not get_app(name="test").local_test:
147 |             return
148 |         with self.assertRaises(Exception):
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
149 |             res = add_dependency(self.test_env_name, "non_existent_package_xyz")
150 |             if res is False:
    |

F403 `from .test_main_page import *` used; unable to detect undefined names
 --> toolboxv2\tests\web_test\__init__.py:1:1
  |
1 | from .test_main_page import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2 |
3 | in_valid_session_tests = []
  |

F405 `contact_page_interactions` may be undefined, or defined from star imports
 --> toolboxv2\tests\web_test\__init__.py:4:24
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                        ^^^^^^^^^^^^^^^^^^^^^^^^^
5 | loot_session_tests = []
  |

F405 `installer_interactions` may be undefined, or defined from star imports
 --> toolboxv2\tests\web_test\__init__.py:4:51
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                                                   ^^^^^^^^^^^^^^^^^^^^^^
5 | loot_session_tests = []
  |

F403 `from ..system.all_functions_enums import *` used; unable to detect undefined names
  --> toolboxv2\utils\daemon\daemon_util.py:10:1
   |
 8 | from ..extras.show_and_hide_console import show_console
 9 | from ..extras.Style import Style
10 | from ..system.all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
11 | from ..system.getting_and_closing_app import get_app
12 | from ..system.tb_logger import get_logger
   |

F405 `SOCKETMANAGER` may be undefined, or defined from star imports
  --> toolboxv2\utils\daemon\daemon_util.py:85:45
   |
83 |         if not app.mod_online("SocketManager"):
84 |             await app.load_mod("SocketManager")
85 |         server_result = await app.a_run_any(SOCKETMANAGER.CREATE_SOCKET,
   |                                             ^^^^^^^^^^^^^
86 |                                             get_results=True,
87 |                                             name=self._name,
   |

B023 Function definition does not bind loop variable `name`
   --> toolboxv2\utils\daemon\daemon_util.py:216:71
    |
214 |                         async def _helper_runner():
215 |                             try:
216 |                                 attr_f = getattr(self.class_instance, name)
    |                                                                       ^^^^
217 |
218 |                                 if asyncio.iscoroutinefunction(attr_f):
    |

B023 Function definition does not bind loop variable `args`
   --> toolboxv2\utils\daemon\daemon_util.py:219:57
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                               ^^^^
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

B023 Function definition does not bind loop variable `kwargs`
   --> toolboxv2\utils\daemon\daemon_util.py:219:65
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                                       ^^^^^^
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

B023 Function definition does not bind loop variable `args`
   --> toolboxv2\utils\daemon\daemon_util.py:221:51
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                   ^^^^
222 |
223 |                                 if res is None:
    |

B023 Function definition does not bind loop variable `kwargs`
   --> toolboxv2\utils\daemon\daemon_util.py:221:59
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                           ^^^^^^
222 |
223 |                                 if res is None:
    |

B023 Function definition does not bind loop variable `identifier`
   --> toolboxv2\utils\daemon\daemon_util.py:237:51
    |
235 |                                 get_logger().info(f"sending response {res} {type(res)}")
236 |
237 |                                 await sender(res, identifier)
    |                                                   ^^^^^^^^^^
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |

B023 Function definition does not bind loop variable `identifier`
   --> toolboxv2\utils\daemon\daemon_util.py:239:64
    |
237 |                                 await sender(res, identifier)
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |                                                                ^^^^^^^^^^
240 |
241 |                         await _helper_runner()
    |

B023 Function definition does not bind loop variable `fuction`
   --> toolboxv2\utils\extras\base_widget.py:108:24
    |
106 |         for fuction in functions:
107 |             def x(r):
108 |                 return fuction(request=r)
    |                        ^^^^^^^
109 |             self.onReload.append(x)
    |

B007 Loop control variable `i` not used within loop body
   --> toolboxv2\utils\extras\blobs.py:225:13
    |
223 |         if len(self.blob_ids) < 5:
224 |             return
225 |         for i in range(len(self.servers)//2+1):
    |             ^
226 |             self.share_blobs(self.blob_ids)
    |
help: Rename unused `i` to `_i`

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> toolboxv2\utils\extras\blobs.py:256:17
    |
254 |                 assert Code.decrypt_symmetric(Code.encrypt_symmetric(b"test", key), key, to_str=False) == b"test"
255 |             except Exception:
256 |                 raise ValueError("Invalid symmetric key provided.")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
257 |
258 |     @staticmethod
    |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
   --> toolboxv2\utils\extras\blobs.py:279:18
    |
277 |             if isinstance(e, requests.exceptions.HTTPError) and e.response.status_code == 404:
278 |                 blob_content = {}  # Blob doesn't exist yet, treat as empty
279 |             elif isinstance(e, (EOFError, pickle.UnpicklingError)):
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
280 |                 blob_content = {}  # Blob is empty or corrupt, treat as empty for writing
281 |             else:
    |
help: Convert to `X | Y`

B023 Function definition does not bind loop variable `tb_func`
   --> toolboxv2\utils\extras\bottleup.py:125:112
    |
123 |                         if request_as_kwarg:
124 |                             def tb_func_(**kw):
125 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read()+tb_func(**kw)
    |                                                                                                                ^^^^^^^
126 |                         else:
127 |                             def tb_func_():
    |

B023 Function definition does not bind loop variable `tb_func`
   --> toolboxv2\utils\extras\bottleup.py:128:114
    |
126 |                         else:
127 |                             def tb_func_():
128 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read() + tb_func()
    |                                                                                                                  ^^^^^^^
129 |                         self.route(f'/{mod_name}', method='GET')(tb_func_)
130 |                         print("adding root:", f'/{mod_name}')
    |

S113 Probable use of `requests` call without timeout
  --> toolboxv2\utils\extras\gist_control.py:35:20
   |
33 |         api_url = f"https://api.github.com/gists/{gist_id}"
34 |
35 |         response = requests.get(api_url)
   |                    ^^^^^^^^^^^^
36 |
37 |         if response.status_code == 200:
   |

S113 Probable use of `requests` call without timeout
  --> toolboxv2\utils\extras\gist_control.py:77:20
   |
75 |         # Update an existing Gist
76 |         url = f"https://api.github.com/gists/{gist_id}"
77 |         response = requests.patch(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^^
78 |     else:
79 |         # Create a new Gist
   |

S113 Probable use of `requests` call without timeout
  --> toolboxv2\utils\extras\gist_control.py:81:20
   |
79 |         # Create a new Gist
80 |         url = "https://api.github.com/gists"
81 |         response = requests.post(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^
82 |
83 |     # Check if the request was successful
   |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\utils\proxy\prox_util.py:149:21
    |
147 |                               return await app_attr(*args, **kwargs)
148 |                           return app_attr(*args, **kwargs)
149 | /                     if (name == 'run_any' or name == 'a_run_any') and kwargs.get('get_results', False):
150 | |                         if isinstance(args[0], Enum):
    | |_____________________________________________________^
151 |                               args = (args[0].__class__.NAME.value, args[0].value), args[1:]
152 |                       self.app.sprint(f"Calling method {name}, {args=}, {kwargs}=")
    |
help: Combine `if` statements using `and`

F403 `from .all_functions_enums import *` used; unable to detect undefined names
 --> toolboxv2\utils\system\__init__.py:1:1
  |
1 | from .all_functions_enums import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2 | from .cache import FileCache, MemoryCache
3 | from .file_handler import FileHandler
  |

S113 Probable use of `requests` call without timeout
   --> toolboxv2\utils\system\api.py:134:20
    |
132 |     print(f"Attempting to download executable from {url}...")
133 |     try:
134 |         response = requests.get(url, stream=True)
    |                    ^^^^^^^^^^^^
135 |     except Exception as e:
136 |         print(f"Download error: {e}")
    |

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\utils\system\conda_runner.py:15:19
   |
13 |     if live:
14 |         # Using subprocess.Popen to stream stdout and stderr live
15 |         process = subprocess.Popen(command, shell=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
   |                   ^^^^^^^^^^^^^^^^
16 |         process.communicate()  # Wait for the process to complete
17 |         return process.returncode == 0, None
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> toolboxv2\utils\system\conda_runner.py:21:18
   |
19 |     try:
20 |         # If not live, capture output and return it
21 |         result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True, encoding='cp850')
   |                  ^^^^^^^^^^^^^^
22 |         return True, result.stdout
23 |     except subprocess.CalledProcessError as e:
   |

B030 `except` handlers should only be exception classes or tuples of exception classes
  --> toolboxv2\utils\system\file_handler.py:60:16
   |
58 |                 self.file_handler_max_loaded_index_ = -1
59 |             rdu()
60 |         except OSError and PermissionError as e:
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
61 |             raise e
   |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\utils\system\file_handler.py:111:9
    |
109 |               )
110 |               return False
111 | /         if key not in self.file_handler_load:
112 | |             if key in self.file_handler_key_mapper:
    | |___________________________________________________^
113 |                   key = self.file_handler_key_mapper[key]
    |
help: Combine `if` statements using `and`

B030 `except` handlers should only be exception classes or tuples of exception classes
   --> toolboxv2\utils\system\file_handler.py:148:16
    |
146 |                 self.file_handler_load[key] = self.decode_code(line)
147 |
148 |         except json.decoder.JSONDecodeError and Exception:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
149 |
150 |             for line in self.file_handler_storage:
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> toolboxv2\utils\system\file_handler.py:166:9
    |
164 |       def get_file_handler(self, obj: str, default=None) -> str or None:
165 |           logger = get_logger()
166 | /         if obj not in self.file_handler_load:
167 | |             if obj in self.file_handler_key_mapper:
    | |___________________________________________________^
168 |                   obj = self.file_handler_key_mapper[obj]
169 |           logger.info(Style.ITALIC(Style.GREY(f"Collecting data from storage key : {obj}")))
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
  --> toolboxv2\utils\system\getting_and_closing_app.py:15:5
   |
13 |   def override_main_app(app):
14 |       global registered_apps
15 | /     if registered_apps[0] is not None:
16 | |         if time.time() - registered_apps[0].called_exit[1] > 30:
   | |________________________________________________________________^
17 |               raise PermissionError("Permission denied because of overtime fuction override_main_app sud only be called "
18 |                                     f"once and ontime overtime {time.time() - registered_apps[0].called_exit[1]}")
   |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
  --> toolboxv2\utils\system\ipy_completer.py:64:9
   |
62 |       for _name, obj in inspect.getmembers(module, inspect.isclass):
63 |           # Check if the class is defined in the current module
64 | /         if obj.__module__ == module.__name__:
65 | |             # Check if the class is a dataclass
66 | |             if is_dataclass(obj):
   | |_________________________________^
67 |                   dataclasses.append(obj)
   |
help: Combine `if` statements using `and`

S113 Probable use of `requests` call without timeout
   --> toolboxv2\utils\system\session.py:274:24
    |
272 |             except Exception as e:
273 |                 print("Error session fetch:", e, self.username)
274 |                 return requests.request(method, url, data=data)
    |                        ^^^^^^^^^^^^^^^^
275 |         else:
276 |             print(f"Could not find session using request on {url}")
    |

S113 Probable use of `requests` call without timeout
   --> toolboxv2\utils\system\session.py:278:24
    |
276 |             print(f"Could not find session using request on {url}")
277 |             if method.upper() == 'POST':
278 |                 return requests.request(method, url, json=data)
    |                        ^^^^^^^^^^^^^^^^
279 |             return requests.request(method, url, data=data)
280 |             # raise Exception("Session not initialized. Please login first.")
    |

S113 Probable use of `requests` call without timeout
   --> toolboxv2\utils\system\session.py:279:20
    |
277 |             if method.upper() == 'POST':
278 |                 return requests.request(method, url, json=data)
279 |             return requests.request(method, url, data=data)
    |                    ^^^^^^^^^^^^^^^^
280 |             # raise Exception("Session not initialized. Please login first.")
    |

S113 Probable use of `requests` call without timeout
   --> toolboxv2\utils\system\session.py:360:20
    |
358 | def get_public_ip():
359 |     try:
360 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^
361 |         ip_address = response.json()['ip']
362 |         return ip_address
    |

F403 `from .all_functions_enums import *` used; unable to detect undefined names
  --> toolboxv2\utils\system\types.py:26:1
   |
24 | from ..extras.Style import Spinner
25 | from ..system.db_cli_manager import ClusterManager
26 | from .all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
27 | from .file_handler import FileHandler
   |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:545:25
    |
545 | class ToolBoxError(str, Enum):
    |                         ^^^^
546 |     none = "none"
547 |     input_error = "InputError"
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:552:30
    |
552 | class ToolBoxInterfaces(str, Enum):
    |                              ^^^^
553 |     cli = "CLI"
554 |     api = "API"
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:595:62
    |
593 |     def as_result(self):
594 |         return Result(
595 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^
596 |             result=ToolBoxResult(
597 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:597:86
    |
595 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
596 |             result=ToolBoxResult(
597 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^
598 |                 data_info=self.result.data_info,
599 |                 data=self.result.data,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:637:64
    |
635 |     def as_dict(self):
636 |         return {
637 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                                ^^^^
638 |         "result" : {
639 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:639:84
    |
637 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
638 |         "result" : {
639 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                    ^^^^
640 |             "data_info":self.result.data_info,
641 |             "data":self.result.data,
    |

SIM103 Return the condition `self.info.exec_code != 200` directly
   --> toolboxv2\utils\system\types.py:670:9
    |
668 |           if self.info.exec_code == 0:
669 |               return False
670 | /         if self.info.exec_code == 200:
671 | |             return False
672 | |         return True
    | |___________________^
673 |
674 |       def is_ok(self):
    |
help: Replace with `return self.info.exec_code != 200`

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:683:62
    |
681 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
682 |         return ApiResult(
683 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^
684 |             result=ToolBoxResultBM(
685 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:685:86
    |
683 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
684 |             result=ToolBoxResultBM(
685 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^
686 |                 data_info=self.result.data_info,
687 |                 data=self.result.data,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:705:46
    |
703 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
704 |         return ApiResult(
705 |             error=error if isinstance(error, Enum) else error,
    |                                              ^^^^
706 |             result=ToolBoxResultBM(
707 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |

F405 `Enum` may be undefined, or defined from star imports
   --> toolboxv2\utils\system\types.py:707:84
    |
705 |             error=error if isinstance(error, Enum) else error,
706 |             result=ToolBoxResultBM(
707 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |                                                                                    ^^^^
708 |                 data_info=result.get('data_info', '404'),
709 |                 data=result.get('data'),
    |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1051:45
     |
1049 |     async def aget(self, key=None, default=None):
1050 |         if asyncio.isfuture(self.result.data) or asyncio.iscoroutine(self.result.data) or (
1051 |             isinstance(self.result.data_to, Enum) and self.result.data_to.name == ToolBoxInterfaces.future.name):
     |                                             ^^^^
1052 |             data = await self.result.data
1053 |         else:
     |

F405 `CLOUDM_AUTHMANAGER` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1201:35
     |
1200 |     async def get_user(self, username: str) -> Result:
1201 |         return self.app.a_run_any(CLOUDM_AUTHMANAGER.GET_USER_BY_NAME, username=username, get_results=True)
     |                                   ^^^^^^^^^^^^^^^^^^
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1372:29
     |
1371 |     def _get_function(self,
1372 |                       name: Enum or None,
     |                             ^^^^
1373 |                       state: bool = True,
1374 |                       specification: str = "app",
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1431:34
     |
1429 |         """proxi attr"""
1430 |
1431 |     def get_function(self, name: Enum or tuple, **kwargs):
     |                                  ^^^^
1432 |         """
1433 |         Kwargs for _get_function
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1460:47
     |
1458 |                 run a async fuction
1459 |                 """
1460 |     def run_function(self, mod_function_name: Enum or tuple,
     |                                               ^^^^
1461 |                      tb_run_function_with_state=True,
1462 |                      tb_run_with_specification='app',
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1470:55
     |
1468 |         """proxi attr"""
1469 |
1470 |     async def a_run_function(self, mod_function_name: Enum or tuple,
     |                                                       ^^^^
1471 |                              tb_run_function_with_state=True,
1472 |                              tb_run_with_specification='app',
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1500:49
     |
1498 |         """
1499 |
1500 |     async def run_http(self, mod_function_name: Enum or str or tuple, function_name=None, method="GET",
     |                                                 ^^^^
1501 |                        args_=None,
1502 |                        kwargs_=None,
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1506:42
     |
1504 |         """run a function remote via http / https"""
1505 |
1506 |     def run_any(self, mod_function_name: Enum or str or tuple, backwords_compability_variabel_string_holder=None,
     |                                          ^^^^
1507 |                 get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
1508 |                 kwargs_=None,
     |

F405 `Enum` may be undefined, or defined from star imports
    --> toolboxv2\utils\system\types.py:1512:50
     |
1510 |         """proxi attr"""
1511 |
1512 |     async def a_run_any(self, mod_function_name: Enum or str or tuple,
     |                                                  ^^^^
1513 |                         backwords_compability_variabel_string_holder=None,
1514 |                         get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
     |

UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    --> toolboxv2\utils\system\types.py:2086:28
     |
2084 |                         payload_content = json_data['data']
2085 |                         # If payload_content is complex, re-serialize it to JSON string
2086 |                         if isinstance(payload_content, (dict, list)):
     |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2087 |                             sse_data_field = json.dumps(payload_content)
2088 |                         else:  # Simple type (string, number, bool)
     |
help: Convert to `X | Y`

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:944:60
    |
942 | …                     async def _():
943 | …                         try:
944 | …                             if asyncio.iscoroutine(result):
    |                                                      ^^^^^^
945 | …                                 await result
946 | …                             if hasattr(result, 'Name'):
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:945:47
    |
943 | …                     try:
944 | …                         if asyncio.iscoroutine(result):
945 | …                             await result
    |                                     ^^^^^^
946 | …                         if hasattr(result, 'Name'):
947 | …                             print('Opened :', result.Name)
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:946:48
    |
944 | …                     if asyncio.iscoroutine(result):
945 | …                         await result
946 | …                     if hasattr(result, 'Name'):
    |                                  ^^^^^^
947 | …                         print('Opened :', result.Name)
948 | …                     elif hasattr(result, 'name'):
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:947:59
    |
945 | …                         await result
946 | …                     if hasattr(result, 'Name'):
947 | …                         print('Opened :', result.Name)
    |                                             ^^^^^^
948 | …                     elif hasattr(result, 'name'):
949 | …                         print('Opened :', result.name)
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:948:50
    |
946 | …                         if hasattr(result, 'Name'):
947 | …                             print('Opened :', result.Name)
948 | …                         elif hasattr(result, 'name'):
    |                                        ^^^^^^
949 | …                             print('Opened :', result.name)
950 | …                     except Exception as e:
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:949:59
    |
947 | …                             print('Opened :', result.Name)
948 | …                         elif hasattr(result, 'name'):
949 | …                             print('Opened :', result.name)
    |                                                 ^^^^^^
950 | …                     except Exception as e:
951 | …                         self.debug_rains(e)
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:952:48
    |
950 | …                     except Exception as e:
951 | …                         self.debug_rains(e)
952 | …                         if hasattr(result, 'Name'):
    |                                      ^^^^^^
953 | …                             print('Error opening :', result.Name)
954 | …                         elif hasattr(result, 'name'):
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:953:66
    |
951 | …                     self.debug_rains(e)
952 | …                     if hasattr(result, 'Name'):
953 | …                         print('Error opening :', result.Name)
    |                                                    ^^^^^^
954 | …                     elif hasattr(result, 'name'):
955 | …                         print('Error opening :', result.name)
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:954:50
    |
952 | …                             if hasattr(result, 'Name'):
953 | …                                 print('Error opening :', result.Name)
954 | …                             elif hasattr(result, 'name'):
    |                                            ^^^^^^
955 | …                                 print('Error opening :', result.name)
956 | …                     asyncio.create_task(_())
    |

B023 Function definition does not bind loop variable `result`
   --> toolboxv2\utils\toolbox.py:955:66
    |
953 |                                         print('Error opening :', result.Name)
954 |                                     elif hasattr(result, 'name'):
955 |                                         print('Error opening :', result.name)
    |                                                                  ^^^^^^
956 |                             asyncio.create_task(_())
957 |                         else:
    |

S310 Audit URL open for permitted schemes. Allowing use of `file:` or custom schemes is often unexpected.
  --> uv_api_python_helper.py:11:9
   |
 9 |     if not os.path.exists(dest):
10 |         print(f"Downloading {url}...")
11 |         urllib.request.urlretrieve(url, dest)
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |

S202 Uses of `tarfile.extractall()`
  --> uv_api_python_helper.py:46:13
   |
44 |         download_file(python_url, archive_path)
45 |         with tarfile.open(archive_path, "r:gz") as tar:
46 |             tar.extractall(target_dir) # nosec: S202
   |             ^^^^^^^^^^^^^^
47 |         os.remove(archive_path)
48 |         python_extracted = os.path.join(target_dir, f"Python-{version}")
   |

UP022 Prefer `capture_output` over sending `stdout` and `stderr` to `PIPE`
  --> uv_api_python_helper.py:57:18
   |
55 |   def pip_exists(python_exe):
56 |       try:
57 |           result = subprocess.run(
   |  __________________^
58 | |             [python_exe, "-m", "pip", "--version"],
59 | |             stdout=subprocess.PIPE,
60 | |             stderr=subprocess.PIPE,
61 | |             check=True
62 | |         )
   | |_________^
63 |           print(result.stdout.decode().strip())
64 |           print("pip already installed.")
   |
help: Replace with `capture_output` keyword argument

F403 `from toolboxv2.mods import *` used; unable to detect undefined names
   --> ~oolboxv2\__init__.py:117:5
    |
115 |     MODS_ERROR = None
116 |     import toolboxv2.mods
117 |     from toolboxv2.mods import *
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
118 | except ImportError as e:
119 |     MODS_ERROR = e
    |

F405 `mods` may be undefined, or defined from star imports
   --> ~oolboxv2\__init__.py:149:5
    |
147 |     "get_logger",
148 |     "flows_dict",
149 |     "mods",
    |     ^^^^^^
150 |     "get_app",
151 |     "TBEF",
    |

B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   --> ~oolboxv2\__main__.py:104:5
    |
102 |     def profile_execute_all_functions(*args):
103 |         return print(args)
104 |     raise ValueError("Failed to import function for profiling")
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
105 |
106 | try:
    |

B018 Found useless expression. Either assign it to a variable or remove it.
    --> ~oolboxv2\__main__.py:1025:5
     |
1024 | """)
1025 |     ()
     |     ^^
1026 |     return c
     |

SIM102 Use a single `if` statement instead of nested `if` statements
  --> ~oolboxv2\setup_helper.py:66:5
   |
64 |       print("🔧 Installiere Dev-Tools...")
65 |       d = ["cargo", "node"]
66 | /     if a := input("With docker (N/y)"):
67 | |         if a.lower() == 'y':
   | |____________________________^
68 |               d.append("docker")
69 |       for _d in d.copy():
   |
help: Combine `if` statements using `and`

S602 `subprocess` call with `shell=True` identified, security issue
   --> ~oolboxv2\setup_helper.py:152:9
    |
150 |         cwd = _cwd
151 |     try:
152 |         subprocess.run(command, cwd=cwd, shell=True, check=True,
    |         ^^^^^^^^^^^^^^
153 |                        stdout=subprocess.PIPE if silent else None)
154 |         return True
    |

B017 Do not assert blind exception: `Exception`
  --> ~oolboxv2\tests\test_utils\test_daemon\test.py:18:14
   |
16 |         self.assertFalse(daemon_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
19 |             await DaemonUtil(class_instance=None, host='0.0.0.0', port=6582, t=False,
20 |                              app=None, peer=False, name='daemonApp-server',
   |

B017 Do not assert blind exception: `Exception`
  --> ~oolboxv2\tests\test_utils\test_proxy\test.py:18:14
   |
16 |         self.assertFalse(proxy_util.async_initialized)
17 |
18 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
19 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
20 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

B017 Do not assert blind exception: `Exception`
  --> ~oolboxv2\tests\test_utils\test_proxy\test.py:23:14
   |
21 |                                          unix_socket=False)
22 |
23 |         with self.assertRaises(Exception):
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
24 |             await ProxyUtil(class_instance=None, host='0.0.0.0', port=6581, timeout=15, app=None,
25 |                                          remote_functions=None, peer=False, name='daemonApp-client', do_connect=True,
   |

S602 `subprocess` call with `shell=True` seems safe, but may be changed in the future; consider rewriting without `shell`
  --> ~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:63:22
   |
61 |         # Check if the environment exists
62 |         try:
63 |             output = subprocess.check_output("conda env list", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^
64 |             self.assertIn(self.test_env_name, output)
65 |         except subprocess.CalledProcessError:
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> ~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:81:13
   |
79 |         # Check that the environment no longer exists
80 |         with self.assertRaises(subprocess.CalledProcessError):
81 |             subprocess.check_output(f"conda env list | grep {self.test_env_name}", shell=True, text=True)
   |             ^^^^^^^^^^^^^^^^^^^^^^^
82 |
83 |     def test_add_dependency(self):
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> ~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:96:22
   |
94 |         # Verify the dependency was added by checking conda list
95 |         try:
96 |             output = subprocess.check_output(f"conda list -n {self.test_env_name} numpy", shell=True, text=True)
   |                      ^^^^^^^^^^^^^^^^^^^^^^^
97 |             self.assertIn("numpy", output)
98 |         except subprocess.CalledProcessError:
   |

B017 Do not assert blind exception: `Exception`
   --> ~oolboxv2\tests\test_utils\test_system\test_conda_runner.py:148:14
    |
146 |         if not get_app(name="test").local_test:
147 |             return
148 |         with self.assertRaises(Exception):
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
149 |             res = add_dependency(self.test_env_name, "non_existent_package_xyz")
150 |             if res is False:
    |

F403 `from .test_main_page import *` used; unable to detect undefined names
 --> ~oolboxv2\tests\web_test\__init__.py:1:1
  |
1 | from .test_main_page import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2 |
3 | in_valid_session_tests = []
  |

F405 `contact_page_interactions` may be undefined, or defined from star imports
 --> ~oolboxv2\tests\web_test\__init__.py:4:24
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                        ^^^^^^^^^^^^^^^^^^^^^^^^^
5 | loot_session_tests = []
  |

F405 `installer_interactions` may be undefined, or defined from star imports
 --> ~oolboxv2\tests\web_test\__init__.py:4:51
  |
3 | in_valid_session_tests = []
4 | valid_session_tests = [contact_page_interactions, installer_interactions]
  |                                                   ^^^^^^^^^^^^^^^^^^^^^^
5 | loot_session_tests = []
  |

F403 `from ..system.all_functions_enums import *` used; unable to detect undefined names
  --> ~oolboxv2\utils\daemon\daemon_util.py:10:1
   |
 8 | from ..extras.show_and_hide_console import show_console
 9 | from ..extras.Style import Style
10 | from ..system.all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
11 | from ..system.getting_and_closing_app import get_app
12 | from ..system.tb_logger import get_logger
   |

F405 `SOCKETMANAGER` may be undefined, or defined from star imports
  --> ~oolboxv2\utils\daemon\daemon_util.py:85:45
   |
83 |         if not app.mod_online("SocketManager"):
84 |             await app.load_mod("SocketManager")
85 |         server_result = await app.a_run_any(SOCKETMANAGER.CREATE_SOCKET,
   |                                             ^^^^^^^^^^^^^
86 |                                             get_results=True,
87 |                                             name=self._name,
   |

B023 Function definition does not bind loop variable `name`
   --> ~oolboxv2\utils\daemon\daemon_util.py:216:71
    |
214 |                         async def _helper_runner():
215 |                             try:
216 |                                 attr_f = getattr(self.class_instance, name)
    |                                                                       ^^^^
217 |
218 |                                 if asyncio.iscoroutinefunction(attr_f):
    |

B023 Function definition does not bind loop variable `args`
   --> ~oolboxv2\utils\daemon\daemon_util.py:219:57
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                               ^^^^
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

B023 Function definition does not bind loop variable `kwargs`
   --> ~oolboxv2\utils\daemon\daemon_util.py:219:65
    |
218 | …                     if asyncio.iscoroutinefunction(attr_f):
219 | …                         res = await attr_f(*args, **kwargs)
    |                                                       ^^^^^^
220 | …                     else:
221 | …                         res = attr_f(*args, **kwargs)
    |

B023 Function definition does not bind loop variable `args`
   --> ~oolboxv2\utils\daemon\daemon_util.py:221:51
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                   ^^^^
222 |
223 |                                 if res is None:
    |

B023 Function definition does not bind loop variable `kwargs`
   --> ~oolboxv2\utils\daemon\daemon_util.py:221:59
    |
219 |                                     res = await attr_f(*args, **kwargs)
220 |                                 else:
221 |                                     res = attr_f(*args, **kwargs)
    |                                                           ^^^^^^
222 |
223 |                                 if res is None:
    |

B023 Function definition does not bind loop variable `identifier`
   --> ~oolboxv2\utils\daemon\daemon_util.py:237:51
    |
235 |                                 get_logger().info(f"sending response {res} {type(res)}")
236 |
237 |                                 await sender(res, identifier)
    |                                                   ^^^^^^^^^^
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |

B023 Function definition does not bind loop variable `identifier`
   --> ~oolboxv2\utils\daemon\daemon_util.py:239:64
    |
237 |                                 await sender(res, identifier)
238 |                             except Exception as e:
239 |                                 await sender({"data": str(e)}, identifier)
    |                                                                ^^^^^^^^^^
240 |
241 |                         await _helper_runner()
    |

B023 Function definition does not bind loop variable `fuction`
  --> ~oolboxv2\utils\extras\base_widget.py:74:24
   |
72 |         for fuction in functions:
73 |             def x(r):
74 |                 return fuction(request=r)
   |                        ^^^^^^^
75 |             self.onReload.append(x)
   |

SIM113 Use `enumerate()` for index variable `current_blob_id` in `for` loop
  --> ~oolboxv2\utils\extras\blobs.py:55:13
   |
53 |                 if index_ + 1 > len(blob_ids) and len(all_link[i + splitter:]) > 1:
54 |                     self.add_link(blob_ids[current_blob_id], blob_ids[current_blob_id], link_port)
55 |             current_blob_id += 1
   |             ^^^^^^^^^^^^^^^^^^^^
56 |
57 |     def recover_blob(self, blob_ids, check_blobs_ids):
   |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> ~oolboxv2\utils\extras\blobs.py:180:9
    |
178 |           self.storage = storage
179 |           self.data = b""
180 | /         if key is not None:
181 | |             if Code.decrypt_symmetric(Code.encrypt_symmetric("test", key), key) != "test":
    | |__________________________________________________________________________________________^
182 |                   raise ValueError("Invalid Key")
183 |           self.key = key
    |
help: Combine `if` statements using `and`

B023 Function definition does not bind loop variable `tb_func`
   --> ~oolboxv2\utils\extras\bottleup.py:125:112
    |
123 |                         if request_as_kwarg:
124 |                             def tb_func_(**kw):
125 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read()+tb_func(**kw)
    |                                                                                                                ^^^^^^^
126 |                         else:
127 |                             def tb_func_():
    |

B023 Function definition does not bind loop variable `tb_func`
   --> ~oolboxv2\utils\extras\bottleup.py:128:114
    |
126 |                         else:
127 |                             def tb_func_():
128 |                                 return open(os.path.join(self.tb_app.start_dir, 'dist', 'helper.html')).read() + tb_func()
    |                                                                                                                  ^^^^^^^
129 |                         self.route(f'/{mod_name}', method='GET')(tb_func_)
130 |                         print("adding root:", f'/{mod_name}')
    |

S113 Probable use of `requests` call without timeout
  --> ~oolboxv2\utils\extras\gist_control.py:35:20
   |
33 |         api_url = f"https://api.github.com/gists/{gist_id}"
34 |
35 |         response = requests.get(api_url)
   |                    ^^^^^^^^^^^^
36 |
37 |         if response.status_code == 200:
   |

S113 Probable use of `requests` call without timeout
  --> ~oolboxv2\utils\extras\gist_control.py:77:20
   |
75 |         # Update an existing Gist
76 |         url = f"https://api.github.com/gists/{gist_id}"
77 |         response = requests.patch(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^^
78 |     else:
79 |         # Create a new Gist
   |

S113 Probable use of `requests` call without timeout
  --> ~oolboxv2\utils\extras\gist_control.py:81:20
   |
79 |         # Create a new Gist
80 |         url = "https://api.github.com/gists"
81 |         response = requests.post(url, json=gist_data, headers=headers)
   |                    ^^^^^^^^^^^^^
82 |
83 |     # Check if the request was successful
   |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> ~oolboxv2\utils\proxy\prox_util.py:149:21
    |
147 |                               return await app_attr(*args, **kwargs)
148 |                           return app_attr(*args, **kwargs)
149 | /                     if (name == 'run_any' or name == 'a_run_any') and kwargs.get('get_results', False):
150 | |                         if isinstance(args[0], Enum):
    | |_____________________________________________________^
151 |                               args = (args[0].__class__.NAME.value, args[0].value), args[1:]
152 |                       self.app.sprint(f"Calling method {name}, {args=}, {kwargs}=")
    |
help: Combine `if` statements using `and`

F403 `from .all_functions_enums import *` used; unable to detect undefined names
 --> ~oolboxv2\utils\system\__init__.py:1:1
  |
1 | from .all_functions_enums import *
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2 | from .cache import FileCache, MemoryCache
3 | from .file_handler import FileHandler
  |

S113 Probable use of `requests` call without timeout
   --> ~oolboxv2\utils\system\api.py:151:20
    |
149 |     print(f"Attempting to download executable from {url}...")
150 |     try:
151 |         response = requests.get(url, stream=True)
    |                    ^^^^^^^^^^^^
152 |     except Exception as e:
153 |         print(f"Download error: {e}")
    |

S602 `subprocess` call with `shell=True` identified, security issue
  --> ~oolboxv2\utils\system\conda_runner.py:15:19
   |
13 |     if live:
14 |         # Using subprocess.Popen to stream stdout and stderr live
15 |         process = subprocess.Popen(command, shell=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
   |                   ^^^^^^^^^^^^^^^^
16 |         process.communicate()  # Wait for the process to complete
17 |         return process.returncode == 0, None
   |

S602 `subprocess` call with `shell=True` identified, security issue
  --> ~oolboxv2\utils\system\conda_runner.py:21:18
   |
19 |     try:
20 |         # If not live, capture output and return it
21 |         result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True, encoding='cp850')
   |                  ^^^^^^^^^^^^^^
22 |         return True, result.stdout
23 |     except subprocess.CalledProcessError as e:
   |

B030 `except` handlers should only be exception classes or tuples of exception classes
  --> ~oolboxv2\utils\system\file_handler.py:60:16
   |
58 |                 self.file_handler_max_loaded_index_ = -1
59 |             rdu()
60 |         except OSError and PermissionError as e:
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
61 |             raise e
   |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> ~oolboxv2\utils\system\file_handler.py:111:9
    |
109 |               )
110 |               return False
111 | /         if key not in self.file_handler_load:
112 | |             if key in self.file_handler_key_mapper:
    | |___________________________________________________^
113 |                   key = self.file_handler_key_mapper[key]
    |
help: Combine `if` statements using `and`

B030 `except` handlers should only be exception classes or tuples of exception classes
   --> ~oolboxv2\utils\system\file_handler.py:148:16
    |
146 |                 self.file_handler_load[key] = self.decode_code(line)
147 |
148 |         except json.decoder.JSONDecodeError and Exception:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
149 |
150 |             for line in self.file_handler_storage:
    |

SIM102 Use a single `if` statement instead of nested `if` statements
   --> ~oolboxv2\utils\system\file_handler.py:166:9
    |
164 |       def get_file_handler(self, obj: str, default=None) -> str or None:
165 |           logger = get_logger()
166 | /         if obj not in self.file_handler_load:
167 | |             if obj in self.file_handler_key_mapper:
    | |___________________________________________________^
168 |                   obj = self.file_handler_key_mapper[obj]
169 |           logger.info(Style.ITALIC(Style.GREY(f"Collecting data from storage key : {obj}")))
    |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
  --> ~oolboxv2\utils\system\getting_and_closing_app.py:15:5
   |
13 |   def override_main_app(app):
14 |       global registered_apps
15 | /     if registered_apps[0] is not None:
16 | |         if time.time() - registered_apps[0].called_exit[1] > 30:
   | |________________________________________________________________^
17 |               raise PermissionError("Permission denied because of overtime fuction override_main_app sud only be called "
18 |                                     f"once and ontime overtime {time.time() - registered_apps[0].called_exit[1]}")
   |
help: Combine `if` statements using `and`

SIM102 Use a single `if` statement instead of nested `if` statements
  --> ~oolboxv2\utils\system\ipy_completer.py:64:9
   |
62 |       for _name, obj in inspect.getmembers(module, inspect.isclass):
63 |           # Check if the class is defined in the current module
64 | /         if obj.__module__ == module.__name__:
65 | |             # Check if the class is a dataclass
66 | |             if is_dataclass(obj):
   | |_________________________________^
67 |                   dataclasses.append(obj)
   |
help: Combine `if` statements using `and`

I001 [*] Import block is un-sorted or un-formatted
  --> ~oolboxv2\utils\system\main_tool.py:1:1
   |
 1 | / import asyncio
 2 | | import inspect
 3 | | import os
 4 | | from toolboxv2.utils.extras import Style
 5 | |
 6 | | from .getting_and_closing_app import get_app
 7 | | from .tb_logger import get_logger
 8 | | from .types import Result, ToolBoxError, ToolBoxInfo, ToolBoxInterfaces, ToolBoxResult
   | |______________________________________________________________________________________^
 9 |
10 |   try:
   |
help: Organize imports

S113 Probable use of `requests` call without timeout
   --> ~oolboxv2\utils\system\session.py:265:24
    |
263 |             except Exception as e:
264 |                 print("Error session fetch:", e, self.username)
265 |                 return requests.request(method, url, data=data)
    |                        ^^^^^^^^^^^^^^^^
266 |         else:
267 |             print(f"Could not find session using request on {url}")
    |

S113 Probable use of `requests` call without timeout
   --> ~oolboxv2\utils\system\session.py:269:24
    |
267 |             print(f"Could not find session using request on {url}")
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
    |                        ^^^^^^^^^^^^^^^^
270 |             return requests.request(method, url, data=data)
271 |             # raise Exception("Session not initialized. Please login first.")
    |

S113 Probable use of `requests` call without timeout
   --> ~oolboxv2\utils\system\session.py:270:20
    |
268 |             if method.upper() == 'POST':
269 |                 return requests.request(method, url, json=data)
270 |             return requests.request(method, url, data=data)
    |                    ^^^^^^^^^^^^^^^^
271 |             # raise Exception("Session not initialized. Please login first.")
    |

S113 Probable use of `requests` call without timeout
   --> ~oolboxv2\utils\system\session.py:351:20
    |
349 | def get_public_ip():
350 |     try:
351 |         response = requests.get('https://api.ipify.org?format=json')
    |                    ^^^^^^^^^^^^
352 |         ip_address = response.json()['ip']
353 |         return ip_address
    |

F403 `from .all_functions_enums import *` used; unable to detect undefined names
  --> ~oolboxv2\utils\system\types.py:20:1
   |
18 | from ..extras import generate_test_cases
19 | from ..extras.Style import Spinner
20 | from .all_functions_enums import *
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
21 | from .file_handler import FileHandler
   |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:482:25
    |
482 | class ToolBoxError(str, Enum):
    |                         ^^^^
483 |     none = "none"
484 |     input_error = "InputError"
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:489:30
    |
489 | class ToolBoxInterfaces(str, Enum):
    |                              ^^^^
490 |     cli = "CLI"
491 |     api = "API"
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:532:62
    |
530 |     def as_result(self):
531 |         return Result(
532 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^
533 |             result=ToolBoxResult(
534 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:534:86
    |
532 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
533 |             result=ToolBoxResult(
534 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^
535 |                 data_info=self.result.data_info,
536 |                 data=self.result.data,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:574:64
    |
572 |     def as_dict(self):
573 |         return {
574 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                                ^^^^
575 |         "result" : {
576 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:576:84
    |
574 |             "error":self.error.value if isinstance(self.error, Enum) else self.error,
575 |         "result" : {
576 |             "data_to":self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                    ^^^^
577 |             "data_info":self.result.data_info,
578 |             "data":self.result.data,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:611:62
    |
609 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
610 |         return ApiResult(
611 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
    |                                                              ^^^^
612 |             result=ToolBoxResultBM(
613 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:613:86
    |
611 |             error=self.error.value if isinstance(self.error, Enum) else self.error,
612 |             result=ToolBoxResultBM(
613 |                 data_to=self.result.data_to.value if isinstance(self.result.data_to, Enum) else self.result.data_to,
    |                                                                                      ^^^^
614 |                 data_info=self.result.data_info,
615 |                 data=self.result.data,
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:633:46
    |
631 |         # print(f" error={self.error}, result= {self.result}, info= {self.info}, origin= {self.origin}")
632 |         return ApiResult(
633 |             error=error if isinstance(error, Enum) else error,
    |                                              ^^^^
634 |             result=ToolBoxResultBM(
635 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:635:84
    |
633 |             error=error if isinstance(error, Enum) else error,
634 |             result=ToolBoxResultBM(
635 |                 data_to=result.get('data_to') if isinstance(result.get('data_to'), Enum) else result.get('data_to'),
    |                                                                                    ^^^^
636 |                 data_info=result.get('data_info', '404'),
637 |                 data=result.get('data'),
    |

F405 `Enum` may be undefined, or defined from star imports
   --> ~oolboxv2\utils\system\types.py:884:45
    |
882 |     async def aget(self, key=None, default=None):
883 |         if asyncio.isfuture(self.result.data) or asyncio.iscoroutine(self.result.data) or (
884 |             isinstance(self.result.data_to, Enum) and self.result.data_to.name == ToolBoxInterfaces.future.name):
    |                                             ^^^^
885 |             data = await self.result.data
886 |         else:
    |

F405 `CLOUDM_AUTHMANAGER` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1034:35
     |
1033 |     async def get_user(self, username: str) -> Result:
1034 |         return self.app.a_run_any(CLOUDM_AUTHMANAGER.GET_USER_BY_NAME, username=username, get_results=True)
     |                                   ^^^^^^^^^^^^^^^^^^
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1192:29
     |
1191 |     def _get_function(self,
1192 |                       name: Enum or None,
     |                             ^^^^
1193 |                       state: bool = True,
1194 |                       specification: str = "app",
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1251:34
     |
1249 |         """proxi attr"""
1250 |
1251 |     def get_function(self, name: Enum or tuple, **kwargs):
     |                                  ^^^^
1252 |         """
1253 |         Kwargs for _get_function
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1266:47
     |
1264 |         """
1265 |
1266 |     def run_function(self, mod_function_name: Enum or tuple,
     |                                               ^^^^
1267 |                      tb_run_function_with_state=True,
1268 |                      tb_run_with_specification='app',
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1276:55
     |
1274 |         """proxi attr"""
1275 |
1276 |     async def a_run_function(self, mod_function_name: Enum or tuple,
     |                                                       ^^^^
1277 |                              tb_run_function_with_state=True,
1278 |                              tb_run_with_specification='app',
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1306:49
     |
1304 |         """
1305 |
1306 |     async def run_http(self, mod_function_name: Enum or str or tuple, function_name=None, method="GET",
     |                                                 ^^^^
1307 |                        args_=None,
1308 |                        kwargs_=None,
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1312:42
     |
1310 |         """run a function remote via http / https"""
1311 |
1312 |     def run_any(self, mod_function_name: Enum or str or tuple, backwords_compability_variabel_string_holder=None,
     |                                          ^^^^
1313 |                 get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
1314 |                 kwargs_=None,
     |

F405 `Enum` may be undefined, or defined from star imports
    --> ~oolboxv2\utils\system\types.py:1318:50
     |
1316 |         """proxi attr"""
1317 |
1318 |     async def a_run_any(self, mod_function_name: Enum or str or tuple,
     |                                                  ^^^^
1319 |                         backwords_compability_variabel_string_holder=None,
1320 |                         get_results=False, tb_run_function_with_state=True, tb_run_with_specification='app', args_=None,
     |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:879:60
    |
877 | …                     async def _():
878 | …                         try:
879 | …                             if asyncio.iscoroutine(result):
    |                                                      ^^^^^^
880 | …                                 await result
881 | …                             if hasattr(result, 'Name'):
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:880:47
    |
878 | …                     try:
879 | …                         if asyncio.iscoroutine(result):
880 | …                             await result
    |                                     ^^^^^^
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:881:48
    |
879 | …                     if asyncio.iscoroutine(result):
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
    |                                  ^^^^^^
882 | …                         print('Opened :', result.Name)
883 | …                     elif hasattr(result, 'name'):
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:882:59
    |
880 | …                         await result
881 | …                     if hasattr(result, 'Name'):
882 | …                         print('Opened :', result.Name)
    |                                             ^^^^^^
883 | …                     elif hasattr(result, 'name'):
884 | …                         print('Opened :', result.name)
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:883:50
    |
881 | …                         if hasattr(result, 'Name'):
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
    |                                        ^^^^^^
884 | …                             print('Opened :', result.name)
885 | …                     except Exception as e:
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:884:59
    |
882 | …                             print('Opened :', result.Name)
883 | …                         elif hasattr(result, 'name'):
884 | …                             print('Opened :', result.name)
    |                                                 ^^^^^^
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:887:48
    |
885 | …                     except Exception as e:
886 | …                         self.debug_rains(e)
887 | …                         if hasattr(result, 'Name'):
    |                                      ^^^^^^
888 | …                             print('Error opening :', result.Name)
889 | …                         elif hasattr(result, 'name'):
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:888:66
    |
886 | …                     self.debug_rains(e)
887 | …                     if hasattr(result, 'Name'):
888 | …                         print('Error opening :', result.Name)
    |                                                    ^^^^^^
889 | …                     elif hasattr(result, 'name'):
890 | …                         print('Error opening :', result.name)
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:889:50
    |
887 | …                             if hasattr(result, 'Name'):
888 | …                                 print('Error opening :', result.Name)
889 | …                             elif hasattr(result, 'name'):
    |                                            ^^^^^^
890 | …                                 print('Error opening :', result.name)
891 | …                     asyncio.create_task(_())
    |

B023 Function definition does not bind loop variable `result`
   --> ~oolboxv2\utils\toolbox.py:890:66
    |
888 |                                         print('Error opening :', result.Name)
889 |                                     elif hasattr(result, 'name'):
890 |                                         print('Error opening :', result.name)
    |                                                                  ^^^^^^
891 |                             asyncio.create_task(_())
892 |                         else:
    |

Found 629 errors.
[*] 14 fixable with the `--fix` option (57 hidden fixes can be enabled with the `--unsafe-fixes` option).

[Safety] Exit Code: 1
[Safety] Output:
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Scripts\safety.exe\__main__.py", line 4, in <module>
  File "C:\Users\<USER>\Workspace\ToolBoxV2\.venv\Lib\site-packages\safety\cli.py", line 593, in <module>
    typer.rich_utils.STYLE_HELPTEXT = ""
    ^^^^^^^^^^^^^^^^
AttributeError: module 'typer' has no attribute 'rich_utils'

[Versions] Exit Code: 0
[Versions] Output:
Starting ToolBox as main from : [1m[36mC:\Users\<USER>\Workspace\ToolBoxV2\toolboxv2[0m[0m
[33m[1mCould not rename log file appending on Logs-toolbox-main-2025-09-02-ERROR[0m[0m
Logger in Default
================================
[36mSystem$main-DESKTOP-CI57V1L:[0m Infos:
  Name     -> DESKTOP-CI57V1L
  ID       -> main-DESKTOP-CI57V1L
  Version  -> 0.1.22


------------------ Version ------------------

[1m[36m[3mRE[0m[0m[0m[3mSimple[0mToolBox:  0.1.22  



[1m[3m- end -[0m[0m

