<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SimpleCore - Innovation Framework</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 2rem;
            background: var(--theme-bg, #f5f5f5);
            color: var(--theme-text, #333);
        }
        .framework-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .section {
            padding: 2rem;
            border-bottom: 1px solid #eee;
        }
        .section:last-child { border-bottom: none; }
        .section-title {
            color: #667eea;
            font-size: 1.8rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        .component-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .status-implemented { border-left-color: #28a745; }
        .status-progress { border-left-color: #ffc107; }
        .status-planned { border-left-color: #dc3545; }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .implemented { background: #d4edda; color: #155724; }
        .progress { background: #fff3cd; color: #856404; }
        .planned { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="framework-container">
        <div class="header">
            <h1>🚀 SimpleCore Innovation Framework</h1>
            <p>Kollaborative Plattform für Ideenentwicklung und Innovation</p>
        </div>

        <!-- 1. DER PITCH -->
        <div class="section">
            <h2 class="section-title">💡 Der Pitch - Die Kernidee</h2>
            
            <h3>Vision</h3>
            <p><strong>SimpleCore verbindet Menschen mit ähnlichen Ideen und zeigt die Bausteine der Innovation.</strong></p>
            
            <h3>Das Problem</h3>
            <ul>
                <li>Innovatoren arbeiten isoliert an ähnlichen Ideen</li>
                <li>Wissen über Ideenkomponenten ist fragmentiert</li>
                <li>Keine Transparenz über bestehende Lösungsansätze</li>
                <li>Fehlende Kollaborationsmöglichkeiten</li>
            </ul>

            <h3>Die Lösung</h3>
            <p>SimpleCore erstellt einen <strong>Innovation Graph</strong>, der:</p>
            <ul>
                <li><strong>Ideenverbindungen</strong> sichtbar macht (Akku-Schrauber ↔ Batterie + Motor + Schraubenschlüssel)</li>
                <li><strong>Menschen mit ähnlichen Ideen</strong> vernetzt</li>
                <li><strong>Komponenten-Abhängigkeiten</strong> transparent darstellt</li>
                <li><strong>Kollaborative Weiterentwicklung</strong> ermöglicht</li>
            </ul>

            <h3>Beispiel-Szenario</h3>
            <div class="component-card">
                <strong>Idee:</strong> Akku-Schrauber<br>
                <strong>Komponenten:</strong> Batterie → Motor → Schraubenschlüssel<br>
                <strong>Verbindung:</strong> Person A + Person B entdecken gemeinsame Idee<br>
                <strong>Kollaboration:</strong> Gemeinsame Verfeinerung und Umsetzung
            </div>
        </div>

        <!-- 2. KOMPONENTEN -->
        <div class="section">
            <h2 class="section-title">🔧 Systemkomponenten</h2>
            
            <div class="component-grid">
                <div class="component-card status-implemented">
                    <span class="status-badge implemented">✅ Implementiert</span>
                    <h4>Idea Capture System</h4>
                    <p>Erfassung und Speicherung von Ideen mit automatischer Verbindungserkennung</p>
                    <small>SimpleCore.py - add_idea(), DataManager</small>
                </div>

                <div class="component-card status-implemented">
                    <span class="status-badge implemented">✅ Implementiert</span>
                    <h4>Web Interface</h4>
                    <p>Benutzeroberfläche für Ideeneingabe mit Belohnungssystem</p>
                    <small>index.html - HTMX Forms, Reward Animation</small>
                </div>

                <div class="component-card status-progress">
                    <span class="status-badge progress">🔄 In Arbeit</span>
                    <h4>Innovation Tree Visualizer</h4>
                    <p>Grafische Darstellung von Ideenverbindungen und Abhängigkeiten</p>
                    <small>innovation_tree.html - Cytoscape.js Integration</small>
                </div>

                <div class="component-card status-progress">
                    <span class="status-badge progress">🔄 In Arbeit</span>
                    <h4>Digital Workbench</h4>
                    <p>Kollaborative Arbeitsumgebung für Ideenentwicklung</p>
                    <small>workbench.html - Canvas-basierte Entwicklung</small>
                </div>

                <div class="component-card status-planned">
                    <span class="status-badge planned">📋 Geplant</span>
                    <h4>User Matching System</h4>
                    <p>Algorithmus zur Verbindung von Nutzern mit ähnlichen Ideen</p>
                    <small>Benötigt: ML-basierte Ähnlichkeitserkennung</small>
                </div>

                <div class="component-card status-planned">
                    <span class="status-badge planned">📋 Geplant</span>
                    <h4>Component Dependency Tracker</h4>
                    <p>System zur Erkennung und Verfolgung von Ideenkomponenten</p>
                    <small>Benötigt: Ontologie-basierte Komponentenerkennung</small>
                </div>

                <div class="component-card status-planned">
                    <span class="status-badge planned">📋 Geplant</span>
                    <h4>Collaboration Hub</h4>
                    <p>Tools für gemeinsame Ideenentwicklung und Projektmanagement</p>
                    <small>Benötigt: Real-time Collaboration Features</small>
                </div>

                <div class="component-card status-planned">
                    <span class="status-badge planned">📋 Geplant</span>
                    <h4>Knowledge Graph Database</h4>
                    <p>Strukturierte Speicherung von Ideen, Komponenten und Verbindungen</p>
                    <small>Benötigt: Graph Database Integration (Neo4j/ArangoDB)</small>
                </div>
            </div>
        </div>

        <!-- 3. UMSETZUNGSSTAND -->
        <div class="section">
            <h2 class="section-title">📊 Umsetzungsstand & Roadmap</h2>
            
            <h3>✅ Phase 1: Grundfunktionen (Implementiert)</h3>
            <ul>
                <li><strong>Backend:</strong> ToolBoxV2 SimpleCore Modul mit Datenverwaltung</li>
                <li><strong>Frontend:</strong> Web-Interface für Ideeneingabe</li>
                <li><strong>Database:</strong> Grundlegende Ideenspeicherung</li>
                <li><strong>API:</strong> REST-Endpoints für CRUD-Operationen</li>
            </ul>

            <h3>🔄 Phase 2: Visualisierung (In Arbeit)</h3>
            <ul>
                <li><strong>Innovation Tree:</strong> Grafische Darstellung von Ideenverbindungen</li>
                <li><strong>Workbench:</strong> Canvas-basierte Ideenentwicklung</li>
                <li><strong>UI/UX:</strong> Verbesserung der Benutzeroberfläche</li>
            </ul>

            <h3>📋 Phase 3: Intelligente Verbindungen (Geplant)</h3>
            <ul>
                <li><strong>ML-Integration:</strong> Automatische Ideenähnlichkeit</li>
                <li><strong>User Matching:</strong> Verbindung ähnlicher Nutzer</li>
                <li><strong>Component Analysis:</strong> Automatische Komponentenerkennung</li>
                <li><strong>Semantic Search:</strong> Intelligente Ideensuche</li>
            </ul>

            <h3>🚀 Phase 4: Kollaboration (Zukunft)</h3>
            <ul>
                <li><strong>Real-time Collaboration:</strong> Gemeinsame Ideenbearbeitung</li>
                <li><strong>Project Management:</strong> Von Idee zur Umsetzung</li>
                <li><strong>Community Features:</strong> Bewertungen, Kommentare, Forks</li>
                <li><strong>Integration:</strong> GitHub, Patent-DBs, Forschungsdaten</li>
            </ul>

            <div class="component-card">
                <h4>🎯 Nächste Schritte</h4>
                <ol>
                    <li>Innovation Tree Visualisierung fertigstellen</li>
                    <li>Datensammlung und -validierung bestehender Ideen</li>
                    <li>ML-Modell für Ideenähnlichkeit entwickeln</li>
                    <li>User Matching Algorithmus implementieren</li>
                    <li>Beta-Test mit ersten Nutzern</li>
                </ol>
            </div>
        </div>
    </div>

    <footer style="text-align: center; padding: 2rem; color: #666;">
        <p>SimpleCore - Powered by <a href="https://github.com/MarkinHaus/ToolBoxV2">ToolBoxV2</a></p>
    </footer>
</body>
</html>