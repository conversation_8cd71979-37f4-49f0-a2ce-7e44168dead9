# SimpleCore - Detailed Implementation Plan

This document outlines the complete development lifecycle for the SimpleCore platform, designed as a modular application suite built on the ToolBoxV2 framework. It integrates two core concepts: the **Digital Workbench** for idea development and the **Innovation Tree** for visualizing technological history.

---

## Part 1: The Innovation Tree

### Phase 1: Backend Foundation and Data Model

**Status:** ⬜ **To-Do**

**Goal:** Create the core backend modules and database schema required to store, manage, and serve the complex graph data of the Innovation Tree.

**Analysis:** This phase is critical for the success of the entire project. The data structure must accurately represent the nuanced relationships between innovations (direct, logical, hypothetical) and their associated metadata, such as confidence levels and evidence. We will leverage ToolBoxV2's DB module for persistence.

**Sub-Tasks:**

- [ ] **1. Module: `DataManager` (Schema Definition)**
    -   **Goal:** Define the table structure in the database to store the graph's nodes and edges.
    -   **Action:** Within the `on_start` method of the main `InnovationTree` module, we will use the DB module to create the necessary tables if they do not exist.
        -   **Table `it_nodes`:** Stores each innovation.
            -   `id` (PRIMARY KEY, TEXT)
            -   `name` (TEXT)
            -   `description` (TEXT)
            -   `category` (JSON, e.g., `["Tools", "Communication"]`)
            -   `timeframe` (TEXT, e.g., `~3200 BC`)
            -   `location` (TEXT, e.g., `Mesopotamia`)
            -   `importance_level` (FLOAT, 0.0-1.0)
        -   **Table `it_edges`:** Stores each connection between innovations.
            -   `id` (PRIMARY KEY, AUTOINCREMENT)
            -   `from_node` (TEXT, FOREIGN KEY to `it_nodes.id`)
            -   `to_node` (TEXT, FOREIGN KEY to `it_nodes.id`)
            -   `connection_type` (TEXT, ENUM: `DIRECT`, `LOGICAL`, `HYPOTHETICAL`, `ABSTRACT`)
            -   `confidence` (FLOAT, 0.0-1.0)
            -   `necessity` (TEXT, ENUM: `essential`, `helpful`, `optional`)
            -   `evidence` (JSON, e.g., `{"summary": "...", "sources": ["Smith2019"]}`)
            -   `time_gap_years` (INTEGER)
            -   `controversy` (TEXT, Optional)

- [ ] **2. Module: `InnovationTree` (Core Logic & API)**
    -   **Goal:** Create a dedicated module to act as the main controller for all Innovation Tree operations and provide an API for the frontend.
    -   **Action:** Create the file `toolboxv2/mods/InnovationTree.py`.
        -   The main class `Tools(MainTool)` will encapsulate the business logic.
        -   Implement API endpoints using the `@export(api=True)` decorator:
            -   `get_full_graph() -> Result`: Retrieves all nodes and edges from the DB and returns them as JSON.
            -   `get_graph_slice(filters: RequestData) -> Result`: Accepts filters (category, timeframe, confidence_threshold) from query parameters and returns a filtered graph.
            -   `get_node_details(node_id: str) -> Result`: Retrieves detailed information for a single node, including its dependencies and the innovations it enabled.
            -   `add_node(data: RequestData) -> Result`: (Admin function) Adds a new innovation.
            -   `add_edge(data: RequestData) -> Result`: (Admin function) Adds a new connection.

- [ ] **3. Data Pilot Project**
    -   **Goal:** Populate the system with an initial, high-quality dataset.
    -   **Action:**
        -   Create a CSV or JSON file (`pilot_data.json`) containing the first 50-100 key innovations and their connections according to the defined schema.
        -   Create a Python script (`toolboxv2/scripts/populate_innovation_tree.py`) that reads the data file and uses the app's DB instance to populate the `it_nodes` and `it_edges` tables.

---

### Phase 2: Frontend Visualization and Interaction

**Status:** ⬜ **To-Do**

**Goal:** Create a responsive and intuitive user interface for visualizing and exploring the innovation graph.

**Analysis:** The biggest challenge is the performant rendering of a potentially large graph. D3.js or Cytoscape.js are ideal for this. The interaction must be fluid, and the complex data (connection types, confidence levels) must be visualized clearly.

**Sub-Tasks:**

- [ ] **1. Module: `InnovationTree` (UI Endpoint)**
    -   **Goal:** Provide an API endpoint that serves the main HTML page for the visualization.
    -   **Action:** In the `InnovationTree.py` module, add a new endpoint:
        -   `@export(name="ui", api=True)`
        -   This function will load an HTML template file (`innovation_tree.html`) and return it as `Result.html`.

- [ ] **2. Frontend: The "Innovation Tree" View (UI)**
    -   **Goal:** Build the interactive graph.
    -   **Action:** Create the file `toolboxv2/web/templates/innovation_tree.html`.
        -   **Integration:** Embed Cytoscape.js for network visualizations.
        -   **Data Fetching:** Use `TB.api.request('InnovationTree', 'get_full_graph')` on page load to get the graph data.
        -   **Visual Representation:**
            -   Implement visual logic based on edge data:
                -   **Line Style:** Solid (`DIRECT`), dashed (`LOGICAL`), dotted (`HYPOTHETICAL`), wavy (`ABSTRACT`).
                -   **Color Coding:** Use a color scale (Green → Yellow → Orange → Red) for the `confidence` attribute.
        -   **Interactive Elements:**
            -   **Hover:** Display a tooltip with type, confidence, and evidence summary when hovering over an edge.
            -   **Click:** On node click, call `TB.api.request('InnovationTree', 'get_node_details', {node_id: '...'})` and show details in a sidebar.
            -   **Filter UI:** Create controls (dropdowns, sliders) that call `TB.api.request('InnovationTree', 'get_graph_slice', {filter: '...'})` and redraw the graph.
            -   **Search:** A search field to highlight nodes in the graph.

---

### Phase 3: Advanced Features and Community Engagement

**Status:** ⬜ **To-Do**

**Goal:** Extend the platform with advanced interaction capabilities and features for community involvement.

**Analysis:** This phase builds on the functional prototype, adding more complex features that allow for deeper analysis of the graph and decentralize data collection.

**Sub-Tasks:**

- [ ] **1. Database Extension for Proposals**
    -   **Goal:** Create a structure for storing user-submitted proposals.
    -   **Action:** In `DataManager` (or `on_start`), define a new table:
        -   **Table `it_proposals`:** Stores proposals for new nodes or edges. Includes fields like `proposal_type`, `proposed_data` (JSON), `submitter_user_id`, `status` (pending, approved, rejected), `reviewer_notes`.

- [ ] **2. Backend: API Extensions**
    -   **Goal:** Provide endpoints for community features.
    -   **Action:** In the `InnovationTree.py` module, add new functions:
        -   `submit_proposal(data: RequestData) -> Result`: Allows authenticated users to submit proposals.
        -   `get_pending_proposals() -> Result`: (Admin function) Retrieves all pending proposals for a review interface.
        -   `review_proposal(data: RequestData) -> Result`: (Admin function) Allows for approving or rejecting a proposal. On approval, data is transferred to the main tables.

- [ ] **3. Frontend: Advanced Interactions & Community UI**
    -   **Goal:** Make the new features accessible in the frontend.
    -   **Action:**
        -   **Extend Filters:** Implement UI switches for:
            -   Confidence threshold.
            -   Connection type filters.
            -   "Controversy Mode" to highlight contested connections.
        -   **Path Tracing:** A feature to select two nodes and visualize the shortest or most logical path between them.
        -   **Submission Form:** Create a modal or page for users to propose new innovations or connections, sending data to the `submit_proposal` endpoint.

---

## Part 2: The Digital Workbench

### Phase 1: Foundation - Universal Canvas & Core Modules (3 Months)

**Status:** ☑️ **Done**

**Goal:** Create a stable foundation with a functional infinite canvas for 1D and 2D elements, basic data persistence, and simple collaboration features.

**Analysis:** This is the most critical phase, as it lays the groundwork for all other dimensions and functions. The core is the Universal Element Schema. The backend architecture must be designed for scalability and real-time capability from the outset.

**Sub-Tasks:**

- [x] **1. Module: `DataManager` (Universal Database Schema)**
    -   **Goal:** Map the complex data structure for elements, workspaces, and versions in the database.
    -   **Action:** Use ToolBoxV2's DB module to define the following tables:
        -   `dw_workspaces`: Contains metadata for each project (`id`, `name`, `workspace_settings` (JSONB), `external_integrations` (JSONB)).
        -   `dw_elements`: The central table for all objects on the canvas.
            -   `id` (PRIMARY KEY, TEXT)
            -   `workspace_id` (FOREIGN KEY to `dw_workspaces.id`)
            -   `type` (TEXT: `text`, `image`, `group`, etc.)
            -   `dimension` (TEXT: `1D`, `2D`, `3D`, `4D`)
            -   `position` (JSONB: `{x, y, z, scale}`)
            -   `content` (JSONB: Stores `native_format` and `preview_format` metadata)
            -   `relationships` (JSONB: Array of connections to other elements)
            -   `collaboration` (JSONB: `{owner, editors, lock_status}`)
        -   `dw_element_versions`: Stores snapshots of elements for version control.
        -   `dw_workspace_members`: Manages users, roles, and permissions per workspace.

- [x] **2. Module: `ElementManager` (CRUD for Canvas Objects)**
    -   **Goal:** Provide a central service for creating, querying, and modifying universal elements.
    -   **Action:** Create `toolboxv2/mods/SimpleCore/ElementManager.py` with `@export(api=True)` functions:
        -   `create_element(workspace_id, type, dimension, position, content) -> Result`
        -   `get_workspace_elements(workspace_id) -> Result`
        -   `update_element_position(element_id, new_position) -> Result`
        -   `update_element_content(element_id, new_content) -> Result`
        -   `link_elements(from_id, to_id, relationship_data) -> Result`

- [x] **3. Module: `CollaborationManager` (Real-Time Engine)**
    -   **Goal:** Build the infrastructure for live collaboration.
    -   **Action:** Create `toolboxv2/mods/SimpleCore/CollaborationManager.py`:
        -   Use the `WebSocketManager` from ToolBoxV2 to manage a real-time channel per workspace.
        -   Implement a `ws_workspace_handler(websocket, workspace_id)` endpoint.
        -   The `ElementManager` will call a function in the `CollaborationManager` after every successful change to broadcast the update to all connected clients.
        -   Implement API endpoints: `lock_element(element_id)` and `unlock_element(element_id)`.

- [x] **4. Frontend: The "Workbench" Canvas (UI)**
    -   **Goal:** Implement the infinite canvas with 1D (text) and 2D (image) elements.
    -   **Action:** Create the `workbench.html` page:
        -   Integrate `Fabric.js` or `Konva.js` for 2D canvas management.
        -   Fetch initial data using `TB.api.request('ElementManager', 'get_workspace_elements')`.
        -   Establish a WebSocket connection to the `ws_workspace_handler`.
        -   Implement event listeners to react to broadcast messages for real-time updates.
        -   Implement basic interactions: select, move, resize elements, and add simple comments.

---

### Phase 2: Dimension Expansion & Integrations (4 Months)

**Status:** ⬜ **To-Do**

**Goal:** Extend the workbench with 3D and 4D capabilities and implement the first external tool integrations (Figma, GitHub).

**Analysis:** This phase significantly increases technical complexity. The frontend must manage multiple rendering engines (Canvas 2D, WebGL for 3D) in parallel. The backend needs a robust pipeline for importing, converting, and storing large and complex file formats.

**Sub-Tasks:**

- [ ] **1. Backend: Support for Complex Formats**
    -   **Goal:** Prepare the system to handle 3D models and code repositories.
    -   **Action:**
        -   Integrate a file storage module (e.g., the `FileWidget` module) for large binary files. The `dw_elements.content` field will then only store a reference to the file.
        -   Develop a `FormatConverter` service in the backend to asynchronously generate `glTF` from CAD files or thumbnails from design files.

- [ ] **2. Module: `ImportManager` (External Connections)**
    -   **Goal:** Create a bridge to external services like Figma and GitHub.
    -   **Action:** Create `toolboxv2/mods/SimpleCore/ImportManager.py`:
        -   `import_from_figma(api_key, file_id) -> Result`: Uses the Figma API to create 2D elements on the canvas. Supports live sync via webhooks.
        -   `sync_github_repo(repo_url, branch) -> Result`: Clones a Git repository, analyzes the file structure, and represents it as a tree of 4D elements.

- [ ] **3. Frontend: 3D and 4D Representation**
    -   **Goal:** Visualize and make the new dimensions editable on the canvas.
    -   **Action:** In `workbench.html`:
        -   Integrate `Three.js` into the canvas system. If an element is of type 3D, it will be rendered in a Three.js scene.
        -   Implement a 3D viewer with orbit controls (pan, zoom, rotate).
        -   Integrate a code editor (e.g., Monaco Editor) that appears when a 4D element is selected.
        -   Implement dimensional layers: UI controls to switch between views (e.g., 2D only, 3D only, mixed).

---

### Phase 3: Intelligence Layer (3 Months)

**Status:** ⬜ **To-Do**

**Goal:** Equip the workbench with AI-powered features to accelerate workflows and enable new insights.

**Analysis:** This phase utilizes the `isaa` module (Intelligent System & Automation Agent) of ToolBoxV2 to perform complex analysis and automation tasks. The challenge is to integrate the AI results seamlessly and unobtrusively into the user interface.

**Sub-Tasks:**

- [ ] **1. Backend: AI-Powered Analysis Functions**
    -   **Goal:** Provide API endpoints for intelligent operations.
    -   **Action:** Extend the existing managers:
        -   In `ElementManager`, implement `analyze_element_content(element_id) -> Result`. This function sends the element's content to `isaa` for auto-tagging, summaries, or color scheme extraction.
        -   In `RelationshipManager`, implement `suggest_links(element_id) -> Result`. This function uses `isaa` to analyze the element's content and find semantically similar or dependent elements in the same workspace.
        -   In `ElementManager`, implement `auto_layout_workspace(workspace_id) -> Result`: Uses AI to suggest a logical arrangement of elements on the canvas.

- [ ] **2. Frontend: Displaying AI Results**
    -   **Goal:** Present AI suggestions to the user visually and interactively.
    -   **Action:** In `workbench.html`:
        -   Add an "Organize" button that calls `auto_layout_workspace` and animates the new positions.
        -   Display AI-suggested links as faint, dotted lines on the canvas. A click allows the user to confirm or reject the connection.
        -   Show the `ai_analysis` section (e.g., generated tags) in the element's detail view.

---

### Phase 4: Enterprise Features & Polish (2 Months)

**Status:** ⬜ **To-Do**

**Goal:** Prepare the application for professional use with robust team management, advanced permissions, and a final UI/UX polish.

**Sub-Tasks:**

- [ ] **1. Backend: Advanced Permission Management**
    -   **Goal:** Enable granular control over who can do what in a workspace.
    -   **Action:** Extend the `CollaborationManager`:
        -   Implement logic that evaluates the `permissions` and `role` fields from `dw_workspace_members`.
        -   Each API endpoint in `ElementManager` and other services will check if the calling user has the required permission.
        -   Create API endpoints for team management: `invite_user_to_workspace`, `update_user_role`.

- [ ] **2. UI/UX: Final Polish**
    -   **Goal:** Make the user experience seamless, intuitive, and performant.
    -   **Action:**
        -   **Performance Optimization:** Implement caching strategies and lazy loading for large workspaces.
        -   **Minimap:** Add a small overview map for navigation in very large projects.
        -   **Smart Zoom:** Implement a function that intelligently zooms to fit a selection of elements in the viewport.
        -   **Responsive Design:** Ensure the application is well-usable on various screen sizes, especially tablets.
        -   **Consistent Notifications:** Use `TB.ui.Toast` for all success, error, and information messages.

- [ ] **3. Documentation and Tests**
    -   **Goal:** Ensure the quality and maintainability of the application.
    -   **Action:**
        -   Write a comprehensive `docs/digital_workbench_user_guide.md`.
        -   Create a `docs/developer_guide.md` describing the module architecture.
        -   Write unit tests for all critical backend functions and integration tests for the API endpoints.