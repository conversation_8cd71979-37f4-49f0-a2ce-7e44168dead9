[{"sha": "07406e6", "date": "2025-05-18 02:17:13 +0200", "message": "[t:d] tbjs  Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 talk : 0.0.1 ProcessManager : 0.0.1 setup : 0.0.3 TruthSeeker : unknown FileWidget : 1.0.0 WhatsAppTb : unknown MinimalHtml : 0.0.2 welcome : 0.1.21 TestWidget : 0.0.1 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "a7ae777", "date": "2025-05-17 22:54:53 +0200", "message": "[t:d] tbjs Ruff❌, Safety✅"}, {"sha": "2b568b8", "date": "2025-05-17 22:52:39 +0200", "message": "[t:d] Ruff❌, Safety✅"}, {"sha": "f8b8254", "date": "2025-05-16 17:34:31 +0200", "message": " add auto tagging [t:d] Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 FileWidget : 1.0.0 ProcessManager : 0.0.1 talk : 0.0.1 TruthSeeker : unknown welcome : 0.1.21 setup : 0.0.3 WhatsAppTb : unknown MinimalHtml : 0.0.2 TestWidget : 0.0.1 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "7214f45", "date": "2025-05-16 17:32:20 +0200", "message": " [t:d] Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DoNext : 0.1.21 DashProvider : 0.0.1 talk : 0.0.1 ProcessManager : 0.0.1 FileWidget : 1.0.0 welcome : 0.1.21 setup : 0.0.3 MinimalHtml : 0.0.2 TruthSeeker : unknown WhatsAppTb : unknown SocketManager : 0.1.9 TestWidget : 0.0.1 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "8563357", "date": "2025-05-16 17:26:49 +0200", "message": " [t:d] Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DoNext : 0.1.21 DashProvider : 0.0.1 MinimalHtml : 0.0.2 CodeVerification : 0.0.1 FileWidget : 1.0.0 ProcessManager : 0.0.1 talk : 0.0.1 setup : 0.0.3 welcome : 0.1.21 TruthSeeker : unknown TestWidget : 0.0.1 WhatsAppTb : unknown SocketManager : 0.1.9 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "d830ca5", "date": "2025-05-15 13:58:48 +0200", "message": "Update setup_helper.py"}, {"sha": "92e1b99", "date": "2025-05-15 13:37:15 +0200", "message": "Update installer.sh"}, {"sha": "01c9925", "date": "2025-05-15 13:32:14 +0200", "message": "Update installer.sh"}, {"sha": "6cdce8a", "date": "2025-05-15 13:07:16 +0200", "message": "Update pre-commit.sh"}, {"sha": "f72db98", "date": "2025-05-14 22:01:49 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 FileWidget : 1.0.0 setup : 0.0.3 ProcessManager : 0.0.1 welcome : 0.1.21 MinimalHtml : 0.0.2 talk : 0.0.1 TruthSeeker : unknown SocketManager : 0.1.9 WhatsAppTb : unknown TestWidget : 0.0.1 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "f829ba9", "date": "2025-05-14 01:48:42 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 FileWidget : 1.0.0 ProcessManager : 0.0.1 DoNext : 0.1.21 setup : 0.0.3 welcome : 0.1.21 talk : 0.0.1 MinimalHtml : 0.0.2 WhatsAppTb : unknown TruthSeeker : unknown SocketManager : 0.1.9 TestWidget : 0.0.1 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "86132db", "date": "2025-05-14 01:47:34 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 talk : 0.0.1 FileWidget : 1.0.0 ProcessManager : 0.0.1 setup : 0.0.3 MinimalHtml : 0.0.2 welcome : 0.1.21 TruthSeeker : unknown TestWidget : 0.0.1 WhatsAppTb : unknown SocketManager : 0.1.9 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "b05e925", "date": "2025-05-14 01:09:58 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 ProcessManager : 0.0.1 DoNext : 0.1.21 CodeVerification : 0.0.1 FileWidget : 1.0.0 welcome : 0.1.21 setup : 0.0.3 talk : 0.0.1 TruthSeeker : unknown WhatsAppTb : unknown TestWidget : 0.0.1 MinimalHtml : 0.0.2 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "a7c5dba", "date": "2025-05-14 00:54:40 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 DoNext : 0.1.21 ProcessManager : 0.0.1 talk : 0.0.1 welcome : 0.1.21 FileWidget : 1.0.0 setup : 0.0.3 TestWidget : 0.0.1 TruthSeeker : unknown MinimalHtml : 0.0.2 WhatsAppTb : unknown SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "c80ce69", "date": "2025-05-14 00:45:57 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 welcome : 0.1.21 MinimalHtml : 0.0.2 ProcessManager : 0.0.1 talk : 0.0.1 FileWidget : 1.0.0 setup : 0.0.3 TruthSeeker : unknown TestWidget : 0.0.1 SocketManager : 0.1.9 WhatsAppTb : unknown EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "6dbf0b9", "date": "2025-05-14 00:37:36 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 DoNext : 0.1.21 CodeVerification : 0.0.1 FileWidget : 1.0.0 ProcessManager : 0.0.1 welcome : 0.1.21 talk : 0.0.1 MinimalHtml : 0.0.2 TruthSeeker : unknown setup : 0.0.3 SocketManager : 0.1.9 TestWidget : 0.0.1 WhatsAppTb : unknown EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "5dd3d22", "date": "2025-05-14 00:35:17 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 MinimalHtml : 0.0.2 talk : 0.0.1 setup : 0.0.3 DoNext : 0.1.21 FileWidget : 1.0.0 ProcessManager : 0.0.1 TruthSeeker : unknown welcome : 0.1.21 WhatsAppTb : unknown SocketManager : 0.1.9 TestWidget : 0.0.1 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "defe200", "date": "2025-05-13 23:55:02 +0200", "message": " Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 DoNext : 0.1.21 CodeVerification : 0.0.1 setup : 0.0.3 TruthSeeker : unknown FileWidget : 1.0.0 MinimalHtml : 0.0.2 welcome : 0.1.21 talk : 0.0.1 WhatsAppTb : unknown ProcessManager : 0.0.1 TestWidget : 0.0.1 SocketManager : 0.1.9 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "0b7204f", "date": "2025-05-13 23:26:51 +0200", "message": "isaa import fixes Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 setup : 0.0.3 MinimalHtml : 0.0.2 FileWidget : 1.0.0 ProcessManager : 0.0.1 talk : 0.0.1 TruthSeeker : unknown welcome : 0.1.21 WhatsAppTb : unknown TestWidget : 0.0.1 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "1ace1ce", "date": "2025-05-13 23:08:29 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DoNext : 0.1.21 DashProvider : 0.0.1 ProcessManager : 0.0.1 talk : 0.0.1 setup : 0.0.3 MinimalHtml : 0.0.2 welcome : 0.1.21 TestWidget : 0.0.1 FileWidget : 1.0.0 WhatsAppTb : unknown SocketManager : 0.1.9 TruthSeeker : unknown EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1"}, {"sha": "a8288f0", "date": "2025-05-13 22:57:13 +0200", "message": "liter update Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DoNext : 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 ProcessManager : 0.0.1 talk : 0.0.1 MinimalHtml : 0.0.2 setup : 0.0.3 TruthSeeker : unknown TestWidget : 0.0.1 welcome : 0.1.21 WhatsAppTb : unknown SocketManager : 0.1.9 FileWidget : 1.0.0 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1"}, {"sha": "0889527", "date": "2025-05-13 22:44:32 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DoNext : 0.1.21 CodeVerification : 0.0.1 ProcessManager : 0.0.1 setup : 0.0.3 DashProvider : 0.0.1 MinimalHtml : 0.0.2 welcome : 0.1.21 FileWidget : 1.0.0 talk : 0.0.1 TestWidget : 0.0.1 WhatsAppTb : unknown SocketManager : 0.1.9 TruthSeeker : unknown EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "e003d75", "date": "2025-05-13 22:37:44 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 DoNext : 0.1.21 welcome : 0.1.21 setup : 0.0.3 ProcessManager : 0.0.1 MinimalHtml : 0.0.2 TruthSeeker : unknown talk : 0.0.1 FileWidget : 1.0.0 WhatsAppTb : unknown TestWidget : 0.0.1 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "825f306", "date": "2025-05-13 22:34:58 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 DoNext : 0.1.21 CodeVerification : 0.0.1 ProcessManager : 0.0.1 MinimalHtml : 0.0.2 talk : 0.0.1 welcome : 0.1.21 setup : 0.0.3 TruthSeeker : unknown WhatsAppTb : unknown FileWidget : 1.0.0 TestWidget : 0.0.1 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "e82d3b0", "date": "2025-05-13 22:30:12 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 CodeVerification : 0.0.1 DoNext : 0.1.21 MinimalHtml : 0.0.2 FileWidget : 1.0.0 talk : 0.0.1 ProcessManager : 0.0.1 setup : 0.0.3 TruthSeeker : unknown welcome : 0.1.21 SocketManager : 0.1.9 TestWidget : 0.0.1 WhatsAppTb : unknown EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "8a03d24", "date": "2025-05-13 21:50:39 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 DoNext : 0.1.21 CodeVerification : 0.0.1 ProcessManager : 0.0.1 FileWidget : 1.0.0 talk : 0.0.1 MinimalHtml : 0.0.2 SocketManager : 0.1.9 setup : 0.0.3 TruthSeeker : unknown WhatsAppTb : unknown TestWidget : 0.0.1 welcome : 0.1.21 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "cc0fc63", "date": "2025-05-13 21:41:47 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 DoNext : 0.1.21 ProcessManager : 0.0.1 TruthSeeker : unknown welcome : 0.1.21 talk : 0.0.1 setup : 0.0.3 MinimalHtml : 0.0.2 FileWidget : 1.0.0 WhatsAppTb : unknown SocketManager : 0.1.9 TestWidget : 0.0.1 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "5e2d383", "date": "2025-05-13 21:27:40 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DoNext : 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 talk : 0.0.1 ProcessManager : 0.0.1 setup : 0.0.3 MinimalHtml : 0.0.2 FileWidget : 1.0.0 WhatsAppTb : unknown welcome : 0.1.21 TestWidget : 0.0.1 TruthSeeker : unknown SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "96ac8e1", "date": "2025-05-13 21:07:30 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 MinimalHtml : 0.0.2 DoNext : 0.1.21 TruthSeeker : unknown welcome : 0.1.21 ProcessManager : 0.0.1 setup : 0.0.3 FileWidget : 1.0.0 talk : 0.0.1 TestWidget : 0.0.1 WhatsAppTb : unknown SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "8299351", "date": "2025-05-13 20:35:11 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 setup : 0.0.3 ProcessManager : 0.0.1 DoNext : 0.1.21 talk : 0.0.1 FileWidget : 1.0.0 welcome : 0.1.21 TruthSeeker : unknown MinimalHtml : 0.0.2 WhatsAppTb : unknown TestWidget : 0.0.1 EventManager : 0.0.3 SocketManager : 0.1.9 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "a6dde05", "date": "2025-05-13 20:32:08 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 ProcessManager : 0.0.1 DoNext : 0.1.21 setup : 0.0.3 welcome : 0.1.21 FileWidget : 1.0.0 WhatsAppTb : unknown MinimalHtml : 0.0.2 talk : 0.0.1 TruthSeeker : unknown TestWidget : 0.0.1 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "120d531", "date": "2025-05-13 20:18:35 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DashProvider : 0.0.1 DoNext : 0.1.21 CodeVerification : 0.0.1 ProcessManager : 0.0.1 FileWidget : 1.0.0 setup : 0.0.3 MinimalHtml : 0.0.2 talk : 0.0.1 WhatsAppTb : unknown TruthSeeker : unknown TestWidget : 0.0.1 welcome : 0.1.21 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "6f6e8af", "date": "2025-05-13 20:11:44 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DoNext : 0.1.21 DashProvider : 0.0.1 setup : 0.0.3 FileWidget : 1.0.0 talk : 0.0.1 welcome : 0.1.21 ProcessManager : 0.0.1 MinimalHtml : 0.0.2 TruthSeeker : unknown TestWidget : 0.0.1 WhatsAppTb : unknown SocketManager : 0.1.9 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "0283ec5", "date": "2025-05-13 20:06:07 +0200", "message": "Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 FileWidget : 1.0.0 MinimalHtml : 0.0.2 DoNext : 0.1.21 WhatsAppTb : unknown TruthSeeker : unknown welcome : 0.1.21 talk : 0.0.1 ProcessManager : 0.0.1 setup : 0.0.3 TestWidget : 0.0.1 SocketManager : 0.1.9 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "9bd5601", "date": "2025-05-13 19:54:35 +0200", "message": "<PERSON><PERSON>=False, Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 DoNext : 0.1.21 setup : 0.0.3 talk : 0.0.1 FileWidget : 1.0.0 TestWidget : 0.0.1 TruthSeeker : unknown ProcessManager : 0.0.1 MinimalHtml : 0.0.2 WhatsAppTb : unknown SocketManager : 0.1.9 welcome : 0.1.21 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "38a313f", "date": "2025-05-13 19:53:51 +0200", "message": "<PERSON><PERSON>=False, Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 DoNext : 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 MinimalHtml : 0.0.2 welcome : 0.1.21 ProcessManager : 0.0.1 setup : 0.0.3 talk : 0.0.1 TruthSeeker : unknown FileWidget : 1.0.0 SocketManager : 0.1.9 WhatsAppTb : unknown TestWidget : 0.0.1 email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "a410444", "date": "2025-05-13 19:11:50 +0200", "message": "<PERSON><PERSON>=False, Ruff❌, Safety✅ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DashProvider : 0.0.1 DoNext : 0.1.21 ProcessManager : 0.0.1 talk : 0.0.1 setup : 0.0.3 SocketManager : 0.1.9 MinimalHtml : 0.0.2 FileWidget : 1.0.0 TruthSeeker : unknown welcome : 0.1.21 TestWidget : 0.0.1 WhatsAppTb : unknown email_waiting_list : 0.1.21 EventManager : 0.0.3 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM : 0.0.3 CloudM.UserInstances : 0.0.2 CloudM.UI.widget : 0.0.1 WidgetsProvider : 0.0.1 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "5a7bca7", "date": "2025-05-13 16:43:42 +0200", "message": "ruff checks, <PERSON><PERSON>=<PERSON>alse, Ruff❌, Safety❌ Version Info: RESimpleToolBox: 0.1.21 CodeVerification : 0.0.1 DoNext : 0.1.21 DashProvider : 0.0.1 talk : 0.0.1 MinimalHtml : 0.0.2 FileWidget : 1.0.0 setup : 0.0.3 WhatsAppTb : unknown ProcessManager : 0.0.1 TestWidget : 0.0.1 TruthSeeker : unknown welcome : 0.1.21 SocketManager : 0.1.9 EventManager : 0.0.3 email_waiting_list : 0.1.21 SchedulerManager : 0.0.2 WebSocketManager : 0.0.3 DB : 0.0.3 cli_functions : 0.0.1 FastApi : 0.2.2 CloudM.AuthManager : 0.1.21 CloudM.UI.widget : 0.0.1 CloudM.UserInstances : 0.0.2 WidgetsProvider : 0.0.1 CloudM : 0.0.3 WidgetsProvider.BoardWidget : 0.0.1 isaa : 0.1.5"}, {"sha": "f0001f4", "date": "2025-05-13 16:35:57 +0200", "message": "ruff checks, <PERSON><PERSON>=False, Ruff❌, Safety❌"}, {"sha": "981137e", "date": "2025-05-13 16:34:49 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "135de0b", "date": "2025-05-13 16:27:35 +0200", "message": "ruff checks, <PERSON><PERSON>=False, Ruff❌, Safety❌"}, {"sha": "3de0240", "date": "2025-05-13 16:25:56 +0200", "message": "ruff checks, <PERSON><PERSON>=<PERSON><PERSON><PERSON>, <PERSON><PERSON> \\<#> Security❌, Safety❌"}, {"sha": "f4e8436", "date": "2025-05-13 16:20:21 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "852e5db", "date": "2025-05-13 16:16:05 +0200", "message": "ruff checks, <PERSON><PERSON>=False, <#>"}, {"sha": "abf277e", "date": "2025-05-13 16:11:16 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "7eaaf9a", "date": "2025-05-13 16:09:39 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "1b189ca", "date": "2025-05-13 16:05:03 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "5281c88", "date": "2025-05-13 15:55:36 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "966e414", "date": "2025-05-13 15:54:38 +0200", "message": "ruff checks, <PERSON><PERSON>=False,"}, {"sha": "199cae6", "date": "2025-05-13 14:37:04 +0200", "message": "ruff checks, <PERSON><PERSON>=False"}, {"sha": "f3c67ed", "date": "2025-05-13 03:10:39 +0200", "message": "release redy, <PERSON><PERSON>=False"}, {"sha": "05ad284", "date": "2025-05-13 02:44:22 +0200", "message": "release redy"}, {"sha": "608ae02", "date": "2025-05-13 02:38:26 +0200", "message": "release redy"}, {"sha": "f7aab9b", "date": "2025-05-13 02:31:44 +0200", "message": "release redy"}, {"sha": "3c52009", "date": "2025-05-13 02:31:28 +0200", "message": "release redy"}, {"sha": "43e4dd8", "date": "2025-05-13 02:21:52 +0200", "message": "release redy"}, {"sha": "8100fd0", "date": "2025-05-13 02:18:03 +0200", "message": "release redy"}, {"sha": "5371265", "date": "2025-05-13 02:13:33 +0200", "message": "release redy"}, {"sha": "335e6a3", "date": "2025-05-13 02:07:42 +0200", "message": "release redy"}, {"sha": "2496891", "date": "2025-05-13 02:02:48 +0200", "message": "release redy"}, {"sha": "e38b424", "date": "2025-05-13 01:50:34 +0200", "message": "release redy"}, {"sha": "820d117", "date": "2025-05-13 01:39:21 +0200", "message": "release redy"}, {"sha": "089da63", "date": "2025-05-13 01:27:39 +0200", "message": "release redy"}, {"sha": "82c4206", "date": "2025-05-13 01:26:11 +0200", "message": "release redy"}, {"sha": "b8801db", "date": "2025-05-13 01:22:30 +0200", "message": "release redy"}, {"sha": "8d0ca66", "date": "2025-05-13 01:20:10 +0200", "message": "release redy"}, {"sha": "404b19c", "date": "2025-05-13 01:12:40 +0200", "message": "release redy"}, {"sha": "b1d3932", "date": "2025-05-13 01:09:31 +0200", "message": "release redy"}, {"sha": "ff29d41", "date": "2025-05-13 01:07:33 +0200", "message": "release redy"}, {"sha": "461ad51", "date": "2025-05-13 00:54:15 +0200", "message": "releas redy"}, {"sha": "6973f02", "date": "2025-05-13 00:19:23 +0200", "message": "releas redy"}, {"sha": "e36f462", "date": "2025-05-07 17:01:56 +0200", "message": "fix tests"}, {"sha": "ca32a86", "date": "2025-05-07 16:28:06 +0200", "message": "fix tests"}, {"sha": "c83463a", "date": "2025-05-07 16:26:17 +0200", "message": "fix tests"}, {"sha": "1f942d2", "date": "2025-05-07 16:24:27 +0200", "message": "fix tests"}, {"sha": "2ed5a5f", "date": "2025-05-07 16:23:08 +0200", "message": "fix tests"}, {"sha": "c7c482e", "date": "2025-05-07 16:10:30 +0200", "message": "fix tests"}, {"sha": "b4fadd9", "date": "2025-05-07 16:02:03 +0200", "message": "fix tests"}, {"sha": "ebe2139", "date": "2025-05-07 15:44:51 +0200", "message": "fix tests"}, {"sha": "5b51a7a", "date": "2025-05-07 14:33:17 +0200", "message": "user experience update"}, {"sha": "eb69b37", "date": "2025-05-07 01:15:49 +0200", "message": "user experience update"}, {"sha": "5f8987a", "date": "2025-05-07 01:09:44 +0200", "message": "user experience update"}, {"sha": "0ea82c7", "date": "2025-05-07 01:07:10 +0200", "message": "user experience update"}, {"sha": "9dae26f", "date": "2025-05-07 01:01:54 +0200", "message": "user experience update"}, {"sha": "c8a6c9f", "date": "2025-05-07 01:00:54 +0200", "message": "user experience update"}, {"sha": "e6970fd", "date": "2025-05-07 00:56:53 +0200", "message": "user experience update"}, {"sha": "7dd0514", "date": "2025-05-07 00:54:52 +0200", "message": "user experience update"}, {"sha": "c523453", "date": "2025-05-07 00:52:18 +0200", "message": "user experience update"}, {"sha": "83d1373", "date": "2025-05-07 00:48:41 +0200", "message": "user experience update"}, {"sha": "aed6395", "date": "2025-05-07 00:47:01 +0200", "message": "user experience update"}, {"sha": "119ab15", "date": "2025-05-07 00:46:45 +0200", "message": "user experience update"}, {"sha": "ea4a114", "date": "2025-05-07 00:44:33 +0200", "message": "user experience update"}, {"sha": "ccfeb4c", "date": "2025-05-07 00:40:33 +0200", "message": "user experience update"}, {"sha": "2b01f25", "date": "2025-05-07 00:36:36 +0200", "message": "user experience update"}, {"sha": "72e7144", "date": "2025-05-07 00:32:34 +0200", "message": "user experience update"}, {"sha": "e2f7b6e", "date": "2025-05-07 00:28:12 +0200", "message": "user experience update"}, {"sha": "d703134", "date": "2025-05-07 00:25:22 +0200", "message": "user experience update"}, {"sha": "ae38183", "date": "2025-05-07 00:24:14 +0200", "message": "user experience update"}, {"sha": "3ed8ac3", "date": "2025-05-06 23:37:03 +0200", "message": "user experience update"}, {"sha": "4c9635f", "date": "2025-05-06 23:32:14 +0200", "message": "user experience update"}, {"sha": "9314b8f", "date": "2025-05-06 23:28:25 +0200", "message": "user experience update"}, {"sha": "2f8a4c5", "date": "2025-05-06 23:21:19 +0200", "message": "user experience update"}, {"sha": "d5eafc1", "date": "2025-05-06 23:19:46 +0200", "message": "user experience update"}, {"sha": "bc5542b", "date": "2025-05-06 23:01:00 +0200", "message": "user experience update"}, {"sha": "0e0ae83", "date": "2025-05-06 22:59:23 +0200", "message": "user experience update"}, {"sha": "01aebfd", "date": "2025-05-06 22:55:08 +0200", "message": "user experience update"}, {"sha": "73431b8", "date": "2025-05-06 22:52:56 +0200", "message": "user experience update"}, {"sha": "6583cc0", "date": "2025-05-06 22:44:18 +0200", "message": "user experience update"}, {"sha": "d933441", "date": "2025-05-06 22:38:59 +0200", "message": "user experience update"}, {"sha": "ecc0287", "date": "2025-05-06 22:38:19 +0200", "message": "user experience update"}, {"sha": "ed0c9d9", "date": "2025-05-06 22:35:17 +0200", "message": "user experience update"}, {"sha": "a50781c", "date": "2025-05-06 22:20:34 +0200", "message": "user experience update"}, {"sha": "b0c60ec", "date": "2025-05-06 22:17:41 +0200", "message": "user experience update"}, {"sha": "293de5c", "date": "2025-05-06 22:07:08 +0200", "message": "user experience update"}, {"sha": "74bf5f1", "date": "2025-05-06 21:52:54 +0200", "message": "user experience update"}, {"sha": "381fd86", "date": "2025-05-06 21:16:08 +0200", "message": "pre version 0.1.21 push"}, {"sha": "43548f7", "date": "2025-05-06 20:12:51 +0200", "message": "tauri config update"}, {"sha": "d22e681", "date": "2025-05-06 20:04:43 +0200", "message": "tauri config update"}, {"sha": "4540490", "date": "2025-05-06 19:59:04 +0200", "message": "Merge remote-tracking branch 'origin/master'"}, {"sha": "4f9d77d", "date": "2025-05-06 19:58:15 +0200", "message": "updateds semi stabel"}, {"sha": "78f5e1f", "date": "2025-04-04 19:56:52 +0200", "message": "fixes laptop"}, {"sha": "76f0f45", "date": "2025-04-04 18:58:29 +0200", "message": "updated jaks"}, {"sha": "09b53cd", "date": "2025-04-04 01:28:46 +0200", "message": "optional big tb update"}, {"sha": "b42d528", "date": "2025-03-27 01:34:06 +0100", "message": "optional import taichi as ti"}, {"sha": "2ff67ba", "date": "2025-03-10 13:28:48 +0100", "message": "optional import taichi as ti"}, {"sha": "7c8a07f", "date": "2025-03-01 03:42:30 +0100", "message": "optional import taichi as ti"}, {"sha": "da62855", "date": "2025-02-28 01:38:04 +0100", "message": "optional import taichi as ti"}, {"sha": "bd0ce0d", "date": "2025-02-28 01:20:46 +0100", "message": "optional import taichi as ti"}, {"sha": "cc1f61b", "date": "2025-02-27 19:56:41 +0100", "message": "optional import taichi as ti"}, {"sha": "b32486c", "date": "2025-02-27 04:21:54 +0100", "message": "optional import taichi as ti"}, {"sha": "11f2aaf", "date": "2025-02-27 04:06:33 +0100", "message": "optional import taichi as ti"}, {"sha": "60ad170", "date": "2025-02-25 00:03:09 +0100", "message": "optional import taichi as ti"}, {"sha": "814c11c", "date": "2025-02-17 04:50:52 +0100", "message": "optional import taichi as ti"}, {"sha": "e84f50a", "date": "2025-02-16 03:26:37 +0100", "message": "optional import taichi as ti"}, {"sha": "2671d3c", "date": "2025-02-15 04:28:57 +0100", "message": "optional import taichi as ti"}, {"sha": "551a67c", "date": "2025-02-14 14:43:52 +0100", "message": "optional import taichi as ti"}, {"sha": "f3bb5eb", "date": "2025-02-14 01:59:50 +0100", "message": "optional import taichi as ti"}, {"sha": "d58d070", "date": "2025-02-13 15:57:49 +0100", "message": "fixes laptop"}, {"sha": "e7c8b97", "date": "2025-02-13 01:29:33 +0100", "message": "optional import taichi as ti"}, {"sha": "25a825b", "date": "2025-02-12 02:56:15 +0000", "message": "server fixes"}, {"sha": "958ac9d", "date": "2025-02-12 02:06:19 +0000", "message": "server fixes"}, {"sha": "7868728", "date": "2025-02-12 01:46:59 +0000", "message": "server fixes"}, {"sha": "f053251", "date": "2025-02-12 02:35:24 +0100", "message": "optional import taichi as ti"}, {"sha": "7a1f784", "date": "2025-02-12 02:20:29 +0100", "message": "optional import taichi as ti"}, {"sha": "1cf2765", "date": "2025-02-12 01:17:08 +0000", "message": "server fixes"}, {"sha": "af49ad1", "date": "2025-02-12 01:07:27 +0000", "message": "server fixes"}, {"sha": "dc2776d", "date": "2025-02-12 01:03:57 +0000", "message": "server fixes"}, {"sha": "d493a7c", "date": "2025-02-12 01:00:05 +0000", "message": "server fixes"}, {"sha": "dc2d5d3", "date": "2025-02-12 00:55:07 +0000", "message": "server fixes"}, {"sha": "20728f8", "date": "2025-02-12 00:49:12 +0000", "message": "server fixes"}, {"sha": "100cb3e", "date": "2025-02-12 01:23:09 +0100", "message": "optional import taichi as ti"}, {"sha": "cfbeed7", "date": "2025-02-12 01:21:05 +0100", "message": "optional import taichi as ti"}, {"sha": "085110f", "date": "2025-02-12 01:08:49 +0100", "message": "pc"}, {"sha": "3774c47", "date": "2025-02-12 00:57:06 +0100", "message": "pc"}, {"sha": "528c636", "date": "2025-02-10 20:47:09 +0000", "message": "server fixes"}, {"sha": "49dec1d", "date": "2025-02-10 21:02:26 +0100", "message": "pc"}, {"sha": "5dd82a4", "date": "2025-02-10 19:03:29 +0100", "message": "pc"}, {"sha": "cb16579", "date": "2025-02-10 06:25:59 +0100", "message": "pc"}, {"sha": "378505b", "date": "2025-02-09 20:39:45 +0100", "message": "pc"}, {"sha": "052061d", "date": "2025-02-06 19:16:05 +0000", "message": "server fixes"}, {"sha": "ba28dc8", "date": "2025-02-06 19:15:14 +0000", "message": "server fixes"}, {"sha": "328e247", "date": "2025-02-06 20:09:40 +0100", "message": "cages"}, {"sha": "00716e9", "date": "2025-02-06 19:53:14 +0100", "message": "cages"}, {"sha": "8203dd2", "date": "2025-02-06 19:48:52 +0100", "message": "cages"}, {"sha": "49ddad6", "date": "2025-02-06 19:43:23 +0100", "message": "cages"}, {"sha": "5f7d27a", "date": "2025-02-06 19:41:56 +0100", "message": "cages"}, {"sha": "ffefc59", "date": "2025-02-04 08:02:59 +0100", "message": "cages"}, {"sha": "2752083", "date": "2025-02-03 19:34:39 +0000", "message": "server fixes"}, {"sha": "701fa39", "date": "2025-02-01 15:39:24 +0000", "message": "server fixes"}, {"sha": "dca4b16", "date": "2025-01-30 18:49:09 +0000", "message": "server fixes"}, {"sha": "6be7013", "date": "2025-01-30 11:48:11 +0000", "message": "server fixes"}, {"sha": "cb65f0a", "date": "2025-01-22 22:19:51 +0100", "message": "cages"}, {"sha": "b2a990a", "date": "2025-01-22 21:18:58 +0000", "message": "remoute updates"}, {"sha": "0d7e6e0", "date": "2025-01-22 20:26:38 +0100", "message": "cages"}, {"sha": "d0ffac6", "date": "2025-01-22 13:38:12 +0000", "message": "remoute updates"}, {"sha": "44c7ffc", "date": "2025-01-21 23:17:03 +0100", "message": "cages"}, {"sha": "d700de1", "date": "2025-01-21 22:41:17 +0100", "message": "cages"}, {"sha": "1bc8b21", "date": "2025-01-21 17:13:55 +0100", "message": "cages"}, {"sha": "374f470", "date": "2025-01-21 17:12:28 +0100", "message": "cages"}, {"sha": "2238245", "date": "2025-01-19 00:47:31 +0100", "message": "updasde"}, {"sha": "0ef7d38", "date": "2025-01-18 01:27:49 +0100", "message": "updasde"}, {"sha": "df09300", "date": "2025-01-13 15:45:29 +0100", "message": "fixes laptop"}, {"sha": "96eb14d", "date": "2025-01-13 15:07:28 +0100", "message": "fixes laptop"}, {"sha": "50445b9", "date": "2025-01-13 11:55:29 +0100", "message": "updasde"}, {"sha": "968be95", "date": "2025-01-10 13:46:34 +0100", "message": "updasde"}, {"sha": "5ffc8ec", "date": "2025-01-09 19:36:21 +0100", "message": "fixes laptop"}, {"sha": "bcc68bc", "date": "2025-01-07 09:59:07 +0100", "message": "Sonar + Irings upadte"}, {"sha": "67046ef", "date": "2025-01-01 17:49:17 +0100", "message": "Sonar + Irings upadte"}, {"sha": "67be5d9", "date": "2025-01-01 17:40:28 +0100", "message": "greedy test install playwright"}, {"sha": "d940d3e", "date": "2024-12-27 17:12:31 +0100", "message": "greedy test install playwright"}, {"sha": "7fb696b", "date": "2024-12-27 16:52:34 +0100", "message": "fix CodeVerification"}, {"sha": "4a8b4d2", "date": "2024-12-20 14:47:47 +0100", "message": "fixes laptop"}, {"sha": "7ce7603", "date": "2024-12-19 20:14:16 +0100", "message": "fixes laptop"}, {"sha": "72ecc40", "date": "2024-12-15 18:00:10 +0100", "message": "Update cosseana_core.py"}, {"sha": "fc6782f", "date": "2024-12-13 19:02:03 +0100", "message": "Update cosseana_core.py"}, {"sha": "bb1a6c7", "date": "2024-12-13 16:08:24 +0100", "message": "minorfixes"}, {"sha": "15b7f64", "date": "2024-12-13 16:01:55 +0100", "message": "minorfixes"}, {"sha": "2017e40", "date": "2024-12-13 15:45:29 +0100", "message": "minorfixes"}, {"sha": "8b6068f", "date": "2024-12-13 11:29:31 +0100", "message": "Improved tests add req builder"}, {"sha": "7fc8e3c", "date": "2024-12-13 11:17:49 +0100", "message": "Improved tests"}, {"sha": "bf4e538", "date": "2024-12-13 11:12:58 +0100", "message": "Improved tests"}, {"sha": "062b3a1", "date": "2024-12-13 11:03:36 +0100", "message": "Improved tests"}, {"sha": "8e5a306", "date": "2024-12-06 11:21:12 +0100", "message": "test next system and 2 extras"}, {"sha": "9c9b254", "date": "2024-12-02 23:14:41 +0100", "message": "test next system and 2 extras"}, {"sha": "79fd988", "date": "2024-12-01 23:59:13 +0100", "message": "v nexxt flows2"}, {"sha": "64f6f3b", "date": "2024-11-29 22:21:53 +0100", "message": "penis"}, {"sha": "4f323b7", "date": "2024-11-29 12:06:13 +0100", "message": "CLEEN up"}, {"sha": "8a06897", "date": "2024-11-27 16:05:28 +0100", "message": "bak to base"}, {"sha": "bf933db", "date": "2024-11-27 15:51:30 +0100", "message": "Merge branch 'master' of https://github.com/MarkinHaus/ToolBoxV2"}, {"sha": "e12ef6d", "date": "2024-11-27 15:34:35 +0100", "message": "dinds"}, {"sha": "056fd5c", "date": "2024-11-27 14:01:55 +0100", "message": "v nexxt flows23"}, {"sha": "1802486", "date": "2024-11-26 23:23:42 +0100", "message": "v nexxt flows2"}, {"sha": "87525df", "date": "2024-11-26 00:33:47 +0100", "message": "Aktualisieren von requirements.txt"}, {"sha": "ac036e7", "date": "2024-11-26 00:09:59 +0100", "message": "v nexxt flows"}, {"sha": "25023cc", "date": "2024-11-24 18:33:41 +0100", "message": "## Modified Features and Fixes"}, {"sha": "500f57d", "date": "2024-11-22 03:16:50 +0100", "message": "cossena update"}, {"sha": "5922d63", "date": "2024-11-19 17:58:55 +0100", "message": "new changes are based on what a new diff displayed that implies changes in the `toolboxv2/__gui__.py` file. I'll walk you through the additions, modifications, and removals based on the provided snippet."}, {"sha": "8e9ca3c", "date": "2024-11-19 01:34:15 +0100", "message": "Based on the given code changes, here are the new features and fixes added:"}, {"sha": "d1bf516", "date": "2024-11-18 09:52:23 +0100", "message": "update files"}, {"sha": "ba3a4a7", "date": "2024-11-14 01:12:02 +0100", "message": "update files"}, {"sha": "8d95822", "date": "2024-11-14 00:43:50 +0100", "message": "update files"}, {"sha": "6419872", "date": "2024-11-14 00:30:03 +0100", "message": "update files"}, {"sha": "fbdd11b", "date": "2024-11-14 00:01:28 +0100", "message": "update files"}, {"sha": "59c5864", "date": "2024-11-13 23:57:54 +0100", "message": "update files"}, {"sha": "186d73f", "date": "2024-11-13 23:51:24 +0100", "message": "dings"}, {"sha": "b548ef3", "date": "2024-11-13 20:24:41 +0100", "message": "dings"}, {"sha": "acffc65", "date": "2024-11-13 20:20:43 +0100", "message": "dings"}, {"sha": "a81911d", "date": "2024-11-13 20:20:28 +0100", "message": "dings"}, {"sha": "4e8b883", "date": "2024-11-13 20:18:52 +0100", "message": "dings"}, {"sha": "0afee22", "date": "2024-11-13 20:14:28 +0100", "message": "Merge remote-tracking branch 'origin/master'"}, {"sha": "4cb801d", "date": "2024-11-13 20:13:50 +0100", "message": "dings"}, {"sha": "1600c9b", "date": "2024-11-13 17:22:15 +0100", "message": "Aktualisiere<PERSON> von __main__.py"}, {"sha": "9645d6d", "date": "2024-11-13 16:29:03 +0100", "message": "Aktualisiere<PERSON> von __main__.py"}, {"sha": "038f334", "date": "2024-11-13 14:50:47 +0100", "message": "dings"}, {"sha": "a36a7d7", "date": "2024-11-12 18:50:42 +0100", "message": "dings"}, {"sha": "76b4ee9", "date": "2024-11-12 13:27:55 +0100", "message": "server test"}, {"sha": "3092538", "date": "2024-11-12 13:15:11 +0100", "message": "addit compact mods sto"}, {"sha": "246e757", "date": "2024-11-12 09:49:57 +0100", "message": "dings"}, {"sha": "63eb13b", "date": "2024-11-09 22:34:46 +0100", "message": "### Code Review and Updates"}, {"sha": "6b937b0", "date": "2024-11-08 11:32:06 +0100", "message": "dings"}, {"sha": "77d9250", "date": "2024-11-08 01:47:08 +0100", "message": "**Changes to Toolbox V2:**"}, {"sha": "45427f7", "date": "2024-11-06 00:31:47 +0100", "message": "Refactor DoNext.py: fix template and add JS styles"}, {"sha": "7281eb7", "date": "2024-11-05 17:52:05 +0100", "message": "Refactor DoNext.py: fix template and add JS styles"}, {"sha": "1e9fc65", "date": "2024-11-05 01:20:11 +0100", "message": "**Applied Changes and Key Points** The following key points were addressed in these changes:"}, {"sha": "db2435e", "date": "2024-11-01 20:27:25 +0100", "message": "lost"}, {"sha": "2e365b1", "date": "2024-11-01 20:19:31 +0100", "message": "It appears to be a diff showing changes made to a codebase, specifically in relation to a project named \"simplecore.\" The changes seem to involve updating Dockerfile, toolboxv2, and email_waiting_list.py files. However, without more context and the actual file contents before and after the changes, it's difficult to provide a detailed analysis."}, {"sha": "45a608b", "date": "2024-11-01 19:35:16 +0100", "message": "It appears to be a diff showing changes made to a codebase, specifically in relation to a project named \"simplecore.\" The changes seem to involve updating Dockerfile, toolboxv2, and email_waiting_list.py files. However, without more context and the actual file contents before and after the changes, it's difficult to provide a detailed analysis."}, {"sha": "09da79f", "date": "2024-10-22 20:09:37 +0200", "message": "There are several changes made to the code. Here are some key observations and explanations:"}, {"sha": "81a7dec", "date": "2024-10-17 12:40:18 +0200", "message": "Bumped package versions to v0.1.20"}, {"sha": "d616bbc", "date": "2024-10-17 12:25:03 +0200", "message": "Bumped package versions to v0.1.20"}, {"sha": "1c39b4b", "date": "2024-10-16 19:10:17 +0200", "message": "v0.1.19b changes Refactoring and minor optimization in CLI functions module"}, {"sha": "e4da968", "date": "2024-10-16 10:56:42 +0200", "message": "v0.1.19b changes blob storage test fix"}, {"sha": "b68a9f2", "date": "2024-10-14 15:03:14 +0200", "message": "v0.1.19b changes blob storage test fix"}, {"sha": "1d7fbb6", "date": "2024-10-14 09:03:19 +0200", "message": "v0.1.19b changes"}, {"sha": "2bb3345", "date": "2024-09-27 01:01:46 +0200", "message": "v0.1.19 conda + isaa setup helper + fixes"}, {"sha": "245578e", "date": "2024-09-26 15:24:31 +0200", "message": "v0.1.19 live talk fix"}, {"sha": "f3cf1ac", "date": "2024-09-26 15:18:55 +0200", "message": "v0.1.19 live GistLoader"}, {"sha": "8388265", "date": "2024-09-26 15:09:56 +0200", "message": "v0.1.19"}, {"sha": "f117e9b", "date": "2024-09-18 20:22:50 +0200", "message": "added local exter runnabelss files sep by ,"}, {"sha": "b0508fe", "date": "2024-09-14 17:34:48 +0200", "message": "added romote mini cli"}, {"sha": "61faa5e", "date": "2024-09-14 17:33:20 +0200", "message": "1. In `file_handler.py`, the function `get_file_handler` has been modified to accept an additional parameter `default` (line 165 vs line 185). This parameter is used to return a default value if the function cannot find the file. 2. In `state_system.py`, a new import statement has been added at line 24 to import the `Spinner` class from the `extras.Style` module (line 76 vs line 138). This class is used to display a spinner while processing files. 3. In `toolbox.py`, a new if statement has been added at line 180 to check if the `user_name` variable is None before setting it to an empty string (line 181 vs line 180). 4. In `web/assets/styles.css`, there are some small changes in the CSS code, including adding a new line break at lines 356 and 467 to improve readability (lines 356-357 vs lines 466-467)."}, {"sha": "ba795e1", "date": "2024-09-02 00:28:56 +0200", "message": "* Changes: \t+ The `talk.py` file has been updated with a new try-except block to handle exceptions more gracefully. * New features: \t+ The `state_system.py` file now includes a `total` parameter in the `tqdm` call to display the total number of chunks being processed. \t+ The `upload_audio_isaa` function in the `toolboxv2/mods/talk.py` file has been updated to handle transcription updates more efficiently by using a loop counter (`max_itter`) to limit the number of iterations."}, {"sha": "62fdc1c", "date": "2024-09-01 19:13:02 +0200", "message": "added talk mod to base"}, {"sha": "29e9849", "date": "2024-09-01 19:08:15 +0200", "message": "fixes"}, {"sha": "0f9948b", "date": "2024-09-01 17:36:35 +0200", "message": "for test"}, {"sha": "034d977", "date": "2024-09-01 17:25:20 +0200", "message": "The differences between the two versions of `toolbox.py` are:"}, {"sha": "8e802ee", "date": "2024-09-01 16:04:22 +0200", "message": "Changes and New Features:"}, {"sha": "ae43b4f", "date": "2024-09-01 14:47:39 +0200", "message": "**Changes:**"}, {"sha": "924903a", "date": "2024-09-01 14:03:17 +0200", "message": "Your changes and new features are:"}, {"sha": "717aba2", "date": "2024-09-01 01:24:14 +0200", "message": "Here are the changes that were made between the two versions of each file:"}, {"sha": "532c850", "date": "2024-08-31 23:48:32 +0200", "message": "Here is a brief summary of the code changes you provided:"}, {"sha": "6b36b29", "date": "2024-08-30 14:12:41 +0200", "message": "ich bin dumm ein bißchen mehr add custom source_id and dfault"}, {"sha": "13e7957", "date": "2024-08-30 14:06:01 +0200", "message": "ich bin dumm ein bißchen mehr add custom source_id and dfault"}, {"sha": "20bd05f", "date": "2024-08-30 13:57:02 +0200", "message": "ich bin dumm ein bißchen"}, {"sha": "d4e240d", "date": "2024-08-30 13:53:25 +0200", "message": "added init key connection pass"}, {"sha": "1935788", "date": "2024-08-30 13:43:54 +0200", "message": "fix P0|S0 server rout by removing it :)"}, {"sha": "82e79d8", "date": "2024-08-30 13:28:31 +0200", "message": "chat auto self host"}, {"sha": "6c09d73", "date": "2024-08-30 13:20:27 +0200", "message": "env-template Cm infint update loop fix better app names"}, {"sha": "614d435", "date": "2024-08-30 13:09:18 +0200", "message": "qick fixes ModManager.py \\\\ -> /"}, {"sha": "01ec41e", "date": "2024-08-30 12:53:47 +0200", "message": "qick fixes ModManager.py \\\\ -> /"}, {"sha": "e17d652", "date": "2024-08-30 12:38:39 +0200", "message": "tbef -> TBEF add chat fix eventManager fix SoketManager"}, {"sha": "4b9eda8", "date": "2024-08-24 03:50:37 +0200", "message": "Merge remote-tracking branch 'origin/master'"}, {"sha": "9857a83", "date": "2024-08-24 03:50:05 +0200", "message": "boosted"}, {"sha": "a9f1124", "date": "2024-07-22 15:26:37 +0200", "message": "fix a_save_closing_app"}, {"sha": "4ea9d8d", "date": "2024-07-22 14:39:06 +0200", "message": "fix a_save_closing_app"}, {"sha": "7ce4bfd", "date": "2024-07-22 14:35:43 +0200", "message": "otional dir reset on exit"}, {"sha": "68dfebb", "date": "2024-07-19 13:17:10 +0200", "message": "save conf"}, {"sha": "e800d76", "date": "2024-07-19 13:15:42 +0200", "message": "save"}, {"sha": "e76794a", "date": "2024-06-21 21:02:05 +0200", "message": "fixing  - experimatal"}, {"sha": "269b4c5", "date": "2024-06-21 20:49:12 +0200", "message": "fixing  - session enty pint to better timout not print"}, {"sha": "2569521", "date": "2024-06-21 20:42:46 +0200", "message": "fixing  - session enty pint to TOOLBOXV2_REMOTE_BASE  - auto run after update"}, {"sha": "0ee7698", "date": "2024-06-21 20:22:06 +0200", "message": "fixing  - online links web url internal  - added itsdangerous as req for api  - tbstry.py caceld <PERSON>ll"}, {"sha": "fe4dea7", "date": "2024-06-21 19:33:58 +0200", "message": "fixing  - update_core_git addete git stash routine if wanted for fixing an update error  - print welcome on new start"}, {"sha": "aa31e93", "date": "2024-06-21 19:28:50 +0200", "message": "fixing  - requirements.txt (ipy)  - Proxi calling with -fg (args parsing)  + added timeout default to 12s"}, {"sha": "5d8a13a", "date": "2024-06-21 15:30:27 +0200", "message": "ToolBoxV2 minor fixes"}, {"sha": "720d55e", "date": "2024-06-18 23:56:47 +0200", "message": "ToolBoxV2 fix mod manager"}, {"sha": "5091c6a", "date": "2024-06-18 23:32:37 +0200", "message": "ToolBoxV2 try migrate drop py 3.9 include 3.12"}, {"sha": "e73795f", "date": "2024-06-18 23:24:58 +0200", "message": "ToolBoxV2 slim req.txt"}, {"sha": "9979ac9", "date": "2024-06-18 22:31:05 +0200", "message": "ToolBoxV2 fix web base and Dashboards"}, {"sha": "380c9d4", "date": "2024-06-17 00:37:47 +0200", "message": "ToolBoxV2 fix pip requirements"}, {"sha": "253c765", "date": "2024-06-17 00:29:17 +0200", "message": "ToolBoxV2 ipy enum parser beta"}, {"sha": "f65a465", "date": "2024-06-17 00:27:14 +0200", "message": "ToolBoxV2 api fixes 2"}, {"sha": "b4f3d47", "date": "2024-06-14 12:57:16 +0200", "message": "ToolBoxV2 api fixes"}, {"sha": "e4074e5", "date": "2024-06-13 08:30:37 +0200", "message": "ToolBoxV2 ipython repel"}, {"sha": "78a4f1d", "date": "2024-06-11 09:44:05 +0200", "message": "ToolBoxV2 ipython repel"}, {"sha": "98291a4", "date": "2024-06-11 09:41:44 +0200", "message": "ToolBoxV2 ipython repel"}, {"sha": "14e3978", "date": "2024-06-11 01:29:00 +0200", "message": "ToolBoxV2 ipython repel"}, {"sha": "6abfdef", "date": "2024-06-11 01:01:37 +0200", "message": "ToolBoxV2 ipython repel"}, {"sha": "2fa6a23", "date": "2024-06-09 03:35:21 +0200", "message": "ToolBoxV2 installation fixes"}, {"sha": "cec0995", "date": "2024-06-09 02:53:13 +0200", "message": " 0.1.17 cm better updates"}, {"sha": "a84d4ec", "date": "2024-06-09 02:37:13 +0200", "message": " 0.1.17 fixes test"}, {"sha": "d05de86", "date": "2024-06-09 02:36:02 +0200", "message": " 0.1.17 fixes"}, {"sha": "87da4d0", "date": "2024-06-09 02:29:35 +0200", "message": " 0.1.17 live installer ..."}, {"sha": "1265dd3", "date": "2024-06-09 02:28:57 +0200", "message": " 0.1.17 live installer"}, {"sha": "c57b343", "date": "2024-06-09 02:27:36 +0200", "message": " 0.1.17 stable version"}, {"sha": "3d09467", "date": "2024-06-06 00:40:06 +0200", "message": " 0.1.16 live docker init fix"}, {"sha": "6ed8276", "date": "2024-06-04 09:16:31 +0200", "message": " 0.1.16 live web last fix"}, {"sha": "ae54671", "date": "2024-06-03 23:03:24 +0200", "message": " 0.1.16 live web last fix"}, {"sha": "9e1ab49", "date": "2024-06-02 20:29:55 +0200", "message": " 0.1.16 live web init fix"}, {"sha": "d0239f7", "date": "2024-06-02 16:00:35 +0200", "message": " 0.1.16 live web init fix"}, {"sha": "910c09e", "date": "2024-06-01 16:12:22 +0200", "message": " 0.1.16 live web init fix"}, {"sha": "c8c3776", "date": "2024-05-31 13:16:55 +0200", "message": " 0.1.16 live web"}, {"sha": "f551ca0", "date": "2024-05-29 20:48:06 +0200", "message": " 0.1.16"}, {"sha": "1925c9e", "date": "2024-05-21 02:20:40 +0200", "message": " 0.1.15b push save data"}, {"sha": "7e5149c", "date": "2024-05-18 17:24:28 +0200", "message": "last 0.1.15 push web updatet No system setting coming in 1.16"}, {"sha": "c790f22", "date": "2024-05-15 16:07:19 +0200", "message": "mod dependency"}, {"sha": "558f624", "date": "2024-05-15 15:51:21 +0200", "message": "0.1.15 fixing tests 5"}, {"sha": "4d018be", "date": "2024-05-15 15:42:35 +0200", "message": "0.1.15 fixing tests 4"}, {"sha": "3e73293", "date": "2024-05-15 15:28:04 +0200", "message": "0.1.15 fixing tests 3"}, {"sha": "8d3bc88", "date": "2024-05-15 15:09:19 +0200", "message": "0.1.15 fixing tests"}, {"sha": "86565cb", "date": "2024-05-15 14:55:47 +0200", "message": "0.1.15 fixing tests"}, {"sha": "ddbe4bb", "date": "2024-05-15 00:58:11 +0200", "message": "0.1.15 updating test for online use ..."}, {"sha": "23ca62d", "date": "2024-05-15 00:54:37 +0200", "message": "0.1.15 integration test online not possible"}, {"sha": "f7f174d", "date": "2024-05-15 00:51:06 +0200", "message": "0.1.15 addet utl test and extend mod test"}, {"sha": "815972d", "date": "2024-05-14 13:46:15 +0200", "message": "0.1.15 minor fixes"}, {"sha": "b9666e8", "date": "2024-05-12 23:11:18 +0200", "message": "0.1.15 stable tb"}, {"sha": "4cb5c1b", "date": "2024-05-07 17:43:43 +0200", "message": "mini stable tb"}, {"sha": "b4e5523", "date": "2024-05-05 09:54:01 +0200", "message": "ToolBoxV2 version 0.1.15 fromatitting smal fixes"}, {"sha": "8f9dee7", "date": "2024-05-02 15:55:41 +0200", "message": "ToolBoxV2 version 0.1.15"}, {"sha": "ffcfca8", "date": "2024-04-29 11:43:19 +0200", "message": "ToolBoxV2 version 0.1.15  web update -> vite + singel page application"}, {"sha": "96feaf2", "date": "2024-04-24 19:51:04 +0200", "message": "ToolBoxV2 version 0.1.15"}, {"sha": "b96b891", "date": "2024-04-24 16:35:02 +0200", "message": "ToolBoxV2 version 0.1.15"}, {"sha": "51b663b", "date": "2024-04-17 21:21:29 +0200", "message": "ToolBoxV2 version 0.1.14"}, {"sha": "de2b45f", "date": "2024-04-17 15:39:55 +0200", "message": "ToolBoxV2 version 0.1.14"}, {"sha": "e3db0c7", "date": "2024-04-15 09:35:23 +0200", "message": "ToolBoxV2 version 0.1.13 TB init test connection to P0 withe session.py beta SocketManager.py"}, {"sha": "3cfb75c", "date": "2024-04-12 01:47:25 +0200", "message": "ToolBoxV2 version 0.1.13 TB init test connection to P0 withe session.py"}, {"sha": "0cd2278", "date": "2024-04-11 22:42:30 +0200", "message": "ToolBoxV2 version 0.1.13 async runtime support"}, {"sha": "bbc0b24", "date": "2024-04-11 20:42:13 +0200", "message": "ToolBoxV2 version 0.1.12 update core web utils installer bulder"}, {"sha": "cbee25c", "date": "2024-04-10 00:49:18 +0200", "message": "ToolBoxV2 version 0.1.12 update core web utils installer bulder"}, {"sha": "5909617", "date": "2024-04-10 00:48:54 +0200", "message": "ToolBoxV2 version 0.1.12 update core web utils installer bulder"}, {"sha": "a30205f", "date": "2024-04-03 20:09:17 +0200", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "c4019a9", "date": "2024-04-03 19:55:59 +0200", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "ac7eb1b", "date": "2024-04-01 15:16:15 +0200", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "ee03d4e", "date": "2024-03-28 22:18:03 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "80d53e1", "date": "2024-03-28 21:51:38 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "6aab794", "date": "2024-03-28 21:43:30 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "7db858c", "date": "2024-03-28 21:42:38 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "32ba5aa", "date": "2024-03-28 21:41:10 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "0bd14f6", "date": "2024-03-28 21:33:53 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "4a66b6d", "date": "2024-03-28 21:30:13 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "abccb6c", "date": "2024-03-28 21:27:49 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "3812b87", "date": "2024-03-28 21:22:19 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "33cbcaf", "date": "2024-03-28 19:30:43 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "0414615", "date": "2024-03-28 19:29:51 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "239d40a", "date": "2024-03-28 18:51:48 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "80d70f0", "date": "2024-03-28 18:49:33 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.3"}, {"sha": "bfa453d", "date": "2024-03-28 18:40:32 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2"}, {"sha": "7b83dd0", "date": "2024-03-28 18:37:37 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2"}, {"sha": "1f6fbf3", "date": "2024-03-28 18:35:20 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2"}, {"sha": "2d7d148", "date": "2024-03-28 18:34:10 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2"}, {"sha": "6f200f0", "date": "2024-03-28 18:30:17 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2"}, {"sha": "3b9498e", "date": "2024-03-28 18:25:16 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2 blobs SocketManager.py"}, {"sha": "d94f986", "date": "2024-03-28 17:49:35 +0100", "message": "ToolBoxV2 version 0.1.12 cicd .0.2"}, {"sha": "35a743b", "date": "2024-03-28 16:59:57 +0100", "message": "ToolBoxV2 version 0.1.12 cicd and blobs"}, {"sha": "1f3bac4", "date": "2024-03-28 14:01:17 +0100", "message": "ToolBoxV2 version 0.1.12 M+"}, {"sha": "f0b2d83", "date": "2024-03-27 13:59:35 +0100", "message": "ToolBoxV2 version 0.1.12 M update save to git"}, {"sha": "c8fa38b", "date": "2024-03-15 01:06:57 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "1b1c1b9", "date": "2024-03-15 00:59:18 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "988378d", "date": "2024-03-15 00:57:43 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "1b812e8", "date": "2024-03-15 00:28:31 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "ee949de", "date": "2024-03-15 00:25:47 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "bdb9c85", "date": "2024-03-15 00:22:28 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "53dfe64", "date": "2024-03-15 00:14:31 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "58a14bb", "date": "2024-03-15 00:11:40 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "2ffb18b", "date": "2024-03-14 23:39:20 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "15cf7b8", "date": "2024-03-14 23:31:47 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "eacee00", "date": "2024-03-14 22:57:35 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "b16624a", "date": "2024-03-14 17:04:05 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "9377d01", "date": "2024-03-14 16:59:44 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "8b105eb", "date": "2024-03-14 16:42:38 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "ffcd5dc", "date": "2024-03-14 16:41:29 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "b87adfe", "date": "2024-03-14 16:38:45 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "761c960", "date": "2024-03-14 16:14:47 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "ac93e65", "date": "2024-03-14 16:12:39 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "8ac5840", "date": "2024-03-14 16:08:13 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "7978ed7", "date": "2024-03-14 16:05:12 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "e9cb98c", "date": "2024-03-14 16:03:20 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "5cc0233", "date": "2024-03-14 15:59:16 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "2791239", "date": "2024-03-14 15:32:23 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "7adbfbe", "date": "2024-03-14 15:26:14 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "1c57d1f", "date": "2024-03-14 14:52:49 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "dc77f77", "date": "2024-03-14 14:51:14 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "78936bd", "date": "2024-03-14 14:14:12 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "4796a2e", "date": "2024-03-14 13:52:31 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "b32a604", "date": "2024-03-14 01:45:21 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "a02fe01", "date": "2024-03-14 01:41:24 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "895a772", "date": "2024-03-14 01:38:16 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "85e7c45", "date": "2024-03-14 01:16:11 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "2b093a0", "date": "2024-03-14 01:15:48 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "12aaa87", "date": "2024-03-14 01:13:15 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "d3ccd9f", "date": "2024-03-14 01:10:31 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "1874db7", "date": "2024-03-14 01:07:14 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "9c0398c", "date": "2024-03-14 01:02:50 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "de112f0", "date": "2024-03-14 01:01:15 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "df181a3", "date": "2024-03-14 00:55:09 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "734784a", "date": "2024-03-14 00:51:27 +0100", "message": "ToolBoxV2 version 0.1.9 light-tb"}, {"sha": "9c908c6", "date": "2024-03-14 00:04:45 +0100", "message": "ToolBoxV2 version 0.1.9 add event manager support scome local next add local_network"}, {"sha": "090be60", "date": "2024-03-13 23:59:03 +0100", "message": "ToolBoxV2 version 0.1.9 add event manager support scome local next add local_network"}, {"sha": "6863249", "date": "2024-03-13 23:55:09 +0100", "message": "ToolBoxV2 version 0.1.9 add event manager support scome local next add local_network"}, {"sha": "d8b2988", "date": "2024-03-12 23:08:13 +0100", "message": "ToolBoxV2 version 0.1.9 DEVICE_KEY_PATH and cli test return"}, {"sha": "7ca1c26", "date": "2024-03-12 23:07:41 +0100", "message": "ToolBoxV2 version 0.1.9 DEVICE_KEY_PATH and cli test return"}, {"sha": "c1aaf5a", "date": "2024-03-12 23:00:32 +0100", "message": "ToolBoxV2 version 0.1.9 DEVICE_KEY_PATH and cli test return"}, {"sha": "df43ba4", "date": "2024-03-12 22:54:01 +0100", "message": "ToolBoxV2 version 0.1.9 DEVICE_KEY_PATH and cli test return"}, {"sha": "6af301b", "date": "2024-03-12 22:46:52 +0100", "message": "ToolBoxV2 version 0.1.9 DEVICE_KEY_PATH and cli test return"}, {"sha": "5993775", "date": "2024-03-12 22:39:09 +0100", "message": "ToolBoxV2 version 0.1.9 update  fix all version"}, {"sha": "c9ccc1d", "date": "2024-03-12 22:38:18 +0100", "message": "ToolBoxV2 version 0.1.9 update  fix Windows version"}, {"sha": "9623d6b", "date": "2024-03-12 22:36:17 +0100", "message": "ToolBoxV2 version 0.1.9 update  fix Windows version"}, {"sha": "dc9a1ba", "date": "2024-03-12 22:17:43 +0100", "message": "ToolBoxV2 version 0.1.9 update  CloudM pre state"}, {"sha": "a210402", "date": "2024-03-12 20:35:35 +0100", "message": "ToolBoxV2 version 0.1.9 update"}, {"sha": "9e6d625", "date": "2024-03-10 08:56:14 +0100", "message": "ToolBoxV2 version 0.1.8 update  fixed docs demo"}, {"sha": "f2b75db", "date": "2024-03-06 02:16:12 +0100", "message": "ToolBoxV2 version 0.1.8 update  fixed docs demo"}, {"sha": "ed025a9", "date": "2024-03-06 01:43:50 +0100", "message": "ToolBoxV2 version 0.1.8 update  fixed docs demo"}, {"sha": "991b290", "date": "2024-03-06 01:39:32 +0100", "message": "ToolBoxV2 version 0.1.8 update  fixed import errors"}, {"sha": "1eff5bd", "date": "2024-03-06 01:26:59 +0100", "message": "ToolBoxV2 version 0.1.8 update DEVICE_KEY_PATH"}, {"sha": "17d17c4", "date": "2024-03-06 01:14:31 +0100", "message": "ToolBoxV2 version 0.1.8 update DEVICE_KEY_PATH"}, {"sha": "01e95c3", "date": "2024-03-06 01:07:30 +0100", "message": "ToolBoxV2 version 0.1.8 update fix info dir"}, {"sha": "7b709e5", "date": "2024-03-06 00:59:44 +0100", "message": "ToolBoxV2 version 0.1.8 update backgrund runner and schockt connection use -fg for normal use"}, {"sha": "233b3e9", "date": "2024-03-06 00:52:55 +0100", "message": "ToolBoxV2 version 0.1.8 update backgrund runner and schockt connection use -fg for normal use"}, {"sha": "4b3e8a8", "date": "2024-03-06 00:52:11 +0100", "message": "ToolBoxV2 version 0.1.8 update backgrund runner and schockt connection use -fg for normal use"}, {"sha": "8f1cc10", "date": "2024-02-29 15:02:17 +0100", "message": "ToolBoxV2 version 0.1.7 update header params -> body"}, {"sha": "4606c10", "date": "2024-02-13 13:59:28 +0100", "message": "ToolBoxV2 version 0.1.7 update header params -> body"}, {"sha": "1a4ed3a", "date": "2024-02-13 13:57:44 +0100", "message": "ToolBoxV2 version 0.1.7 update header params -> body"}, {"sha": "e168806", "date": "2024-02-13 13:40:58 +0100", "message": "ToolBoxV2 version 0.1.7 update header params -> body"}, {"sha": "d948fc1", "date": "2024-02-13 01:25:50 +0100", "message": "Live beta no perstona log in"}, {"sha": "fa47823", "date": "2024-02-13 01:07:58 +0100", "message": "ToolBoxV2 version 0.1.7 update https session"}, {"sha": "8654a2f", "date": "2024-02-12 23:59:51 +0100", "message": "ToolBoxV2 version 0.1.7 update mini claim"}, {"sha": "b07faa8", "date": "2024-02-12 23:20:03 +0100", "message": "ToolBoxV2 version 0.1.7 update fix typo"}, {"sha": "6c36861", "date": "2024-02-12 23:18:31 +0100", "message": "ToolBoxV2 version 0.1.7 update fix typo"}, {"sha": "d958107", "date": "2024-02-12 23:09:37 +0100", "message": "ToolBoxV2 version 0.1.7 update Server auth test"}, {"sha": "db7b2cd", "date": "2024-02-12 22:56:24 +0100", "message": "ToolBoxV2 version 0.1.7 update Server auth test"}, {"sha": "d5a38d9", "date": "2024-02-12 19:01:25 +0100", "message": "ToolBoxV2 version 0.1.7 update api_manager.py add json"}, {"sha": "4828d38", "date": "2024-02-12 18:24:39 +0100", "message": "ToolBoxV2 version 0.1.7 update api_manager.py add json"}, {"sha": "25fcecf", "date": "2024-02-12 18:12:45 +0100", "message": "ToolBoxV2 version 0.1.7 update api_manager.py add json"}, {"sha": "59d943c", "date": "2024-02-12 18:10:37 +0100", "message": "ToolBoxV2 version 0.1.7 update api_manager.py add json"}, {"sha": "103351f", "date": "2024-02-12 17:53:55 +0100", "message": "ToolBoxV2 version 0.1.7 update api_manager.py add json"}, {"sha": "ee0189f", "date": "2024-02-12 17:44:49 +0100", "message": "ToolBoxV2 version 0.1.7 update api_manager.py add json"}, {"sha": "f6a48da", "date": "2024-02-12 15:49:14 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.8) Sending and receive Files withe Gzip p2p & SC alph + large files and folders Stable"}, {"sha": "06b3929", "date": "2024-02-12 15:46:13 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.8) Sending and receive Files withe Gzip p2p & SC alph + large files and folders Stable"}, {"sha": "bb88c61", "date": "2024-02-12 15:37:15 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.8) Sending and receive Files withe Gzip p2p & SC alph + large files and folders Stable"}, {"sha": "c66281f", "date": "2024-02-12 15:14:30 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.8) Sending and receive Files withe Gzip p2p & SC alph + large files and folders Stable"}, {"sha": "89cff25", "date": "2024-02-12 15:08:37 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "69624ea", "date": "2024-02-12 15:02:35 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "e851d11", "date": "2024-02-12 15:02:15 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "03615da", "date": "2024-02-12 14:38:36 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "5409edf", "date": "2024-02-12 14:24:18 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "e144137", "date": "2024-02-12 14:23:20 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "3551201", "date": "2024-02-12 14:22:47 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "8d6aa6f", "date": "2024-02-12 14:12:11 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "f6f7c67", "date": "2024-02-12 14:07:48 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "5192156", "date": "2024-02-12 14:01:17 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "ed1be93", "date": "2024-02-12 13:06:45 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "b256d97", "date": "2024-02-12 13:01:44 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "b8b7fd0", "date": "2024-02-12 12:58:33 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "42c228c", "date": "2024-02-12 12:55:40 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "211b5b6", "date": "2024-02-12 12:53:49 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "72a4d12", "date": "2024-02-12 12:50:28 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "17d96da", "date": "2024-02-12 12:49:01 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "b20e07b", "date": "2024-02-12 12:41:17 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "dfe1422", "date": "2024-02-12 12:37:23 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "5fb07c7", "date": "2024-02-12 12:33:33 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "69cfcf3", "date": "2024-02-12 12:25:06 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.7) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "83fbdf9", "date": "2024-02-12 12:18:37 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "b1a1604", "date": "2024-02-12 12:11:06 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "0f08c46", "date": "2024-02-12 12:02:50 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "5ac64e8", "date": "2024-02-12 12:00:36 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p & SC alph + large files"}, {"sha": "67ee9a9", "date": "2024-02-12 11:51:03 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "64906aa", "date": "2024-02-12 03:31:29 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "24e8c75", "date": "2024-02-12 03:29:49 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "e713b51", "date": "2024-02-12 03:28:10 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "6e2f83a", "date": "2024-02-12 03:26:45 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "80032e2", "date": "2024-02-12 03:24:44 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "8c3d6ef", "date": "2024-02-12 03:23:44 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip p2p& SC alph + large files"}, {"sha": "0b2f7f9", "date": "2024-02-12 02:33:08 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "82d3ed0", "date": "2024-02-12 02:29:46 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "52175f7", "date": "2024-02-12 02:27:45 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "31419be", "date": "2024-02-12 02:26:52 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "b25cf83", "date": "2024-02-12 02:22:29 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "25e2a30", "date": "2024-02-12 02:21:29 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "51b92ca", "date": "2024-02-12 02:15:55 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "1f44792", "date": "2024-02-12 02:07:41 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "40a758a", "date": "2024-02-12 01:56:19 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip SC no p2p alph + large files"}, {"sha": "a635b09", "date": "2024-02-12 01:54:47 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "87fa33e", "date": "2024-02-12 01:41:43 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.6) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "101b741", "date": "2024-02-12 01:38:05 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.5) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "27759dd", "date": "2024-02-12 01:36:05 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.5) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "71cd8ab", "date": "2024-02-12 01:13:11 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.5) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "53e8ec3", "date": "2024-02-12 01:09:34 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.5) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "ef0263d", "date": "2024-02-12 01:05:56 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.5) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "82cde23", "date": "2024-02-12 00:59:18 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.4) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "a4c9f2f", "date": "2024-02-12 00:56:00 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.4) Sending and receive Files withe Gzip P2P alph + large files"}, {"sha": "b258e91", "date": "2024-02-12 00:42:09 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "ae6dd4c", "date": "2024-02-12 00:34:25 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "07c1b40", "date": "2024-02-12 00:32:54 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "f5a095b", "date": "2024-02-12 00:27:16 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "df03e7e", "date": "2024-02-12 00:25:19 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "7389215", "date": "2024-02-12 00:17:44 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "78cd508", "date": "2024-02-12 00:14:35 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.2) Sending and receive Files withe Gzip P2P beta"}, {"sha": "6211a41", "date": "2024-02-12 00:13:23 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.2) Sending and receive Files withe Gzip P2P beta"}, {"sha": "ea88764", "date": "2024-02-12 00:06:49 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.1) Sending and receive Files withe Gzip P2P beta"}, {"sha": "ba5fbb6", "date": "2024-02-12 00:04:24 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.1) Sending and receive Files withe Gzip P2P beta"}, {"sha": "fb124b5", "date": "2024-02-12 00:00:17 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.1) Sending and receive Files withe Gzip P2P beta"}, {"sha": "3567b12", "date": "2024-02-11 23:55:51 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.1.0) Sending and receive Files withe Gzip P2P beta"}, {"sha": "73e7313", "date": "2024-02-11 23:50:20 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.9) Sending and receive Files withe Gzip P2P beta"}, {"sha": "fe808ce", "date": "2024-02-11 23:46:42 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.8) Sending and receive Files withe Gzip P2P beta"}, {"sha": "9bae03b", "date": "2024-02-11 23:40:17 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.8) Sending and receive Files withe Gzip P2P beta"}, {"sha": "e48ae1f", "date": "2024-02-11 23:37:25 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.8) Sending and receive Files withe Gzip P2P beta"}, {"sha": "88076af", "date": "2024-02-11 23:36:23 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.8) Sending and receive Files withe Gzip P2P beta"}, {"sha": "cfe1efa", "date": "2024-02-11 23:33:32 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.8) Sending and receive Files withe Gzip P2P beta"}, {"sha": "234942d", "date": "2024-02-11 23:26:58 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.7) Sending and receive Files withe Gzip P2P beta"}, {"sha": "d2b7994", "date": "2024-02-11 23:20:13 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.6) Sending and receive Files withe Gzip P2P beta"}, {"sha": "bee5d52", "date": "2024-02-11 23:09:09 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.5) Sending and receive Files withe Gzip P2P beta"}, {"sha": "85e3614", "date": "2024-02-11 23:08:50 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.4) Sending and receive Files withe Gzip P2P beta"}, {"sha": "8de10a4", "date": "2024-02-11 22:51:49 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.4) Sending and receive Files withe Gzip P2P beta"}, {"sha": "21a12ba", "date": "2024-02-11 22:49:42 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py v(0.0.3) Sending and receive Files withe Gzip P2P beta"}, {"sha": "b9084ce", "date": "2024-02-11 22:47:39 +0100", "message": "ToolBoxV2 version 0.1.7 update SocketManager.py Sending and receive Files withe Gzip P2P beta"}, {"sha": "1f3413c", "date": "2024-02-11 21:54:00 +0100", "message": "ToolBoxV2 version 0.1.7 update CMajor update"}, {"sha": "cc187c0", "date": "2024-01-31 15:30:44 +0100", "message": "ToolBoxV2 version 0.1.6 update CloudM & DB security update"}, {"sha": "bc75628", "date": "2024-01-31 15:25:38 +0100", "message": "ToolBoxV2 version 0.1.6 update CloudM & DB security update"}, {"sha": "8cf35c7", "date": "2024-01-31 15:13:13 +0100", "message": "ToolBoxV2 version 0.1.6 update cloudM & DB security update"}, {"sha": "508bf2f", "date": "2024-01-31 15:03:20 +0100", "message": "ToolBoxV2 version 0.1.6 update Major api update security and auth added sessions jwt and ras"}, {"sha": "a420e1a", "date": "2024-01-18 21:00:49 +0100", "message": "Live beta no perstona log in"}, {"sha": "624c013", "date": "2024-01-17 14:06:45 +0100", "message": "ToolBoxV2 version 0.1.6 update allow origin simplecore.app"}, {"sha": "a86b69e", "date": "2024-01-17 00:32:20 +0100", "message": "ToolBoxV2 version 0.1.6 update dockerEnv.py Imports and on exit for Classes"}, {"sha": "61bece3", "date": "2024-01-17 00:07:13 +0100", "message": "ToolBoxV2 version 0.1.6 update app live update & ewl"}, {"sha": "9b88476", "date": "2024-01-16 21:27:07 +0100", "message": "ToolBoxV2 version 0.1.6 update app live update & ewl"}, {"sha": "9e38e8f", "date": "2024-01-16 20:22:59 +0100", "message": "ToolBoxV2 version 0.1.6 update app live update"}, {"sha": "ac23250", "date": "2024-01-16 20:14:15 +0100", "message": "ToolBoxV2 version 0.1.6 update app live update"}, {"sha": "98b4082", "date": "2024-01-16 19:39:16 +0100", "message": "ToolBoxV2 version 0.1.6 update qn update cor update"}, {"sha": "d769e68", "date": "2024-01-16 19:37:02 +0100", "message": "ToolBoxV2 version 0.1.6 update qn update cor update"}, {"sha": "8adcac5", "date": "2024-01-16 19:34:13 +0100", "message": "ToolBoxV2 version 0.1.6 update qn update cor update"}, {"sha": "9a27270", "date": "2024-01-16 19:33:41 +0100", "message": "ToolBoxV2 version 0.1.6 update qn update cor update"}, {"sha": "a3521ed", "date": "2024-01-16 19:33:07 +0100", "message": "ToolBoxV2 version 0.1.6 update qn update cor update"}, {"sha": "c6b4df0", "date": "2024-01-16 17:40:09 +0100", "message": "ToolBoxV2 version 0.1.6 update hot fix"}, {"sha": "f69b8d4", "date": "2024-01-16 17:38:30 +0100", "message": "ToolBoxV2 version 0.1.6 update hot fix"}, {"sha": "c67e915", "date": "2024-01-16 16:51:09 +0100", "message": "ToolBoxV2 version 0.1.6 update dockerEnv fix"}, {"sha": "cd5fd63", "date": "2024-01-16 16:45:19 +0100", "message": "ToolBoxV2 version 0.1.6 update removed for ever running fuctions in tests"}, {"sha": "e33c289", "date": "2024-01-16 16:28:55 +0100", "message": "ToolBoxV2 version 0.1.6 update fix toolbox result"}, {"sha": "d94bf33", "date": "2024-01-16 15:02:45 +0100", "message": "ToolBoxV2 version 0.1.6 update fix toolbox result"}, {"sha": "96c2c97", "date": "2024-01-16 14:08:36 +0100", "message": "ToolBoxV2 version 0.1.6 update fix test sett data and config dir"}, {"sha": "1e7b3ea", "date": "2024-01-16 14:06:03 +0100", "message": "ToolBoxV2 version 0.1.6 update CloudM add QuickNote"}, {"sha": "f5252fb", "date": "2024-01-16 14:05:14 +0100", "message": "ToolBoxV2 version 0.1.6 update Internal update"}, {"sha": "c2c2628", "date": "2024-01-12 20:49:18 +0100", "message": "ToolBoxV2 version 0.1.6 update cloudM update add userinstaces add emal waiting list demo"}, {"sha": "80c4668", "date": "2024-01-12 02:33:58 +0100", "message": "ToolBoxV2 version 0.1.6 update refactor last app prefix"}, {"sha": "cfa47a5", "date": "2024-01-12 02:29:09 +0100", "message": "ToolBoxV2 version 0.1.6 update teste and profiler v 0"}, {"sha": "55860fe", "date": "2024-01-12 00:52:18 +0100", "message": "ToolBoxV2 version 0.1.6 update Mods : CloudM and DB"}, {"sha": "0b5dab8", "date": "2024-01-12 00:51:14 +0100", "message": "ToolBoxV2 version 0.1.6 update  - ApiResult type Result <- BaseModel  - added functionality for result type -- TB  - add chash option file and in mem   pre_compute=None   post_compute=None   memory_cache=False   file_cache=False   memory_cache_max_size=100   memory_cache_ttl=300"}, {"sha": "e1f1826", "date": "2023-12-27 15:05:07 +0100", "message": "ToolBoxV2 verstion 0.1.6 update"}, {"sha": "d6a907b", "date": "2023-12-13 12:52:13 +0100", "message": "CLI Update beta"}, {"sha": "06e44e3", "date": "2023-12-13 12:51:06 +0100", "message": "ToolBoxV2 0.1.6 New Functionality"}, {"sha": "6e7f334", "date": "2023-12-13 12:25:29 +0100", "message": "__init__.py mods remove circular input  utils -> add types and classes (all_functions_enums.py)"}, {"sha": "f008854", "date": "2023-11-27 23:12:05 +0100", "message": "cm update"}, {"sha": "f9e39fc", "date": "2023-11-19 20:57:29 +0100", "message": "multi thraded apis"}, {"sha": "442c175", "date": "2023-11-19 20:14:55 +0100", "message": "undo"}, {"sha": "4bc55ae", "date": "2023-11-19 20:08:44 +0100", "message": "update installer :"}, {"sha": "f6c3536", "date": "2023-11-19 19:59:27 +0100", "message": "update installer auto docking"}, {"sha": "24fc331", "date": "2023-11-19 19:57:00 +0100", "message": "update installer , state system for instalaition url"}, {"sha": "7183cd4", "date": "2023-11-19 17:52:57 +0100", "message": "update auto cration form installer files"}, {"sha": "1e5c44f", "date": "2023-11-19 17:37:31 +0100", "message": "Api - Simplehub.com api finisched"}, {"sha": "201779e", "date": "2023-11-19 17:12:45 +0100", "message": "Api - Hot<PERSON><PERSON><PERSON> hidden"}, {"sha": "b230f42", "date": "2023-11-19 17:09:41 +0100", "message": "Api - app udates , installer"}, {"sha": "760de7a", "date": "2023-11-19 16:26:34 +0100", "message": "dockerEnv.py linux var dir update fix worg name"}, {"sha": "d1e8ab3", "date": "2023-11-19 16:24:48 +0100", "message": "dockerEnv.py linux var dir update & modInstaller no app"}, {"sha": "b9467c2", "date": "2023-11-19 16:08:48 +0100", "message": "first app updates"}, {"sha": "4a8107d", "date": "2023-11-16 00:03:55 +0100", "message": "mod installer final"}, {"sha": "38b2df8", "date": "2023-11-15 23:25:57 +0100", "message": "mod installer test local"}, {"sha": "4b8ff52", "date": "2023-11-15 21:23:27 +0100", "message": "update docker utility"}, {"sha": "a7a6bcc", "date": "2023-11-15 21:05:04 +0100", "message": "remove of docker test"}, {"sha": "661bc0c", "date": "2023-11-15 19:05:34 +0100", "message": "Docker update remove non mvp test"}, {"sha": "445a5e8", "date": "2023-11-15 19:01:54 +0100", "message": "Docker update remove non mvp test"}, {"sha": "c95a00e", "date": "2023-11-15 18:46:21 +0100", "message": "docker update, disable docker in docker"}, {"sha": "8ec7233", "date": "2023-11-15 18:36:59 +0100", "message": "docker update, disable docker in docker"}, {"sha": "e416317", "date": "2023-11-15 17:49:50 +0100", "message": "docker cm update mini 2"}, {"sha": "79ffa39", "date": "2023-11-15 17:38:01 +0100", "message": "update"}, {"sha": "429637c", "date": "2023-11-15 14:00:26 +0100", "message": "update"}, {"sha": "27c5030", "date": "2023-11-14 21:54:28 +0100", "message": "update cm"}, {"sha": "1db4fe9", "date": "2023-11-14 21:42:13 +0100", "message": "add live mods"}, {"sha": "eee9b58", "date": "2023-11-14 21:41:35 +0100", "message": "reoved dev mods"}, {"sha": "cffc70c", "date": "2023-11-14 21:41:07 +0100", "message": "ToolBoxV2 0.1.1 bre beat <PERSON> to MVP 10Days"}, {"sha": "3fb400f", "date": "2023-11-09 21:44:06 +0100", "message": "docker update for tb light"}, {"sha": "9cdc2f1", "date": "2023-11-09 21:00:36 +0100", "message": "mini updates"}, {"sha": "9b7799c", "date": "2023-11-09 21:00:08 +0100", "message": "beta docker env"}, {"sha": "1747473", "date": "2023-11-09 20:59:41 +0100", "message": "test -perf_counter"}, {"sha": "65682e0", "date": "2023-11-09 20:59:12 +0100", "message": "ws update"}, {"sha": "76477c9", "date": "2023-11-09 20:58:31 +0100", "message": "toolbox perf over hale"}, {"sha": "7d20345", "date": "2023-11-09 20:55:32 +0100", "message": "isaa update pre new agents and manager"}, {"sha": "370946a", "date": "2023-10-10 19:35:08 +0200", "message": "."}, {"sha": "c0c9e7e", "date": "2023-10-04 16:33:51 +0200", "message": "bitte keine fehler mehr kb. 4 requirements.txt numexpr>=2.8.6"}, {"sha": "193f5fd", "date": "2023-10-04 16:31:20 +0200", "message": "bitte keine fehler mehr kb. 4 requirements.txt numexpr>=2.8.7"}, {"sha": "14ba4e6", "date": "2023-10-04 16:14:27 +0200", "message": "bitte keine fehler mehr kb. 3"}, {"sha": "eb5becf", "date": "2023-10-04 16:08:40 +0200", "message": "bitte keine fehler mehr kb. 2"}, {"sha": "3e1e4c8", "date": "2023-10-04 15:56:45 +0200", "message": "bitte keine fehler mehr kb. 1"}, {"sha": "d7498ea", "date": "2023-10-04 15:22:39 +0200", "message": "bitte keine fehler mehr kb."}, {"sha": "799a5fd", "date": "2023-10-04 15:08:52 +0200", "message": "unittest git charmap update"}, {"sha": "a9a0579", "date": "2023-10-04 14:14:54 +0200", "message": "version S"}, {"sha": "d37b81c", "date": "2023-10-04 14:06:36 +0200", "message": "mini fix"}, {"sha": "33fb17c", "date": "2023-10-04 14:05:45 +0200", "message": "fix 25h miss print"}, {"sha": "c609b0d", "date": "2023-10-04 13:44:58 +0200", "message": "isaa remove encoding"}, {"sha": "cbe9a6e", "date": "2023-10-04 13:28:56 +0200", "message": "Import update logger better prompt control utf-8 encoding better tests."}, {"sha": "df93403", "date": "2023-10-03 23:09:52 +0200", "message": "Agent Memory 2x"}, {"sha": "1a0521d", "date": "2023-10-03 22:40:07 +0200", "message": "Agent Memory"}, {"sha": "485250d", "date": "2023-10-03 22:18:49 +0200", "message": "wikipedia>=1.4.0"}, {"sha": "1c65504", "date": "2023-10-03 21:21:50 +0200", "message": "export secrets OPENAI_API_KEY"}, {"sha": "9ba5145", "date": "2023-10-03 20:51:50 +0200", "message": "isaa update and AgentUtils.py"}, {"sha": "886929a", "date": "2023-10-03 20:39:25 +0200", "message": "isaa update imports"}, {"sha": "1a3cfd0", "date": "2023-10-03 20:18:05 +0200", "message": "isaa update typing import List"}, {"sha": "e7af54b", "date": "2023-10-03 20:10:34 +0200", "message": "requirements.txt update chromadb>=0.4.13 (isaa)"}, {"sha": "1a1214d", "date": "2023-10-03 20:05:42 +0200", "message": "requirements.txt update Pebble>=5.0.3 (isaa)"}, {"sha": "980f8ef", "date": "2023-10-03 19:59:50 +0200", "message": "git action on master /workflow"}, {"sha": "102757b", "date": "2023-10-03 19:58:30 +0200", "message": "requirements.txt update for isaa"}, {"sha": "e5f5aab", "date": "2023-10-03 18:38:06 +0200", "message": ".env-template"}, {"sha": "22668ad", "date": "2023-10-03 18:32:50 +0200", "message": "toolbox Minimal isaa Version"}, {"sha": "fbdb166", "date": "2023-09-24 00:43:37 +0200", "message": "toolbox min defaut isaa3"}, {"sha": "65b64b1", "date": "2023-09-21 18:07:50 +0200", "message": "toolbox min defaut isaa2"}, {"sha": "d711b3d", "date": "2023-09-20 00:24:29 +0200", "message": "toolbox min defaut isaa"}, {"sha": "bdd0295", "date": "2023-08-22 19:14:30 +0200", "message": "toolbox run-i fix"}, {"sha": "4356dae", "date": "2023-08-22 19:05:24 +0200", "message": "stabel discord <PERSON><PERSON>"}, {"sha": "53c531e", "date": "2023-08-22 02:41:10 +0200", "message": "dicord bot add welcom msg"}, {"sha": "c0bcaae", "date": "2023-08-22 02:04:40 +0200", "message": "init dicord bot for master hydration cloud m setter"}, {"sha": "b60ee70", "date": "2023-08-21 02:19:08 +0200", "message": "init isaa"}, {"sha": "f824503", "date": "2023-08-21 02:16:58 +0200", "message": "- Autocompletion Default-Werte hinzugefügt - Nur ausführbare Funktionen, die mit dem Buchstaben 'r' beginnen, werden für die initiale Geschwindigkeitslast ausgeführt - Alle Mods wurden zur tb-Initialisierung hinzugefügt und ein Sicherheitscatch MODS_ERROR wurde eingeführt - Neue Fh-Dict-Syntax jetzt für die init-Datei init.config - Löschen von Backslash / Schlüssel funktioniert jetzt - Entfernung von altem serve.py und Test-HTML in new_tes - Refactoring von toolbox.py util, Anpassung an das neue Dict-System - run_function läuft jetzt im vollen Debug-Modus ohne Schutzschienen - args_sto hinzugefügt - WebSocket-URI lokal importiert"}, {"sha": "7a92846", "date": "2023-08-17 01:16:09 +0200", "message": "Add self print to toolbox for flexibility"}, {"sha": "2a5ae2a", "date": "2023-08-17 01:15:10 +0200", "message": "list to List"}, {"sha": "6b96d3b", "date": "2023-08-13 19:50:59 +0200", "message": "removed unnecessary isaa exra from master add get state from toolbox"}, {"sha": "cea5621", "date": "2023-08-10 23:41:51 +0000", "message": "live debug logs"}, {"sha": "2b089b0", "date": "2023-08-09 16:13:37 +0000", "message": "Addreplit firt adjsutmants"}, {"sha": "b5fe10b", "date": "2023-08-07 18:58:01 +0200", "message": "added in cli run run-i"}, {"sha": "40a5dca", "date": "2023-08-06 19:24:05 +0200", "message": "un kill ws"}, {"sha": "b3347bd", "date": "2023-08-06 19:22:18 +0200", "message": "relies 0.1.0"}, {"sha": "0c47a58", "date": "2023-08-04 18:38:04 +0200", "message": "# toolboxv2 module fix"}, {"sha": "8452799", "date": "2023-08-04 18:27:48 +0200", "message": "Merge pull request #9 from MarkinHaus/light-tb"}, {"sha": "cd28826", "date": "2023-08-04 18:20:04 +0200", "message": "fix confict"}, {"sha": "1743bde", "date": "2023-08-04 18:16:54 +0200", "message": "fix confict"}, {"sha": "9f48cc3", "date": "2023-08-04 17:57:35 +0200", "message": "light-tb client and server"}, {"sha": "7cb5022", "date": "2023-08-04 17:46:34 +0200", "message": "light-client for direct use"}, {"sha": "345b0e9", "date": "2023-08-04 17:44:16 +0200", "message": "light-client for direct use"}, {"sha": "6bfebd6", "date": "2023-08-04 17:32:14 +0200", "message": "light-client for direct use"}, {"sha": "1525aa2", "date": "2023-08-04 17:29:19 +0200", "message": "light-client for direct use"}, {"sha": "6cf6c39", "date": "2023-08-04 17:28:25 +0200", "message": "light-client for direct use"}, {"sha": "d66fedd", "date": "2023-08-04 17:26:43 +0200", "message": "light-client for direct use"}, {"sha": "ba8c67d", "date": "2023-08-04 17:16:59 +0200", "message": ".data and .config is now constant"}, {"sha": "7281e69", "date": "2023-08-04 16:32:50 +0200", "message": "Semi Stabel Version"}, {"sha": "2ab2571", "date": "2023-08-03 13:42:17 +0200", "message": "test v1"}, {"sha": "9e9ad7e", "date": "2023-07-24 19:54:56 +0200", "message": "test v1"}, {"sha": "ee6875b", "date": "2023-07-12 02:58:45 +0200", "message": "now Tool Box v2 alpha - 0.0.1 Linux update"}, {"sha": "df045e6", "date": "2023-07-12 01:21:49 +0200", "message": "now Tool Box v2 alpha - 0.0.1 user level set func"}, {"sha": "a04284b", "date": "2023-07-12 01:03:15 +0200", "message": "now Tool Box v2 alpha - 0.0.1 next version alpha -0.2 -todos:  - Options Widget   - User | Isaa Task | Mods | workspaces  - Isaa widget   v : a.0.3  - Workspaces"}, {"sha": "2ac60e8", "date": "2023-07-03 17:35:52 +0200", "message": "Save"}, {"sha": "48a7cbf", "date": "2023-07-03 17:31:03 +0200", "message": "git ignore update Next stepes Wel<PERSON> The root user."}, {"sha": "3e0f914", "date": "2023-06-29 13:14:56 +0200", "message": "ToolBox App V1.5 cloudM init server Next stepes Welcom The root user."}, {"sha": "9f369a5", "date": "2023-06-19 20:23:00 +0200", "message": "ios patch and v0 web templates"}, {"sha": "bdaf848", "date": "2023-06-19 15:34:31 +0200", "message": "ToolBox App V1 - ws and bg login and key sing in cloudM init server Next stepes Welcom The root user."}, {"sha": "47e721a", "date": "2023-06-15 02:59:50 +0200", "message": "ubdate pre m auto. full v2 qur chain runner with self evalation."}, {"sha": "18e2b0d", "date": "2023-06-15 01:03:23 +0200", "message": "ubdate pre m auto. full v1"}, {"sha": "eeedfd5", "date": "2023-06-14 02:55:24 +0200", "message": "ubdate pre s auto. functions v3"}, {"sha": "50d0f30", "date": "2023-06-12 13:18:23 +0200", "message": "ios patch and dev mods"}, {"sha": "e82ebf9", "date": "2023-06-01 01:39:42 +0200", "message": "app template v 1"}, {"sha": "a0be492", "date": "2023-05-31 02:17:44 +0200", "message": "new feature and bungs xD"}, {"sha": "b0b885b", "date": "2023-05-31 02:16:42 +0200", "message": "new feature and bungs xD"}, {"sha": "c454344", "date": "2023-05-26 15:12:14 +0200", "message": "new env template"}, {"sha": "e593bc6", "date": "2023-05-26 15:10:44 +0200", "message": "Isaa re work and binary tree implementation. v2"}, {"sha": "022f2c4", "date": "2023-05-26 15:06:20 +0200", "message": "Isaa re work and binary tree implementation."}, {"sha": "3aba9a3", "date": "2023-05-10 00:42:51 +0200", "message": "init - 2.0"}, {"sha": "16003b5", "date": "2023-05-08 00:21:34 +0200", "message": "Merge branch 'init'"}, {"sha": "d9dd8b0", "date": "2023-05-07 23:09:24 +0200", "message": "init - api and mod installer isaa"}, {"sha": "5187bb1", "date": "2023-05-05 14:03:02 +0200", "message": "init - api and mod installer"}, {"sha": "e58ecef", "date": "2023-05-04 00:33:50 +0200", "message": "installer and remover for mods"}, {"sha": "481be4b", "date": "2023-05-04 00:28:47 +0200", "message": "installer and remover for mods"}, {"sha": "d155cf7", "date": "2023-05-03 23:27:51 +0200", "message": "isaa installer logs fix ../log"}, {"sha": "d15e42b", "date": "2023-05-03 19:06:13 +0200", "message": "For first init installation"}, {"sha": "a3823d2", "date": "2023-05-03 18:08:28 +0200", "message": "Update"}, {"sha": "79f3da2", "date": "2023-05-03 18:07:18 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "5ae125b", "date": "2023-04-23 17:22:54 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "e8b9845", "date": "2023-04-23 17:22:22 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "6bfcd7e", "date": "2023-04-23 17:21:38 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "c8c9469", "date": "2023-04-23 17:14:12 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "19a8a47", "date": "2023-04-23 17:05:31 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "07d6486", "date": "2023-04-23 17:02:35 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "6e57714", "date": "2023-04-23 16:53:11 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "8318679", "date": "2023-04-18 13:35:32 +0200", "message": "<PERSON><PERSON> Update"}, {"sha": "6fffd03", "date": "2023-03-28 23:17:14 +0200", "message": "test unstable"}, {"sha": "c0a573c", "date": "2023-03-27 20:05:51 +0200", "message": "test unstable"}, {"sha": "48a5152", "date": "2023-03-22 20:21:25 +0100", "message": "fixing server set default worker to 1"}, {"sha": "fb9456d", "date": "2023-03-22 20:12:07 +0100", "message": "fixing server set default port to 8000"}, {"sha": "b160452", "date": "2023-03-22 20:09:25 +0100", "message": "fixing server"}, {"sha": "8154734", "date": "2023-03-22 20:06:06 +0100", "message": "fixing server"}, {"sha": "0798fea", "date": "2023-03-22 20:00:57 +0100", "message": "test"}, {"sha": "44d71bb", "date": "2023-03-22 19:57:08 +0100", "message": "init isaa"}, {"sha": "e60f8c4", "date": "2023-03-22 19:50:59 +0100", "message": "missing one"}, {"sha": "d278866", "date": "2023-03-22 19:49:53 +0100", "message": "missing one"}, {"sha": "01efb8e", "date": "2023-03-22 19:48:38 +0100", "message": "missing one"}, {"sha": "6f5b5d7", "date": "2023-03-22 19:45:26 +0100", "message": "missing one"}, {"sha": "685e69d", "date": "2023-03-22 19:43:06 +0100", "message": "missing one"}, {"sha": "f3772cf", "date": "2023-03-22 19:37:57 +0100", "message": "commit to main branch ... app update"}, {"sha": "98157db", "date": "2023-03-03 12:37:24 +0100", "message": "test fix 1"}, {"sha": "a79cb13", "date": "2023-02-27 22:03:04 +0100", "message": "test fix 1"}, {"sha": "bb9e81f", "date": "2023-02-27 21:49:18 +0100", "message": "api - pid  from /app to /"}, {"sha": "7caed2c", "date": "2023-02-27 21:40:31 +0100", "message": "__init__.py -> update function availability api_manager.py -> stop_api fixes cli.py -> edit log flag - closing Ghosts apps cloudM.py -> strip dead code | refactoring cryp.py -> source aut code from toolbox.py daytree.py -> in develop state DB.py -> formal corrections fast_api.py -> formal corrections file_handler.py -> source aut code from toolbox.py init.config -> error fix (double exit flag) main_tool.py -> source aut code from toolbox.py quickNote.py -> source aut code get_uid to main_tool readchar_buldin_style_cli.py -> new logs system | advanced debug streamlit_web_dev_tools.py -> tbname auto naming Style.py -> remove_styles funtion for logging"}, {"sha": "61c1288", "date": "2023-01-19 20:59:52 +0100", "message": "cloudM login f sting mistake ..."}, {"sha": "e9e3199", "date": "2023-01-19 20:46:16 +0100", "message": "DB string return check"}, {"sha": "12278f2", "date": "2023-01-19 20:22:08 +0100", "message": "fast_api exit security - token validation + ovner"}, {"sha": "07c488a", "date": "2023-01-19 19:53:46 +0100", "message": "update restart better formatting"}, {"sha": "12ef210", "date": "2023-01-19 19:52:34 +0100", "message": "update restart better formatting"}, {"sha": "4d4cf0c", "date": "2023-01-19 19:50:12 +0100", "message": "update restart better formatting"}, {"sha": "8cb6497", "date": "2023-01-19 19:41:24 +0100", "message": "update restart better formatting"}, {"sha": "66d8302", "date": "2023-01-19 19:39:36 +0100", "message": "update out test"}, {"sha": "3e4b526", "date": "2023-01-19 19:38:25 +0100", "message": "update loop fix"}, {"sha": "2274fb7", "date": "2023-01-19 19:36:53 +0100", "message": "impel the -v -l command chain for printig the version of alle moules fix"}, {"sha": "f2dc30a", "date": "2023-01-19 19:28:46 +0100", "message": "impel the -v -l command chain for printig the version of alle moules"}, {"sha": "7de27cb", "date": "2023-01-19 19:23:21 +0100", "message": "tb version 0.0.3"}, {"sha": "08c5928", "date": "2023-01-19 19:18:55 +0100", "message": "update save mode and hard pull"}, {"sha": "662bafd", "date": "2023-01-19 18:58:50 +0100", "message": "api fixes"}, {"sha": "240d7e1", "date": "2023-01-19 18:40:28 +0100", "message": "Merge pull request #7 from MarkinHaus/daytree"}, {"sha": "e09613c", "date": "2023-01-19 18:26:02 +0100", "message": "daytree.py update to 0.0.1 TB on_start | on_exit -> auto close-open"}, {"sha": "f8c4263", "date": "2023-01-16 13:09:51 +0100", "message": "Merge pull request #6 from MarkinHaus/daytree"}, {"sha": "10df276", "date": "2023-01-16 13:08:51 +0100", "message": "Merge pull request #5 from MarkinHaus/master"}, {"sha": "6266d31", "date": "2023-01-16 12:55:15 +0100", "message": "Merge pull request #4 from MarkinHaus/daytree"}, {"sha": "06e67fd", "date": "2023-01-11 16:35:49 +0100", "message": "daytree.py update"}, {"sha": "191af6f", "date": "2023-01-11 16:35:22 +0100", "message": "cloudM & fast_api fixes"}, {"sha": "84f2b14", "date": "2023-01-04 17:55:05 +0100", "message": "Merge pull request #2 from MarkinHaus/quiknote"}, {"sha": "50b0976", "date": "2023-01-04 17:27:28 +0100", "message": "quickNote.py to pre alpa state."}, {"sha": "35ef65d", "date": "2023-01-04 17:19:41 +0100", "message": "quickNote.py to pre alpa state."}, {"sha": "b3f4cde", "date": "2023-01-02 17:22:02 +0100", "message": "new setup.cfg"}, {"sha": "7c20764", "date": "2023-01-02 17:05:25 +0100", "message": ".md v 0.0.1-5"}, {"sha": "f3f80fa", "date": "2023-01-02 16:56:52 +0100", "message": "alpha -> close-beta live"}, {"sha": "91be779", "date": "2023-01-02 16:22:17 +0100", "message": "alpha -> close-beta live"}, {"sha": "058b583", "date": "2023-01-02 16:16:53 +0100", "message": "alpha -> close-beta live"}, {"sha": "4bcef5c", "date": "2022-12-30 18:59:35 +0100", "message": "dev update - 0.0.9"}, {"sha": "0c8910c", "date": "2022-12-30 18:52:41 +0100", "message": "dev update - 0.0.7"}, {"sha": "c81d94d", "date": "2022-12-30 18:49:57 +0100", "message": "dev update - 0.0.6"}, {"sha": "12efb02", "date": "2022-12-30 18:39:59 +0100", "message": "dev update - 0.0.5"}, {"sha": "4995608", "date": "2022-12-30 18:35:06 +0100", "message": "dev update - 0.0.4"}, {"sha": "df0adc7", "date": "2022-12-30 17:26:19 +0100", "message": "dev update - 0.0.3"}, {"sha": "1ba35f5", "date": "2022-12-30 15:57:47 +0100", "message": "dev update - 0.0.2"}, {"sha": "069d8b6", "date": "2022-12-30 15:55:03 +0100", "message": "dev update"}, {"sha": "9d43c6c", "date": "2022-12-30 15:31:27 +0100", "message": "Initial commit"}]