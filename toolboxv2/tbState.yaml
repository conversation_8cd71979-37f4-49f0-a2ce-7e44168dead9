api: {}
app: {}
installable: {}
mods:
  AdminDashboard.py:
    provider: SimpleCore
    shasum: 8e34133ef6d69991f2c61dd7c6fe58ca4261908118a12702120189ad3089f6f0
    url: https://simplecore.app/mods/AdminDashboard.py
    version: legacy
  AgentUtils.py:
    provider: SimpleCore
    shasum: e1be07ceeba50201ae0e2278c36de1ff16164b9abfd1c4beb739499428d95d39
    url: https://simplecore.app/mods/AgentUtils.py
    version: legacy
  AuthManager.py:
    provider: SimpleCore
    shasum: 00c12cb55e27d34f832b1a168233b184ca829652053f2cd863f319759f2e97b3
    url: https://simplecore.app/mods/AuthManager.py
    version: legacy
  Canvas.py:
    provider: SimpleCore
    shasum: c8c6fd9ad2a83d14443e333fa143f6c08d0d3325076019e72ca4903041750f17
    url: https://simplecore.app/mods/Canvas.py
    version: 0.1.0
  ChatModule.py:
    provider: SimpleCore
    shasum: f658c2f13eb51e4148a422be149156aa38daaac39d235271d4029a4521cd9f46
    url: https://simplecore.app/mods/ChatModule.py
    version: 1.0.0
  CodeVerification.py:
    provider: SimpleCore
    shasum: 1c8b852f228d5f09f271a143cf04311fb1fb855c2cec5ea4c3e0ba1aa4e9a7bc
    url: https://simplecore.app/mods/CodeVerification.py
    version: 0.0.1
  CounterTracker.py:
    provider: SimpleCore
    shasum: 467243783b2b01c92e8da09c8b58332d773b8a255b05a28ee6c81b53d9fb33dc
    url: https://simplecore.app/mods/CounterTracker.py
    version: dependency
  FaissVectorStore.py:
    provider: SimpleCore
    shasum: 63bf5c8a01147e8d8f9569e5904a6d2ff747d73207ece96cfc2f147faefb992d
    url: https://simplecore.app/mods/FaissVectorStore.py
    version: legacy
  FileWidget.py:
    provider: SimpleCore
    shasum: e4127a332a71334e4f4041d12dc4d37b9f8c645a5a1fa1c44c2ea66aeefa64a9
    url: https://simplecore.app/mods/FileWidget.py
    version: 0.2.1
  KnowledgeBase.py:
    provider: SimpleCore
    shasum: 9b93a7151d154598aafb933181a24d815911cb4f0e6c3664ca9060b94604a55f
    url: https://simplecore.app/mods/KnowledgeBase.py
    version: legacy
  MinimalHtml.py:
    provider: SimpleCore
    shasum: 119e2674a0685e9f69c06c77581c9e0859e49850eb3df2ed9a1827febf248af2
    url: https://simplecore.app/mods/MinimalHtml.py
    version: 0.0.2
  ModManager.py:
    provider: SimpleCore
    shasum: 3b072a0ff742bacb34fbe6b4fa3182a47180e2caabee510fd9fc801194bee784
    url: https://simplecore.app/mods/ModManager.py
    version: legacy
  ModManager_tests.py:
    provider: SimpleCore
    shasum: b36f0f4dfc15ad4bf2df1d6ab778526afb773ed8e01f271136b7ac48ea3ec465
    url: https://simplecore.app/mods/ModManager_tests.py
    version: legacy
  P2PRPCClient.py:
    provider: SimpleCore
    shasum: c039076dc5be0ddd0d5affed303719e70a1a9b9610253c8277e9a4a30afe0c0b
    url: https://simplecore.app/mods/P2PRPCClient.py
    version: 0.1.0
  P2PRPCServer.py:
    provider: SimpleCore
    shasum: 7a54f675d3daa2c4abc9f780b4a0b66dc0b372ad11f27f36a12e6f40beb9a883
    url: https://simplecore.app/mods/P2PRPCServer.py
    version: 0.1.0
  PasswordManager.py:
    provider: SimpleCore
    shasum: c3d3ee4faa90782be0c10d61a029d3429786236aacd7ef73e35ce069269c1f61
    url: https://simplecore.app/mods/PasswordManager.py
    version: dependency
  ProcessManager.py:
    provider: SimpleCore
    shasum: dd5a99b7dfb079ee220a43533ca27735e24f0a3f6b6352dbdc809dfe17486ac8
    url: https://simplecore.app/mods/ProcessManager.py
    version: 0.0.1
  RedisVectorStore.py:
    provider: SimpleCore
    shasum: ecfa7ab41da1dac6ebedc2f010e19813c0acd0cfd7ebfb67f7efaff40c751418
    url: https://simplecore.app/mods/RedisVectorStore.py
    version: legacy
  SchedulerManager.py:
    provider: SimpleCore
    shasum: 2562957097e59c786569f228e7ee85a004a2f120f6b952441ab07d4d6cfd8626
    url: https://simplecore.app/mods/SchedulerManager.py
    version: 0.0.2
  SimpleCore.py:
    provider: SimpleCore
    shasum: 48efca0a63591e89bd7f52727832b2e7d9e436aba703b1d568c38338db41ec92
    url: https://simplecore.app/mods/SimpleCore.py
    version: 2.0.0
  SocketManager.py:
    provider: SimpleCore
    shasum: 47b1857dead925d4c991bcbfa37739ff5c7dd5450318339ae4af72b3743b8609
    url: https://simplecore.app/mods/SocketManager.py
    version: 0.1.9
  StorageUtil.py:
    provider: SimpleCore
    shasum: 09e44bd93f45bf21c2fe145f10b45ef1c0b5d151dc9f763521ec52d75da66afa
    url: https://simplecore.app/mods/StorageUtil.py
    version: legacy
  UltimateTTT.py:
    provider: SimpleCore
    shasum: ca10416a872bd171a139d35271369a077d573ed4733846517c5e058b3b895dd7
    url: https://simplecore.app/mods/UltimateTTT.py
    version: dependency
  UserAccountManager.py:
    provider: SimpleCore
    shasum: 89065326cf995579e4f1c96a5033844d4c74f188ca52ae5ead33d2106a86a069
    url: https://simplecore.app/mods/UserAccountManager.py
    version: legacy
  UserDashboard.py:
    provider: SimpleCore
    shasum: 1288ae1ec015606563b7ff557272a3c53b5f01e0eda1208b4ed6908b9386cb01
    url: https://simplecore.app/mods/UserDashboard.py
    version: legacy
  UserInstances.py:
    provider: SimpleCore
    shasum: 67cfc7b79a76cb48b8ba6d3188ce2443447e8d620319f10e375c37a48e096598
    url: https://simplecore.app/mods/UserInstances.py
    version: legacy
  WebSocketManager.py:
    provider: SimpleCore
    shasum: a97f16dcc6b19c7fac5dfc0f253955daa29e1e79945be14b467e3f2c51208445
    url: https://simplecore.app/mods/WebSocketManager.py
    version: 2.0.0
  __init__.py:
    provider: SimpleCore
    shasum: ddf7deb30f4076128913c1d2ecd79ddc7d0bd4de3065271c278a25c2e07f5504
    url: https://simplecore.app/mods/__init__.py
    version: legacy
  _temp_0.py:
    provider: SimpleCore
    shasum: 95d389454738789f8b8dcb08b11dab83521026298ae5d5e62e9bda66cc4665e1
    url: https://simplecore.app/mods/_temp_0.py
    version: legacy
  acs.py:
    provider: SimpleCore
    shasum: de1ffce72d4ce47d8fcc2ee290cce3192925a564ed765aee62d74f92efb9b7e4
    url: https://simplecore.app/mods/acs.py
    version: legacy
  adapter.py:
    provider: SimpleCore
    shasum: 7a19e27d729f2b3376db9391d3da97e8c3116a080d17ad7059797bca3b337823
    url: https://simplecore.app/mods/adapter.py
    version: legacy
  agent.py:
    provider: SimpleCore
    shasum: 193987b701fbd542882cf242f63241d1ba603f6c55e987ff113224690e1a28de
    url: https://simplecore.app/mods/agent.py
    version: legacy
  arXivCrawler.py:
    provider: SimpleCore
    shasum: 5851f39ce7689360efed8d1b486bb11958801bd2801181f81218e968370e99dc
    url: https://simplecore.app/mods/arXivCrawler.py
    version: legacy
  audio_generator.py:
    provider: SimpleCore
    shasum: 08ae06e8d7eb109bb4b2586a86e10ffc73a3ebc7e57d9842d6eb7a2b365cf3e5
    url: https://simplecore.app/mods/audio_generator.py
    version: legacy
  auth.py:
    provider: SimpleCore
    shasum: c61aacd54406ff353c5a4853017f9c53be86ef0f12fad931d8bd9d1f82a39ed3
    url: https://simplecore.app/mods/auth.py
    version: legacy
  base_agent.py:
    provider: SimpleCore
    shasum: 41a8ffe40b3d5afff75e83badedcf0fe3cdc6ce0dba4c3029745d6c5ce92f533
    url: https://simplecore.app/mods/base_agent.py
    version: legacy
  base_models.py:
    provider: SimpleCore
    shasum: 3da5a07f8134b9044ad0f898cae5623528666794caff343e38c91b885b657fe3
    url: https://simplecore.app/mods/base_models.py
    version: legacy
  base_parser.py:
    provider: SimpleCore
    shasum: 828add3219f7ed6805297826f67a59b0b9a0bd9db70f3c33a567ccb895c79be3
    url: https://simplecore.app/mods/base_parser.py
    version: legacy
  base_tester.py:
    provider: SimpleCore
    shasum: e20f99118df8a8e2f247c3e8ad299a33069ea62ab30925be02e448b2e5c078ff
    url: https://simplecore.app/mods/base_tester.py
    version: legacy
  blob_instance.py:
    provider: SimpleCore
    shasum: e7836479d3bc9faa8e00f345518fd67f4e60ac2b70a683dc5817a30368771170
    url: https://simplecore.app/mods/blob_instance.py
    version: legacy
  board_widget.py:
    provider: SimpleCore
    shasum: 36e38e6723b01c26f25fb0569b60ac11287c245cd013b30b240c91e826408f17
    url: https://simplecore.app/mods/board_widget.py
    version: legacy
  builder.py:
    provider: SimpleCore
    shasum: 94e4ab66aeed6bfe78c4d30a9f040988e07a9a8bf95c7fe339a11c4b9bf3d587
    url: https://simplecore.app/mods/builder.py
    version: legacy
  cahin_printer.py:
    provider: SimpleCore
    shasum: 8dff11776981af0812346b2832618f2ca899fb2f8d3758052a676779c344633d
    url: https://simplecore.app/mods/cahin_printer.py
    version: legacy
  chain.py:
    provider: SimpleCore
    shasum: 8f65ad11368bf970e18e55b9a7d11755e5c4835208b8820eb9a67545165d7686
    url: https://simplecore.app/mods/chain.py
    version: legacy
  chainUi.py:
    provider: SimpleCore
    shasum: a54640a68889e3747b12b525f33b8b9c2172a6ca0e8496b25ad92234a94097ab
    url: https://simplecore.app/mods/chainUi.py
    version: legacy
  cli_functions.py:
    provider: SimpleCore
    shasum: f3322e6e73569abfe919dcdcf511fdaf95bb7ef94dff04244b88832099ac8d1d
    url: https://simplecore.app/mods/cli_functions.py
    version: 0.0.1
  client.py:
    provider: SimpleCore
    shasum: 8f06456f58b3accb9e22b4b92522fa8b4fb21cdcc08c9a7b81f8ba74f54e587d
    url: https://simplecore.app/mods/client.py
    version: legacy
  clip_generator.py:
    provider: SimpleCore
    shasum: f87791a7c797b69f0c6917f8e995f7b88b28bb24b96cdfe205a44ef0ed85bbac
    url: https://simplecore.app/mods/clip_generator.py
    version: legacy
  coder.py:
    provider: SimpleCore
    shasum: 83d30e2c87f8e03fc540f06b789aef74f75a2a7591e5a3b196f9d21316332e09
    url: https://simplecore.app/mods/coder.py
    version: legacy
  coder_agent-1.py:
    provider: SimpleCore
    shasum: e850d84296f3a58e4669fc8f9a2b252aa1265963635aea6ef7fc9c4d7fdc7559
    url: https://simplecore.app/mods/coder_agent-1.py
    version: legacy
  coder_agent.py:
    provider: SimpleCore
    shasum: e850d84296f3a58e4669fc8f9a2b252aa1265963635aea6ef7fc9c4d7fdc7559
    url: https://simplecore.app/mods/coder_agent.py
    version: legacy
  coder_agentv2.py:
    provider: SimpleCore
    shasum: 64e39e6101dfd8d40ded9d2875502f6b026fc0785563262dfc7cfe2e6dc15b7d
    url: https://simplecore.app/mods/coder_agentv2.py
    version: legacy
  coder_agentv3.py:
    provider: SimpleCore
    shasum: 3be3b444d6938602a33c94d840e4af1817c6f7ad2b4d10c6837c5ed1fe3be7e3
    url: https://simplecore.app/mods/coder_agentv3.py
    version: legacy
  coder_agentv4.py:
    provider: SimpleCore
    shasum: 4188bd0891fcd494cbed4a0a7475bc77ff4401c7eaf91b3b6647b423124ee31f
    url: https://simplecore.app/mods/coder_agentv4.py
    version: legacy
  complexity.py:
    provider: SimpleCore
    shasum: 091966ecc2b4eeb5733dafe81a0d6a0e117f452dee5f590b9e1b8cc69c824d65
    url: https://simplecore.app/mods/complexity.py
    version: legacy
  complexity_evaluator.py:
    provider: SimpleCore
    shasum: 4ee4f0a1d6145678c9dd5437e4f0a2ea046e24f22d07d5a5755539acf2ea4de7
    url: https://simplecore.app/mods/complexity_evaluator.py
    version: legacy
  config.py:
    provider: SimpleCore
    shasum: 0eade26c9ded03fe2ff267e826ee764acf770686f4bb113bfa6d997903ef05cb
    url: https://simplecore.app/mods/config.py
    version: legacy
  config_parser.py:
    provider: SimpleCore
    shasum: f4d1a90b365e7b46f6aa3eaefe13edff583d15db0ecfa3699a2c14b26cef1afd
    url: https://simplecore.app/mods/config_parser.py
    version: legacy
  context.py:
    provider: SimpleCore
    shasum: 13f19133854a084e100b1b797c28fd7145c82e8ab30f25214b08d3d074324fe5
    url: https://simplecore.app/mods/context.py
    version: legacy
  context_manager.py:
    provider: SimpleCore
    shasum: 42fecd3c3dff053beb48315f327c139a61bedb3a8d39ab5066082336b72e2a20
    url: https://simplecore.app/mods/context_manager.py
    version: legacy
  credits.py:
    provider: SimpleCore
    shasum: 5f53617abf6b77c49707dd81c23d5e17118973e570c56fbc306df279b526f5c7
    url: https://simplecore.app/mods/credits.py
    version: legacy
  deep_analyst.py:
    provider: SimpleCore
    shasum: 3168ca53537075dee0683daa2a608bd4affcade5c413d854bebdcd5e9e902484
    url: https://simplecore.app/mods/deep_analyst.py
    version: legacy
  demo_code.py:
    provider: SimpleCore
    shasum: 50d54231babcf21ffdc17aaf8f773a5da58c4685e7a50293c221de9cf53f010d
    url: https://simplecore.app/mods/demo_code.py
    version: legacy
  demo_custom_messaging.py:
    provider: SimpleCore
    shasum: 72eb3dee4a9cb7251600604b39c8b386471f623b5efa4f1a3082eeaf6d126f4a
    url: https://simplecore.app/mods/demo_custom_messaging.py
    version: legacy
  demo_main.py:
    provider: SimpleCore
    shasum: 7c9239728a83acb69e5db7f9b8e3023436652711bba5e7724013d54a7d732763
    url: https://simplecore.app/mods/demo_main.py
    version: legacy
  demo_registry.py:
    provider: SimpleCore
    shasum: 4367dcc22b6e7af5dbe81c22fe90af00276422f454cf5cd2a2d59f1ce3422553
    url: https://simplecore.app/mods/demo_registry.py
    version: legacy
  documentation_io.py:
    provider: SimpleCore
    shasum: f04e59fe9354f9151068ff396530128c68d3d3d248c653817edfe78a40726f75
    url: https://simplecore.app/mods/documentation_io.py
    version: legacy
  email_services.py:
    provider: SimpleCore
    shasum: aa0a98d245c99e3fc71533d518aec19ec9edde5ab28aa0f8fe588bfc791f282d
    url: https://simplecore.app/mods/email_services.py
    version: legacy
  executors.py:
    provider: SimpleCore
    shasum: 624e7b4483a3b96017b6aab22b83634bcc5051fe891b4646bee2251e57cee9d3
    url: https://simplecore.app/mods/executors.py
    version: legacy
  extras.py:
    provider: SimpleCore
    shasum: ff0cda592a8adbc15028373640daceb3149f9650f71ecd575901543359e1a2b4
    url: https://simplecore.app/mods/extras.py
    version: legacy
  filter.py:
    provider: SimpleCore
    shasum: bf0c4a1b43306b5174949c7e8002d04d33fd61b10c1d379f15dd9842e73b7882
    url: https://simplecore.app/mods/filter.py
    version: legacy
  framework_manager.py:
    provider: SimpleCore
    shasum: 91974baa1375519e7ebfb46a078d414dc6f9b94502432589fa060101668cf2ce
    url: https://simplecore.app/mods/framework_manager.py
    version: legacy
  generation.py:
    provider: SimpleCore
    shasum: 4bbe3209af635318e424e8f1717557ceb29181434113bccf8943506ba55bf7fc
    url: https://simplecore.app/mods/generation.py
    version: legacy
  helper.py:
    provider: SimpleCore
    shasum: 2f6965cab60fe202e1ddfaa8778406b77c434e888436621f885c973e1681e207
    url: https://simplecore.app/mods/helper.py
    version: 0.1.0
  html_assets.py:
    provider: SimpleCore
    shasum: dac33f6b2a50b749583fb7c1ad1b6f5e5a5ada411f3acac62e650e787f71cd81
    url: https://simplecore.app/mods/html_assets.py
    version: legacy
  html_css_parser.py:
    provider: SimpleCore
    shasum: 91327d7facadb96f9382d55367d22746f443a7fd820359ad590045a8d8fa7acf
    url: https://simplecore.app/mods/html_css_parser.py
    version: legacy
  html_generator.py:
    provider: SimpleCore
    shasum: c783e9d28c847c0c4f30347d8d5f0e90f3cd1ea70222da7fca6937d5c2e9a82c
    url: https://simplecore.app/mods/html_generator.py
    version: legacy
  image_generator.py:
    provider: SimpleCore
    shasum: c72de501737477224fc022560d40c9cf6cabddb1692df285f290660e53f69f0a
    url: https://simplecore.app/mods/image_generator.py
    version: legacy
  integration_tester.py:
    provider: SimpleCore
    shasum: 4c6f38fad05b7d49bb61cfb17c1393b3e8ce4b7586df1fdddd9113c7be40c7b6
    url: https://simplecore.app/mods/integration_tester.py
    version: legacy
  isaa_modi.py:
    provider: SimpleCore
    shasum: 644cf00bad3ccc6eef5a7fe9ec05a48db3245a7a3ffc50998713783c0d7b2a2f
    url: https://simplecore.app/mods/isaa_modi.py
    version: legacy
  live.py:
    provider: SimpleCore
    shasum: 8a0d9d32debc13935eff2754781d67fa773bab616fdce943af03692aba763ba6
    url: https://simplecore.app/mods/live.py
    version: legacy
  local_instance.py:
    provider: SimpleCore
    shasum: cbd59d79f0eae2dd6c568e4504159ea13bfad53aaf39c912a626d47350c1d8d6
    url: https://simplecore.app/mods/local_instance.py
    version: legacy
  main.py:
    provider: SimpleCore
    shasum: ae12193f246901f422b2c0c73a46473f26d80e550a20945a8ef274cd55073145
    url: https://simplecore.app/mods/main.py
    version: legacy
  mcp_session_manager.py:
    provider: SimpleCore
    shasum: 8f5e64af4ac326187aa5512c8432486d18debeb4d62736ecd7293d5fd440c2eb
    url: https://simplecore.app/mods/mcp_session_manager.py
    version: legacy
  mini.py:
    provider: SimpleCore
    shasum: 8545058ad881db86d1bc79615840a34a758ee4f48c53166b345d3f10e25e547b
    url: https://simplecore.app/mods/mini.py
    version: legacy
  modes.py:
    provider: SimpleCore
    shasum: 94da14bd978a5fb20bcd969d61f7cd7639008b028a94a2c6aee28599b4c61d2d
    url: https://simplecore.app/mods/modes.py
    version: legacy
  module.py:
    provider: SimpleCore
    shasum: 271b24dc87fffe44d775efc15a64c9669669742e0404e89efcd06387928dc87b
    url: https://simplecore.app/mods/module.py
    version: legacy
  nGui.py:
    provider: SimpleCore
    shasum: 0e8d9319b5fb5b65b5ec44b770aa483e0abaed9e1e87d275a3f38cdbc0f5acd7
    url: https://simplecore.app/mods/nGui.py
    version: legacy
  newui.py:
    provider: SimpleCore
    shasum: 73bf5514ed25adf2c56aed8568bab24f61df8c3e41ebf09432da2a5be68aa105
    url: https://simplecore.app/mods/newui.py
    version: legacy
  orchestrator.py:
    provider: SimpleCore
    shasum: e9e2570e7d46905fa4c61cbbc2297198794ee83e218e37c4bafa6fb318a80ca5
    url: https://simplecore.app/mods/orchestrator.py
    version: legacy
  pdf_generator.py:
    provider: SimpleCore
    shasum: 1e5584c14dc8b1308f8812d2d5b71dcb988520873f5eca65dd685781a0e8f5e3
    url: https://simplecore.app/mods/pdf_generator.py
    version: legacy
  project_manager.py:
    provider: SimpleCore
    shasum: f20b423eddd1536962f1b1864a637069234d472b4df7db9b42477c202872c962
    url: https://simplecore.app/mods/project_manager.py
    version: legacy
  projects.py:
    provider: SimpleCore
    shasum: 9ff793d29502df58bfc2df06c0167362019fd17b6c2529abcd6e224376adf342
    url: https://simplecore.app/mods/projects.py
    version: legacy
  python_parser.py:
    provider: SimpleCore
    shasum: 27bd8663778b33759870dc64ca751948c445416b54b4e8fd040ff22421d4a406
    url: https://simplecore.app/mods/python_parser.py
    version: legacy
  python_tester.py:
    provider: SimpleCore
    shasum: 8da632536908154f01549fb51577613096dd365ae4ca874e259f10a8af325648
    url: https://simplecore.app/mods/python_tester.py
    version: legacy
  reddis_instance.py:
    provider: SimpleCore
    shasum: c9eba42d7a0c59c03c2063882ea74372062d2b8dd03f22ba962f51801e6142d2
    url: https://simplecore.app/mods/reddis_instance.py
    version: legacy
  routines_manager.py:
    provider: SimpleCore
    shasum: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
    url: https://simplecore.app/mods/routines_manager.py
    version: legacy
  run_demo.py:
    provider: SimpleCore
    shasum: 1a7a6ff653f992976615af46ddceaf4254ec0c1c15fadbb52f58ba0ab945442a
    url: https://simplecore.app/mods/run_demo.py
    version: legacy
  run_tests.py:
    provider: SimpleCore
    shasum: d2da61de12f16cbda50124feec5452d9f493ff1b47de98b660ac08e0e41be9cf
    url: https://simplecore.app/mods/run_tests.py
    version: legacy
  scanner.py:
    provider: SimpleCore
    shasum: efef409735d9b95e557ad127f0400bbc4b5df17547cf4dc6aaa58e5cea563343
    url: https://simplecore.app/mods/scanner.py
    version: legacy
  server.py:
    provider: SimpleCore
    shasum: 4e2f549b2afe88944b270d33368c668a73ecc1694d58475c319e2c334c6469e3
    url: https://simplecore.app/mods/server.py
    version: legacy
  session.py:
    provider: SimpleCore
    shasum: 45fd65235fbf59a5216430fb4ef25fc77c32efb5f0bd0999e2bd8c096796c661
    url: https://simplecore.app/mods/session.py
    version: legacy
  specs.py:
    provider: SimpleCore
    shasum: 7873ddb326983bc16cfa26c19b4bea20944a154880066e603319ecbc0afed6cd
    url: https://simplecore.app/mods/specs.py
    version: legacy
  specs_generator.py:
    provider: SimpleCore
    shasum: 8e1c6afba29b58df819830d2f2dc878ab6a7119616721f08711995e018fef87c
    url: https://simplecore.app/mods/specs_generator.py
    version: legacy
  steps.py:
    provider: SimpleCore
    shasum: 3d2d42a78e833157f6812ba586317e6d8588026b2684b96c128e067029820374
    url: https://simplecore.app/mods/steps.py
    version: legacy
  story_generator.py:
    provider: SimpleCore
    shasum: 46c433382a0ad6d322815d5137f1e0442caf3074438f5353b99d150c49608d99
    url: https://simplecore.app/mods/story_generator.py
    version: legacy
  taichiNumpyNumbaVectorStores.py:
    provider: SimpleCore
    shasum: 4096729972126658ab1df7eb7a177ec80c886e21d76305b2386b858a6cf6ea62
    url: https://simplecore.app/mods/taichiNumpyNumbaVectorStores.py
    version: legacy
  talk.py:
    provider: SimpleCore
    shasum: d6cde5978f0bea6c8613aea935cc782dc388cd7cffa221e301ffae81cf28c57d
    url: https://simplecore.app/mods/talk.py
    version: 1.0.0
  tasks.py:
    provider: SimpleCore
    shasum: 7a7eabcde7165b7b46287409fcd323f3473b14a17cc29328f1b22224ecfe50e8
    url: https://simplecore.app/mods/tasks.py
    version: legacy
  tb_adapter.py:
    provider: SimpleCore
    shasum: b0932193a1779db5da185181465d387b391133aef96fd9d3b363d12a035ef9dc
    url: https://simplecore.app/mods/tb_adapter.py
    version: legacy
  terminal_progress.py:
    provider: SimpleCore
    shasum: 7dc5d40130787911f5d86e1d38debb4e52c34f158c1b0ffba9ef9e1480e20f67
    url: https://simplecore.app/mods/terminal_progress.py
    version: legacy
  terminal_progress2.py:
    provider: SimpleCore
    shasum: 5444bde65162627428f9e8243213d92f992502b7a6b3d84ea4dec28c0220a81f
    url: https://simplecore.app/mods/terminal_progress2.py
    version: legacy
  test_api.py:
    provider: SimpleCore
    shasum: ed8e51f896c377ad2b4f405f192797145cfa26061d5f977ee145c5caf91848a9
    url: https://simplecore.app/mods/test_api.py
    version: legacy
  test_audio_generator.py:
    provider: SimpleCore
    shasum: a058b12f39e44cd8819f6ca9bd6e711232ffeac203ba53cd3b43be66e4c425ad
    url: https://simplecore.app/mods/test_audio_generator.py
    version: legacy
  test_base_agent.py:
    provider: SimpleCore
    shasum: cd67f8b7dd446a49abb737913319e05a0d4e0bc2414d1d833e9be196c506e580
    url: https://simplecore.app/mods/test_base_agent.py
    version: legacy
  test_clip_generator.py:
    provider: SimpleCore
    shasum: 5e20c2bbdd7c18d770500d646584b6a43cd611809c45a0c086bee14d36c0e14d
    url: https://simplecore.app/mods/test_clip_generator.py
    version: legacy
  test_complexity_evaluator.py:
    provider: SimpleCore
    shasum: 1e6afbe99e76f1fb77375b695cee0e6c771a128d4d7caf8f2dd9ef2d97d24a94
    url: https://simplecore.app/mods/test_complexity_evaluator.py
    version: legacy
  test_complexity_models.py:
    provider: SimpleCore
    shasum: 2ceb68525c9bc6cb1d24b5db63232ee84cc70dd7f20cd55e3d8f35aa7ff3869e
    url: https://simplecore.app/mods/test_complexity_models.py
    version: legacy
  test_context_models.py:
    provider: SimpleCore
    shasum: 05d786b4e2ea2f5699ab84fa015565f5cb33fa4133627e7a161509fb08f43e80
    url: https://simplecore.app/mods/test_context_models.py
    version: legacy
  test_framework_manager.py:
    provider: SimpleCore
    shasum: 23f3bae00caf5382b0cd31f48c14d89ccbe34ca29469f2f115e7df3f6ebb9542
    url: https://simplecore.app/mods/test_framework_manager.py
    version: legacy
  test_html_generator.py:
    provider: SimpleCore
    shasum: 8471e78302c8af7e1403c46c175acfe3b0c4e7912c849adab32ae5272900deda
    url: https://simplecore.app/mods/test_html_generator.py
    version: legacy
  test_image_generator.py:
    provider: SimpleCore
    shasum: 96ab040d9ddde33c066fb1d17d7d671438c248dd17d561c5b30c79875352b337
    url: https://simplecore.app/mods/test_image_generator.py
    version: legacy
  test_init.py:
    provider: SimpleCore
    shasum: 9730f4ccdbe670f5a234ddc04a5944c6318499d3cc6c100517fc14057b46067c
    url: https://simplecore.app/mods/test_init.py
    version: legacy
  test_integration.py:
    provider: SimpleCore
    shasum: 5bfc6a9ed855582ccac51218d2d36ddee6e81aeee8828fb54721ce49b4d95238
    url: https://simplecore.app/mods/test_integration.py
    version: legacy
  test_models.py:
    provider: SimpleCore
    shasum: a07a348479c434a7e5445786ed9c225d6963a701860f64d39eb1855b99849aed
    url: https://simplecore.app/mods/test_models.py
    version: legacy
  test_pdf_generator.py:
    provider: SimpleCore
    shasum: f725dda24ef431f7da8ac94eff9dfbdde4a2e8b12037f41c3701e47954a8f4ec
    url: https://simplecore.app/mods/test_pdf_generator.py
    version: legacy
  test_pipeline.py:
    provider: SimpleCore
    shasum: c1c2bdcc9cba47d25e8379540edfacc31301d46fe913c3ea944c1a56f1d3326e
    url: https://simplecore.app/mods/test_pipeline.py
    version: legacy
  test_project_manager.py:
    provider: SimpleCore
    shasum: 0976cc05c88ca139e64224fd728fe6fca4b49c5c8129486ed0a5b4bf0da2be14
    url: https://simplecore.app/mods/test_project_manager.py
    version: legacy
  test_runner.py:
    provider: SimpleCore
    shasum: 9a420bb9f87221d05cce28e283f294888977e4eb921d2144e92279d67e60a5af
    url: https://simplecore.app/mods/test_runner.py
    version: legacy
  test_scanner.py:
    provider: SimpleCore
    shasum: d2c1242d33ecf31bad4d1569b97aab142601976423570cbecb1612ca9ea9117b
    url: https://simplecore.app/mods/test_scanner.py
    version: legacy
  test_steps.py:
    provider: SimpleCore
    shasum: 957a1d8297bccb6f44dd5d37a46b637d04a95a7ffad4c05063748839a9a995f6
    url: https://simplecore.app/mods/test_steps.py
    version: legacy
  test_story_generator.py:
    provider: SimpleCore
    shasum: ca7bdc4dd20f9d24452b46f992a5c54af1367afb742d1fd3be1ff163f4865b76
    url: https://simplecore.app/mods/test_story_generator.py
    version: legacy
  test_suite_comprehensive.py:
    provider: SimpleCore
    shasum: 95af09010bf89cbde7360b55674ca66cdb510cac6b8ddc216b0f8b9b7fd34a41
    url: https://simplecore.app/mods/test_suite_comprehensive.py
    version: legacy
  test_video_generator.py:
    provider: SimpleCore
    shasum: 2a0af04a77af9343b737dfb0bca22a0701583be03a5ccce3d10beb1dd6edeb36
    url: https://simplecore.app/mods/test_video_generator.py
    version: legacy
  testing_modal.py:
    provider: SimpleCore
    shasum: c9698a60754b8804e191adb2503e9a885012fe335c76c4bf63b8e1af41f57e0a
    url: https://simplecore.app/mods/testing_modal.py
    version: legacy
  tests.py:
    provider: SimpleCore
    shasum: 073e6d20114795ceecd415e944ad4b6c030a3cbe32218556797ce68ce570b3a1
    url: https://simplecore.app/mods/tests.py
    version: legacy
  tools.py:
    provider: SimpleCore
    shasum: 9f4c909ecc2dc9813099813b308c4d88e033565110cb2b7490a578088a5af8b2
    url: https://simplecore.app/mods/tools.py
    version: legacy
  types.py:
    provider: SimpleCore
    shasum: 79421790acf9a4d0a664b2a47c1455cfa4945191d006d5abc355c0786edc96ff
    url: https://simplecore.app/mods/types.py
    version: legacy
  typescript_parser.py:
    provider: SimpleCore
    shasum: 6c8debec5c63079cd630325adbc7a822281306d16913497131ebc92d20a17b15
    url: https://simplecore.app/mods/typescript_parser.py
    version: legacy
  ui.py:
    provider: SimpleCore
    shasum: bd215f49cfa1e8f21baabe0f208d487e2ae733eb2fdab04b720a1eead6ac7828
    url: https://simplecore.app/mods/ui.py
    version: legacy
  update_story.py:
    provider: SimpleCore
    shasum: bf1ace83e6e7db929b32bbfae134fa0fca307c823b3cd8c5081cdaeb8c0beb81
    url: https://simplecore.app/mods/update_story.py
    version: legacy
  utils.py:
    provider: SimpleCore
    shasum: 51ac5215c2fae96e914bd17b588b3a9aa9de2a57c81570b9a3c3070e44a8004b
    url: https://simplecore.app/mods/utils.py
    version: legacy
  verbose_output.py:
    provider: SimpleCore
    shasum: d5dd2ac67210995f57d678db2946085bcb7701e98771e22575ff6b8d026f2c90
    url: https://simplecore.app/mods/verbose_output.py
    version: legacy
  video_generator.py:
    provider: SimpleCore
    shasum: bbf387704b9b2dfab884d2c25597177dd145538fa64ecfd8f3144b95f4a5848b
    url: https://simplecore.app/mods/video_generator.py
    version: legacy
  web_search.py:
    provider: SimpleCore
    shasum: 83fc70493780fc5d41c38136b063c97e592b4b951de9510845c92524c1deb428
    url: https://simplecore.app/mods/web_search.py
    version: legacy
  web_tester.py:
    provider: SimpleCore
    shasum: 4ca808bb939e2d149c42ab08db1cc30b60db94784917e7c384ac862937c144a2
    url: https://simplecore.app/mods/web_tester.py
    version: legacy
  welcome.py:
    provider: SimpleCore
    shasum: 9b43db433035946a0c33c85d98633dc4ed2311d85a6a6b083e0662b7a06c18ee
    url: https://simplecore.app/mods/welcome.py
    version: 0.1.24
  widget.py:
    provider: SimpleCore
    shasum: 627a4ac934fc9741b90cf7d8c948183d0b5b8d88c0cbdad5c570fb0cd85b145e
    url: https://simplecore.app/mods/widget.py
    version: legacy
  wiget_test.py:
    provider: SimpleCore
    shasum: 350d53bcfa219187273df35fc5fa6a267cabfb6ebd716890415c782ea5302ab3
    url: https://simplecore.app/mods/wiget_test.py
    version: legacy
  workspace_maintainer.py:
    provider: SimpleCore
    shasum: 3e508ba696bc8ce0b5ac6ff6f8d7b525ff34d1bf8b3f8f89e20b36cc3b925814
    url: https://simplecore.app/mods/workspace_maintainer.py
    version: legacy
  workspace_manager.py:
    provider: SimpleCore
    shasum: 6e106521c37abb31c72597a770ce55aa8318f26ef01e173e0eea18432788986b
    url: https://simplecore.app/mods/workspace_manager.py
    version: legacy
runnable: {}
utils:
  Style.py:
    provider: git
    shasum: 7135373a6e4b5a39e23dcde5b2b4101413cdcdce080f522667cb51654d27df44
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/Style.py
    version: 0.1.24
  __init__.py:
    provider: git
    shasum: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/__init__.py
    version: 0.1.24
  all_functions_enums.py:
    provider: git
    shasum: 2cec28a769d8485b88325af9de12260f3a72736732506ed6832de55c9f0d8b39
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/all_functions_enums.py
    version: 0.1.24
  api.py:
    provider: git
    shasum: f6c7544f937b8d5353823eb6086e65debbdd6eea118557566b50c74a92c1c3bc
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/api.py
    version: 0.1.24
  ast_utils.py:
    provider: git
    shasum: 49d1841bea0263adac1811b131ebd70d48b0bd291d83e512a6676b98e7efd00c
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/ast_utils.py
    version: 0.1.24
  base_widget.py:
    provider: git
    shasum: 53bcbb08588926e516b74ce6e810b62c44a5c9b35a8576a220fa73f81187c5a8
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/base_widget.py
    version: 0.1.24
  blobs.py:
    provider: git
    shasum: 3d1c10631e7d0c37fda764147538845505e6c66abb8171b2ef44d6a1813ae05c
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/blobs.py
    version: 0.1.24
  bottleup.py:
    provider: git
    shasum: da6df6b5def0f08b77d1e457b7edd2b1095f765438f0a39f5fad91573981ae35
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/bottleup.py
    version: 0.1.24
  cache.py:
    provider: git
    shasum: c602e9be9cd10a5cc5ec849654ad54f98f56df9845f7bffc286516a3435ea06f
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/cache.py
    version: 0.1.24
  client.py:
    provider: git
    shasum: 77f072971d42656c4ed893bc73fccc087a48985d469cc89b686643f09558d90d
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/client.py
    version: 0.1.24
  conda_runner.py:
    provider: git
    shasum: 4028066da4390fc2dc5f2577834ec20de2632d7335dec8fc2f28e2710df820ae
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/conda_runner.py
    version: 0.1.24
  cryp.py:
    provider: git
    shasum: 644d697df766190eac8c8b42a7418548d80be5d4416e7c10006d99ae0aab0250
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/cryp.py
    version: 0.1.24
  daemon_app.py:
    provider: git
    shasum: 4203c6bff6423e7dbb672aa019ab075521ddcb85516d38dc2e9d9539b85144eb
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/daemon_app.py
    version: 0.1.24
  daemon_util.py:
    provider: git
    shasum: a476ee3a54a1366c36b3bc47397559fe067d708c1cf28f4287f3990e5a1175a7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/daemon_util.py
    version: 0.1.24
  db_cli_manager.py:
    provider: git
    shasum: 73339d478a9d68439cff20bfe1973f383909a7a9ca9a9cbd0424cd93a2f04d1e
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/db_cli_manager.py
    version: 0.1.24
  exe_bg.py:
    provider: git
    shasum: e99eff1d539cbaa2f1f1eb3f7a9710982c4fc2753ced7b02d0d2f8fef12d12d7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/exe_bg.py
    version: 0.1.24
  file_handler.py:
    provider: git
    shasum: c0214a00860dae1b630f0ad0a8d12ff2b211e89ab9af13f6855ffd5e0d5b7a2c
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/file_handler.py
    version: 0.1.24
  file_utils.py:
    provider: git
    shasum: 489a9f99694b27e6ae5568fec970f5be5fb6364f3bbcd4774cf05de6cd02c084
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/file_utils.py
    version: 0.1.24
  getting_and_closing_app.py:
    provider: git
    shasum: da3aebc39035862d499a75c3fe8ccd9b806ec10440527eafd40b76bad23f68a7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/getting_and_closing_app.py
    version: 0.1.24
  gist_control.py:
    provider: git
    shasum: 00ab48854e103fb5e8403a7d29dc7cb97080ec2f619b8cb18b4e8fef25fcfe51
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/gist_control.py
    version: 0.1.24
  helper_test_functions.py:
    provider: git
    shasum: 8b8e8a9c1e735543d5be186a042839334edfb3f8f02a23d89991b0688d0ee6f3
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/helper_test_functions.py
    version: 0.1.24
  ipy_completer.py:
    provider: git
    shasum: c0d53880afef0d9032aec1bdb83473b50941c56401637bb9ca5fdcbd90b274d4
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/ipy_completer.py
    version: 0.1.24
  keword_matcher.py:
    provider: git
    shasum: 0ab16ce2b640cededa5a35be17de08031a3fcfa9ba24d147ca9d684a72b6812a
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/keword_matcher.py
    version: 0.1.24
  main_tool.py:
    provider: git
    shasum: 01d08ce773bebabf3cef173e5e2a28503677e06ea550ac391811aa9ac80957e7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/main_tool.py
    version: 0.1.24
  mkdocs.py:
    provider: git
    shasum: 9ebb7495af522a9389ef3755cf3321f08349d5e864b0d93d9a55a773f769680d
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/mkdocs.py
    version: 0.1.24
  notification.py:
    provider: git
    shasum: 7235d6551416f28fc3f99f728c40390431354520d8666762b39e7c0a2b3d716f
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/notification.py
    version: 0.1.24
  prox_util.py:
    provider: git
    shasum: 15531b5e59f540a0594c8e213519d6eb41102eef442aef9cb227080a74e2e16f
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/prox_util.py
    version: 0.1.24
  proxy_app.py:
    provider: git
    shasum: 24c77cfa34ec415727e32e414816220eaebae6d6db865c375578e639bef57444
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/proxy_app.py
    version: 0.1.24
  qr.py:
    provider: git
    shasum: eb54a4662c79a7679ab2ff0a63849dee420a84f2a1009fad5a5e5faabe7ef289
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/qr.py
    version: 0.1.24
  reqbuilder.py:
    provider: git
    shasum: 65283d252c4e1f7b505dc3bda647c166f2b10a74ff2c2d1a0be50578d4857b87
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/reqbuilder.py
    version: 0.1.24
  server.py:
    provider: git
    shasum: 50db7a4b16298c4c4ca5eff62a2074eb4257c4baa319f6c589bb767122abb6f7
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/server.py
    version: 0.1.24
  session.py:
    provider: git
    shasum: e17622a0a8fa0cc9115a02e52b8118baf1e647edfe2d5025b83f95d42a9fa9f5
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/session.py
    version: 0.1.24
  show_and_hide_console.py:
    provider: git
    shasum: d5d5cad1ef8f9cfcc2adcc12c936103d580971d6504ee19509a2f1f4ec7afdbe
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/show_and_hide_console.py
    version: 0.1.24
  singelton_class.py:
    provider: git
    shasum: 0410f2ce1a9d8a860cfc7d67407b2c77eb44f31fc2dbdb6a302c8623866da4b8
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/singelton_class.py
    version: 0.1.24
  state_system.py:
    provider: git
    shasum: 512385c7f4eb0b753bfb5174ca4bfa07ba654faaf98291fb71989ad2dc54c004
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/state_system.py
    version: 0.1.24
  tb_logger.py:
    provider: git
    shasum: 4609d05d21be81b065a1c8b5a65043dcc18698c6bd0051d68077a5d4cf8bde59
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/tb_logger.py
    version: 0.1.24
  tcm_p2p_cli.py:
    provider: git
    shasum: 09937055a142dbd39eb6c03f676743d6baa8b75b43c930deabf4ee7bbef76d40
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/tcm_p2p_cli.py
    version: 0.1.24
  toolbox.py:
    provider: git
    shasum: f718c574b150e7fdfb68de748026990b93589399c05a30080c8921e173af4d6d
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/toolbox.py
    version: 0.1.24
  types.py:
    provider: git
    shasum: 510eeba6347bb98d6991b407bac421ac2a99b63dd2a865d46febfcc6a79084fd
    url: https://github.com/MarkinHaus/ToolBoxV2/tree/master/toolboxv2/utils/types.py
    version: 0.1.24
