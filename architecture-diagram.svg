<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Background and styling -->
  <defs>
    <style>
      text {
        font-family: Arial, sans-serif;
        fill: #333;
      }
      .title {
        font-size: 18px;
        font-weight: bold;
      }
      .subtitle {
        font-size: 14px;
        font-weight: bold;
      }
      .label {
        font-size: 12px;
      }
      .small-label {
        font-size: 10px;
      }
      .connection {
        stroke: #555;
        stroke-width: 2;
      }
    </style>
  </defs>

  <!-- Core: ToolBoxV2 -->
  <rect x="50" y="100" width="200" height="250" rx="5" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  <text x="150" y="80" text-anchor="middle" class="title">Core</text>
  <text x="150" y="125" text-anchor="middle" class="subtitle">ToolBoxV2</text>

  <!-- Core modules -->
  <rect x="70" y="140" width="160" height="190" rx="3" fill="#fff" stroke="#333" stroke-width="1.5"/>

  <!-- UTILS -->
  <rect x="80" y="150" width="60" height="40" rx="3" fill="#e6f2ff" stroke="#333" stroke-width="1"/>
  <text x="110" y="175" text-anchor="middle" class="label">UTILS</text>

  <!-- ... -->
  <rect x="145" y="150" width="30" height="40" rx="3" fill="#f2f2f2" stroke="#333" stroke-width="1"/>
  <text x="160" y="175" text-anchor="middle" class="label">...</text>

  <!-- MODS -->
  <rect x="180" y="150" width="40" height="180" rx="3" fill="#e6f2ff" stroke="#333" stroke-width="1"/>
  <text x="200" y="165" text-anchor="middle" class="small-label">MODS</text>

  <!-- CloudM -->
  <rect x="185" y="170" width="30" height="30" rx="2" fill="#d1e7ff" stroke="#333" stroke-width="0.5"/>
  <text x="200" y="190" text-anchor="middle" class="small-label">CloudM</text>

  <!-- cli -->
  <rect x="80" y="210" width="60" height="40" rx="3" fill="#e6f2ff" stroke="#333" stroke-width="1"/>
  <text x="110" y="235" text-anchor="middle" class="label">cli</text>

  <!-- Isaa -->
  <rect x="145" y="210" width="30" height="40" rx="3" fill="#e6f2ff" stroke="#333" stroke-width="1"/>
  <text x="160" y="235" text-anchor="middle" class="small-label">Isaa</text>

  <!-- API -->
  <rect x="80" y="270" width="95" height="40" rx="3" fill="#ffe6e6" stroke="#333" stroke-width="1"/>
  <text x="127" y="295" text-anchor="middle" class="label">API</text>

  <!-- Core0 - Server -->
  <rect x="300" y="150" width="150" height="150" rx="5" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  <text x="375" y="125" text-anchor="middle" class="subtitle">Core0 - Server</text>

  <!-- TB - Server in Core0 -->
  <rect x="310" y="160" width="90" height="130" rx="3" fill="#fff" stroke="#333" stroke-width="1.5"/>
  <text x="355" y="175" text-anchor="middle" class="small-label">TB - Server</text>

  <!-- TB - instance in Core0 -->
  <rect x="320" y="185" width="70" height="95" rx="2" fill="#e6f2ff" stroke="#333" stroke-width="1"/>
  <text x="355" y="200" text-anchor="middle" class="small-label">TB - instance</text>

  <!-- App in Core0 -->
  <rect x="410" y="160" width="30" height="30" rx="2" fill="#ffe6cc" stroke="#333" stroke-width="1"/>
  <text x="425" y="180" text-anchor="middle" class="small-label">App</text>

  <!-- Web in Core0 -->
  <rect x="410" y="195" width="30" height="30" rx="2" fill="#d4e6c3" stroke="#333" stroke-width="1"/>
  <text x="425" y="215" text-anchor="middle" class="small-label">Web</text>

  <!-- Desk in Core0 -->
  <rect x="410" y="230" width="30" height="30" rx="2" fill="#d9d2e9" stroke="#333" stroke-width="1"/>
  <text x="425" y="250" text-anchor="middle" class="small-label">Desk</text>

  <!-- Blobs in Core0 -->
  <rect x="410" y="265" width="30" height="30" rx="2" fill="#cfe2f3" stroke="#333" stroke-width="1"/>
  <text x="425" y="285" text-anchor="middle" class="small-label">Blobs</text>

  <!-- TB Servers (4) -->
  <!-- Server 1 -->
  <rect x="550" y="75" width="100" height="150" rx="5" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  <text x="600" y="95" text-anchor="middle" class="small-label">TB - Server</text>

  <rect x="560" y="105" width="80" height="110" rx="3" fill="#fff" stroke="#333" stroke-width="1"/>
  <text x="600" y="120" text-anchor="middle" class="small-label">TB - instance</text>

  <rect x="570" y="125" width="30" height="20" rx="2" fill="#ffe6cc" stroke="#333" stroke-width="1"/>
  <text x="585" y="139" text-anchor="middle" class="small-label">App</text>

  <rect x="570" y="150" width="30" height="20" rx="2" fill="#d4e6c3" stroke="#333" stroke-width="1"/>
  <text x="585" y="164" text-anchor="middle" class="small-label">Web</text>

  <rect x="570" y="175" width="30" height="20" rx="2" fill="#d9d2e9" stroke="#333" stroke-width="1"/>
  <text x="585" y="189" text-anchor="middle" class="small-label">Desk</text>

  <rect x="605" y="125" width="30" height="70" rx="2" fill="#cfe2f3" stroke="#333" stroke-width="1"/>
  <text x="620" y="164" text-anchor="middle" class="small-label">Blobs</text>

  <!-- Server 2 -->
  <rect x="660" y="75" width="100" height="150" rx="5" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  <text x="710" y="95" text-anchor="middle" class="small-label">TB - Server</text>

  <rect x="670" y="105" width="80" height="110" rx="3" fill="#fff" stroke="#333" stroke-width="1"/>
  <text x="710" y="120" text-anchor="middle" class="small-label">TB - instance</text>

  <rect x="680" y="125" width="30" height="20" rx="2" fill="#ffe6cc" stroke="#333" stroke-width="1"/>
  <text x="695" y="139" text-anchor="middle" class="small-label">App</text>

  <rect x="680" y="150" width="30" height="20" rx="2" fill="#d4e6c3" stroke="#333" stroke-width="1"/>
  <text x="695" y="164" text-anchor="middle" class="small-label">Web</text>

  <rect x="680" y="175" width="30" height="20" rx="2" fill="#d9d2e9" stroke="#333" stroke-width="1"/>
  <text x="695" y="189" text-anchor="middle" class="small-label">Desk</text>

  <rect x="715" y="125" width="30" height="70" rx="2" fill="#cfe2f3" stroke="#333" stroke-width="1"/>
  <text x="730" y="164" text-anchor="middle" class="small-label">Blobs</text>

  <!-- Server 3 -->
  <rect x="550" y="275" width="100" height="150" rx="5" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  <text x="600" y="295" text-anchor="middle" class="small-label">TB - Server</text>

  <rect x="560" y="305" width="80" height="110" rx="3" fill="#fff" stroke="#333" stroke-width="1"/>
  <text x="600" y="320" text-anchor="middle" class="small-label">TB - instance</text>

  <rect x="570" y="325" width="30" height="20" rx="2" fill="#ffe6cc" stroke="#333" stroke-width="1"/>
  <text x="585" y="339" text-anchor="middle" class="small-label">App</text>

  <rect x="570" y="350" width="30" height="20" rx="2" fill="#d4e6c3" stroke="#333" stroke-width="1"/>
  <text x="585" y="364" text-anchor="middle" class="small-label">Web</text>

  <rect x="570" y="375" width="30" height="20" rx="2" fill="#d9d2e9" stroke="#333" stroke-width="1"/>
  <text x="585" y="389" text-anchor="middle" class="small-label">Desk</text>

  <rect x="605" y="325" width="30" height="70" rx="2" fill="#cfe2f3" stroke="#333" stroke-width="1"/>
  <text x="620" y="364" text-anchor="middle" class="small-label">Blobs</text>

  <!-- Server 4 -->
  <rect x="660" y="275" width="100" height="150" rx="5" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  <text x="710" y="295" text-anchor="middle" class="small-label">TB - Server</text>

  <rect x="670" y="305" width="80" height="110" rx="3" fill="#fff" stroke="#333" stroke-width="1"/>
  <text x="710" y="320" text-anchor="middle" class="small-label">TB - instance</text>

  <rect x="680" y="325" width="30" height="20" rx="2" fill="#ffe6cc" stroke="#333" stroke-width="1"/>
  <text x="695" y="339" text-anchor="middle" class="small-label">App</text>

  <rect x="680" y="350" width="30" height="20" rx="2" fill="#d4e6c3" stroke="#333" stroke-width="1"/>
  <text x="695" y="364" text-anchor="middle" class="small-label">Web</text>

  <rect x="680" y="375" width="30" height="20" rx="2" fill="#d9d2e9" stroke="#333" stroke-width="1"/>
  <text x="695" y="389" text-anchor="middle" class="small-label">Desk</text>

  <rect x="715" y="325" width="30" height="70" rx="2" fill="#cfe2f3" stroke="#333" stroke-width="1"/>
  <text x="730" y="364" text-anchor="middle" class="small-label">Blobs</text>

  <!-- Connection lines -->
  <!-- Core to Core0 -->
  <line x1="250" y1="225" x2="300" y2="225" class="connection"/>

  <!-- Core0 to TB Servers -->
  <line x1="450" y1="225" x2="500" y2="225" class="connection"/>
  <line x1="500" y1="225" x2="500" y2="150" class="connection"/>
  <line x1="500" y1="225" x2="500" y2="350" class="connection"/>
  <line x1="500" y1="150" x2="550" y2="150" class="connection"/>
  <line x1="500" y1="150" x2="550" y2="350" class="connection"/>
  <line x1="500" y1="350" x2="550" y2="150" class="connection"/>
  <line x1="500" y1="350" x2="550" y2="350" class="connection"/>
  <line x1="500" y1="150" x2="660" y2="150" class="connection"/>
  <line x1="500" y1="350" x2="660" y2="350" class="connection"/>

  <!-- Stack layers (bottom section) -->
  <rect x="150" y="420" width="500" height="70" rx="5" fill="#f9f9f9" stroke="#333" stroke-width="1"/>
  <text x="400" y="440" text-anchor="middle" class="subtitle">Technology Stack</text>

  <rect x="170" y="450" width="100" height="30" rx="3" fill="#e6f2ff" stroke="#333" stroke-width="1"/>
  <text x="220" y="470" text-anchor="middle" class="label">Python Backend</text>

  <rect x="290" y="450" width="100" height="30" rx="3" fill="#ffe6cc" stroke="#333" stroke-width="1"/>
  <text x="340" y="470" text-anchor="middle" class="label">Rust Server</text>

  <rect x="410" y="450" width="100" height="30" rx="3" fill="#d4e6c3" stroke="#333" stroke-width="1"/>
  <text x="460" y="470" text-anchor="middle" class="label">tbjs (UI Layer)</text>

  <rect x="530" y="450" width="100" height="30" rx="3" fill="#d9d2e9" stroke="#333" stroke-width="1"/>
  <text x="580" y="470" text-anchor="middle" class="label">Tauri/WebAssets</text>

  <!-- Legend -->
  <text x="50" y="440" text-anchor="middle" class="small-label">Legend:</text>
  <rect x="30" y="450" width="15" height="15" rx="2" fill="#e6f2ff" stroke="#333" stroke-width="0.5"/>
  <text x="60" y="462" text-anchor="start" class="small-label">Module</text>

  <rect x="30" y="470" width="15" height="15" rx="2" fill="#ffe6cc" stroke="#333" stroke-width="0.5"/>
  <text x="60" y="482" text-anchor="start" class="small-label">Interface</text>
</svg>
